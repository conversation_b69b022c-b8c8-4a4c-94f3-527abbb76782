package com.gtech.gvcore.service.report.impl;

import com.gtech.gvcore.common.enums.ReportExportTypeEnum;
import com.gtech.gvcore.common.enums.TransactionTypeEnum;
import com.gtech.gvcore.common.request.orderreport.CreateReportRequest;
import com.gtech.gvcore.dao.model.Cpg;
import com.gtech.gvcore.dao.model.Customer;
import com.gtech.gvcore.dao.model.Merchant;
import com.gtech.gvcore.dao.model.Outlet;
import com.gtech.gvcore.service.report.BusinessReport;
import com.gtech.gvcore.service.report.SingleReport;
import com.gtech.gvcore.service.report.base.ReportSupport;
import com.gtech.gvcore.service.report.extend.joindate.JoinDataMap;
import com.gtech.gvcore.service.report.extend.poll.PollPageHelper;
import com.gtech.gvcore.service.report.impl.bean.GcReactivatedForSummaryBean;
import com.gtech.gvcore.service.report.impl.bo.GcReactivatedBo;
import com.gtech.gvcore.service.report.impl.param.GcReactivatedQueryData;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

/**
 * @Author: gao.yuhua
 * @Date: 2022/5/11 13:57
 * @Description: Gift Card Reactivated (Block) Report Summary Implementation
 */
@Service
public class GcReactivatedForSummaryImpl extends ReportSupport
        implements BusinessReport<GcReactivatedQueryData, GcReactivatedForSummaryBean>, SingleReport {

    @Override
    public GcReactivatedQueryData builderQueryParam(CreateReportRequest reportParam) {

        GcReactivatedQueryData queryData = new GcReactivatedQueryData();

        queryData.setIssuerCodeList(StringUtils.isNotBlank(reportParam.getIssuerCode()) ? Collections.singletonList(reportParam.getIssuerCode()) : null);
        queryData.setMerchantCodeList(reportParam.getMerchantCodes());
        queryData.setOutletCodeList(reportParam.getOutletCodes());

        queryData.setTransactionDateStart(reportParam.getTransactionDateStart());
        queryData.setTransactionDateEnd(reportParam.getTransactionDateEnd());

        queryData.setCpgCodeList(reportParam.getCpgCodes());
        queryData.setInvoiceNumber(reportParam.getInvoiceNo());

        queryData.setVoucherEffectiveDateStart(reportParam.getVoucherEffectiveDateStart());
        queryData.setVoucherEffectiveDateEnd(reportParam.getVoucherEffectiveDateEnd());

        queryData.setTransactionType(TransactionTypeEnum.GIFT_CARD_REACTIVATE.getCode());

        return queryData;
    }

    @Override
    public List<GcReactivatedForSummaryBean> getExportData(GcReactivatedQueryData queryData) {

        //find
        Collection<GcReactivatedSummaryStatisticalBo> list =
                Optional.of(PollPageHelper.pollGroupNewTransactionByCodeSelect(gcReportBusinessMapper::selectGcReactivated, queryData))
                        .map(e -> e.stream()
                                .collect(Collectors.toMap(
                                        GcReactivatedSummaryStatisticalBo::groupKey,
                                        GcReactivatedSummaryStatisticalBo::convert,
                                        GcReactivatedSummaryStatisticalBo::merge))
                        ).map(Map::values)
                        .orElseGet(Collections::emptyList);

        //empty
        if (CollectionUtils.isEmpty(list)) return Collections.emptyList();

        final JoinDataMap<Cpg> cpgMap = super.getMapByCode(list, GcReactivatedSummaryStatisticalBo::getCpgCode, Cpg.class);
        final JoinDataMap<Merchant> merchantMap = super.getMapByCode(list, GcReactivatedSummaryStatisticalBo::getMerchantCode, Merchant.class);
        final JoinDataMap<Outlet> outletMap = super.getMapByCode(list, GcReactivatedSummaryStatisticalBo::getOutletCode, Outlet.class);
        final JoinDataMap<Customer> customerMap = super.getMapByCode(list, GcReactivatedSummaryStatisticalBo::getOwnerCustomer, Customer.class);

        //convert result
        return list.stream()
                .map(e -> new GcReactivatedForSummaryBean()
                        .setVoucherAmount(super.toAmount(e.getTotalAmount()))
                        .setNumberOfVouchers(String.valueOf(e.getNumberOfVouchers()))
                        .setVpg(cpgMap.findValue(e.getCpgCode()).getCpgName())
                        .setMerchantOut(outletMap.findValue(e.getOutletCode()).getOutletName())
                        .setMerchant(merchantMap.findValue(e.getMerchantCode()).getMerchantName())
                        .setCustomerName(customerMap.findValue(e.getOwnerCustomer()).getCustomerName())
                        .setReactivatedReason(e.getReactivatedReason()))
                .collect(Collectors.toList());
    }

    @Override
    public ReportExportTypeEnum exportTypeEnum() {
        return ReportExportTypeEnum.GC_REACTIVATED_SUMMARY;
    }


    @Getter
    @Setter
    @Accessors(chain = true)
    public static class GcReactivatedSummaryStatisticalBo {

        /**
         * Merchant code.
         */
        private String merchantCode;

        /**
         * Outlet code.
         */
        private String outletCode;

        /**
         * Cpg code.
         */
        private String cpgCode;

        /**
         * Number of vouchers.
         */
        private int numberOfVouchers = 1;

        /**
         * Total amount.
         */
        private BigDecimal totalAmount;

        private String customerCode;

        private String reactivatedReason;

        private String ownerCustomer;

        public static GcReactivatedSummaryStatisticalBo convert(GcReactivatedBo reactivatedBo) {

            return new GcReactivatedSummaryStatisticalBo()
                    .setMerchantCode(reactivatedBo.getMerchantCode())
                    .setOutletCode(reactivatedBo.getOutletCode())
                    .setCpgCode(reactivatedBo.getCpgCode())
                    .setTotalAmount(reactivatedBo.getDenomination())
                    .setCustomerCode(reactivatedBo.getCustomerCode())
                    .setReactivatedReason(reactivatedBo.getUnblockReason())
                    .setOwnerCustomer(reactivatedBo.getOwnerCustomer());
        }

        public GcReactivatedSummaryStatisticalBo merge(GcReactivatedSummaryStatisticalBo bo) {

            this.numberOfVouchers += bo.getNumberOfVouchers();
            this.totalAmount = this.totalAmount.add(bo.getTotalAmount());

            return this;
        }

        public static String groupKey(GcReactivatedBo reactivatedBo) {

            return StringUtils.join(",", reactivatedBo.getMerchantCode(), reactivatedBo.getOutletCode(),
                    reactivatedBo.getCpgCode(), reactivatedBo.getOwnerCustomer(), reactivatedBo.getUnblockReason());
        }

    }
}
