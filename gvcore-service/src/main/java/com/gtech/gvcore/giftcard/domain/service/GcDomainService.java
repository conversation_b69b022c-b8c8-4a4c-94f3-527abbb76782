package com.gtech.gvcore.giftcard.domain.service;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.gtech.commons.exception.GTechBaseException;
import com.gtech.commons.page.PageData;
import com.gtech.commons.utils.BeanCopyUtils;
import com.gtech.commons.utils.DateUtil;
import com.gtech.gvcore.common.enums.ResultErrorCodeEnum;
import com.gtech.gvcore.common.request.base.PageBean;
import com.gtech.gvcore.common.request.gcapi.GcIssuanceRequest;
import com.gtech.gvcore.common.request.gcapi.NewBatchCloseRequest;
import com.gtech.gvcore.common.request.voucher.SendVoucherRequest;
import com.gtech.gvcore.common.response.gc.BatchCloseResponse;
import com.gtech.gvcore.common.response.v3.Holder;
import com.gtech.gvcore.common.response.voucherbatch.ExportDigitalVoucherResponse;
import com.gtech.gvcore.dao.model.CustomerOrder;
import com.gtech.gvcore.giftcard.application.dto.*;
import com.gtech.gvcore.giftcard.domain.model.*;
import com.gtech.gvcore.giftcard.domain.repository.*;
import com.gtech.gvcore.giftcard.masterdata.gcpg.dto.GcCpgDTO;
import com.gtech.gvcore.giftcard.masterdata.gcpg.service.GcCpgService;
import com.gtech.gvcore.giftcard.service.CardNumberService;
import com.gtech.gvcore.helper.GvCodeHelper;
import com.gtech.gvcore.helper.VoucherNumberHelper;
import lombok.Data;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.*;
import java.util.stream.Collectors;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import com.gtech.gvcore.giftcard.domain.repository.GiftCardRepository;
import com.gtech.gvcore.giftcard.domain.model.GiftCard;

/**
 * 礼品卡领域服务
 */
@Service
public class GcDomainService {

    private static final Logger log = LoggerFactory.getLogger(GcDomainService.class);

    @Autowired
    private GiftCardRepository giftCardRepository;
    
    @Autowired
    private GcActivationRepository activationRecordRepository;

    
    @Autowired
    private GcCancelSalesRepository salesCancelRepository;
    
    @Autowired
    private GcRedemptionRepository redemptionRecordRepository;
    
    @Autowired
    private GcBalanceAdjustmentRepository balanceAdjustmentRepository;
    
    @Autowired
    private CardNumberGenerator cardNumberGenerator;
    
    @Autowired
    private BarcodeConverter barcodeConverter;

    @Autowired
    private GcCpgService gcCpgService;
    
    @Autowired
    private CardNumberService cardNumberService;

    @Autowired
    private VoucherNumberHelper voucherNumberHelper;

    @Autowired
    private GiftCardHolderRepository giftCardHolderRepository;

    @Autowired
    private RedisTemplate redisTemplate;
    

    

    
    /**
     * 销售礼品卡
     */
    @Transactional
    public int sellGiftCard(GcSalesDTO record) {
        GiftCard giftCard = new GiftCard();
        giftCard.setStatus(GcCardStatus.PURCHASED);
        giftCard.setSalesOutlet(record.getOutletCode());
        giftCard.setSalesTime(record.getSalesTime());
        giftCard.setOwnerCustomer(record.getCustomerCode());
        return giftCardRepository.updateCardByCardNumbers(giftCard, record.getCardNumber());
    }

    /**
     * 删除指定批次卡
     */
    public int deleteGiftCards(String batchCode) {
       return giftCardRepository.deleteFromOrder(batchCode);
    }


    public GcHolder getGiftCardHolder(String cardNumber) {
        return giftCardHolderRepository.getByGiftCardNumber(cardNumber);
    }


    public List<GcHolder> getHolderGiftCardList(String holder) {
        return giftCardHolderRepository.getHolderGiftCardList(holder);
    }

    public List<GiftCard> getCustomerCardList(String customerCode,List<String> cardNumbers) {
        return giftCardRepository.getCustomerCardList(customerCode, cardNumbers);
    }

    public PageData<GiftCard> getCustomerCardListWithPagination(String customerCode, List<String> cardNumbers, PageBean pageBean) {
        return giftCardRepository.getCustomerCardListWithPagination(customerCode, cardNumbers, pageBean);
    }


    /**
     * 根据卡号查找礼品卡
     */
    public GiftCard findByCardNumber(String cardNumber) {
        return giftCardRepository.findByCardNumber(cardNumber)
            .orElseThrow(() -> new IllegalArgumentException("礼品卡不存在"));
    }


    public List<GiftCard> findByCardNumber(List<String> cardNumber) {
        return giftCardRepository.findByCardNumber(cardNumber)
                .orElseThrow(() -> new IllegalArgumentException("礼品卡不存在"));
    }

    /**
     * 根据卡号查找礼品卡
     */
    public List<GiftCard> findByBatchCode(String batchCode) {
        return giftCardRepository.findByBatchCode(batchCode)
                .orElseThrow(() -> new IllegalArgumentException("礼品卡不存在"));
    }

    /**
     * 根据卡号查找礼品卡
     */
    public PageData<ExportGiftCardDto> findByBatchCode(String batchCode, PageBean page,String invoiceNumber, String activeUrl) {
        PageData<GiftCard> byBatchCode = giftCardRepository.findByBatchCode(batchCode, page);
        Map<String, GiftCard> giftCardMap = byBatchCode.getList().stream().collect(Collectors.toMap(x -> x.getCardNumber(), x -> x));
        List<ExportGiftCardDto> exportGiftCardDtos = BeanCopyUtils.jsonCopyList(byBatchCode.getList(), ExportGiftCardDto.class);
        exportGiftCardDtos.forEach(exportGiftCardDto -> {
            exportGiftCardDto.setInvoiceNumber(invoiceNumber);
            exportGiftCardDto.setActivationUrl(activeUrl + exportGiftCardDto.getActivationCode());
            exportGiftCardDto.setExpiryTime(DateUtil.format(giftCardMap.get(exportGiftCardDto.getCardNumber()).getExpiryTime(),DateUtil.FORMAT_YYYYMMDDHHMISS));

        });
        return new PageData<>(exportGiftCardDtos, byBatchCode.getTotal());
    }
    
    /**
     * 根据条形码查找礼品卡
     */
    public GiftCard findByBarcode(String barcode) {
        // 从条形码提取卡号
        String cardNumber = GiftCard.extractCardNumberFromBarcode(barcode, barcodeConverter);
        return findByCardNumber(cardNumber);
    }
    


    public GiftCardDTO getGiftCardInfo(String cardNumber) {
        GiftCard giftCard = giftCardRepository.findByCardNumber(cardNumber)
                .orElseThrow(() -> new IllegalArgumentException("礼品卡不存在: " + cardNumber));
        
        return toGiftCardDTO(giftCard);
    }

    /**
     * 转换为GiftCardDTO
     */
    private GiftCardDTO toGiftCardDTO(GiftCard giftCard) {
        GiftCardDTO dto = new GiftCardDTO();
        dto.setCardNumber(giftCard.getCardNumber());
        dto.setStatus(giftCard.getStatus().name());
        dto.setBalance(giftCard.getBalance());
        dto.setDenomination(giftCard.getDenomination());
        dto.setActivationCode(giftCard.getActivationCode());
        dto.setPinCode(giftCard.getPinCode());
        dto.setActivationDeadline(giftCard.getActivationDeadline());
        dto.setActivationExtensionCount(giftCard.getActivationExtensionCount());
        dto.setActivationTime(giftCard.getActivationTime());
        dto.setExpiryTime(giftCard.getExpiryTime());
        return dto;
    }
    
    /**
     * 批量创建礼品卡
     * 使用事务确保原子性，采用批量插入提高性能
     * 处理卡号重复问题，确保生成的卡号唯一，支持自动纠错
     *
     * @param cpgCode CPG编码
     * @param denomination 面额
     * @param quantity 数量
     * @param issuerCode 发行方编码
     * @return 生成的礼品卡卡号列表
     */
    @Transactional
    public List<String> batchCreateGiftCards(String batchId,String cpgCode, BigDecimal denomination,
                                           int quantity, String issuerCode) {
        // 记录起始时间，用于性能监控
        long startTime = System.currentTimeMillis();
        log.info("开始批量创建礼品卡: CPG={}, 数量={}, 发行方={}", cpgCode, quantity, issuerCode);
        
        // 1. 验证参数
        if (!isValidGiftCardRequest(cpgCode, denomination, quantity, issuerCode)) {
            log.error("礼品卡创建请求参数无效");
            return Collections.emptyList();
        }
        
        // 2. 获取礼品卡属性（激活期限和过期时间）
        GiftCardTimingInfo timingInfo = resolveCardTimingInfo(cpgCode);
        if (!timingInfo.isValid()) {
            log.error("无法获取有效的卡片时间属性: {}", timingInfo.getErrorMessage());
            return Collections.emptyList();
        }
        
        // 3. 生成卡号
        List<String> cardNumbers = generateCardNumbers(cpgCode, quantity);
        if (cardNumbers.isEmpty()) {
            log.error("未能生成任何卡号，创建礼品卡失败");
            throw new  GTechBaseException(ResultErrorCodeEnum.GENERAL_ERROR.code(), ResultErrorCodeEnum.GENERAL_ERROR.desc());
        }
        
        // 4. 创建礼品卡实体并批量保存
        BatchSaveResult saveResult = createAndSaveGiftCards(batchId,
            cardNumbers, cpgCode, denomination, issuerCode, timingInfo);
        
        // 5. 记录结果状态
        long duration = System.currentTimeMillis() - startTime;
        if (saveResult.getStatus() == BatchSaveStatus.COMPLETE) {
            log.info("批量创建礼品卡成功: 数量={}, 耗时={}ms", saveResult.getSuccessfulCardNumbers().size(), duration);
        } else {
            log.warn("批量创建礼品卡部分完成: 请求={}, 成功={}, 耗时={}ms", 
                    quantity, saveResult.getSuccessfulCardNumbers().size(), duration);
        }
        
        return saveResult.getSuccessfulCardNumbers();
    }
    
    /**
     * 验证礼品卡创建请求参数
     */
    private boolean isValidGiftCardRequest(String cpgCode, BigDecimal denomination, 
                                          int quantity, String issuerCode) {
        if (cpgCode == null || cpgCode.isEmpty()) {
            log.error("CPG编码不能为空");
            return false;
        }
        
        if (denomination == null || denomination.compareTo(BigDecimal.ZERO) <= 0) {
            log.error("面额必须大于0");
            return false;
        }
        
        if (quantity <= 0) {
            log.error("创建数量必须大于0");
            return false;
        }
        
        if (issuerCode == null || issuerCode.isEmpty()) {
            log.error("发行方编码不能为空");
            return false;
        }
        
        return true;
    }
    
    /**
     * 获取礼品卡时间属性信息
     */
    private GiftCardTimingInfo resolveCardTimingInfo(String cpgCode) {
        GiftCardTimingInfo timingInfo = new GiftCardTimingInfo();
        
        try {
            GcCpgDTO cpg = gcCpgService.getCpg(cpgCode);
            // 获取激活期限
            Date activationDeadlineDate = gcCpgService.calculateActivationDeadline(cpgCode);
            if (activationDeadlineDate == null) {
                timingInfo.setValid(false);
                timingInfo.setErrorMessage("无法获取CPG激活期限信息: " + cpgCode);
                return timingInfo;
            }
            
            // 获取过期时间 初始化过期时间为 激活期 + 宽限期
            Date expiryDate = gcCpgService.calculateActivationGraceDate(cpgCode);
            if (expiryDate == null) {
                timingInfo.setValid(false);
                timingInfo.setErrorMessage("无法获取CPG有效期信息: " + cpgCode);
                return timingInfo;
            }
            

            timingInfo.setActivationDeadline(activationDeadlineDate);
            timingInfo.setActivationGracePeriod(cpg.getActivationGracePeriod());
            timingInfo.setEffectivePeriod(cpg.getEffectivePeriod());
            timingInfo.setExpiryTime(expiryDate);
            
        } catch (Exception e) {
            log.error("获取礼品卡时间属性异常", e);
            timingInfo.setValid(false);
            timingInfo.setErrorMessage("获取时间属性异常: " + e.getMessage());
            return timingInfo;
        }
        
        return timingInfo;
    }
    
    /**
     * 生成卡号
     */
    private List<String> generateCardNumbers(String cpgCode, int quantity) {
        try {
            List<String> cardNumbers = cardNumberService.batchGenerateCardNumbers(quantity, cpgCode);
            
            if (cardNumbers.size() < quantity) {
                log.warn("生成的卡号数量少于请求数量: 请求={}, 实际={}", quantity, cardNumbers.size());
            }
            
            return cardNumbers;
        } catch (Exception e) {
            log.error("生成卡号异常", e);
            return Collections.emptyList();
        }
    }
    
    /**
     * 创建并保存礼品卡
     */
    private BatchSaveResult createAndSaveGiftCards(String batchId,
            List<String> cardNumbers, 
            String cpgCode, 
            BigDecimal denomination,
            String issuerCode,
            GiftCardTimingInfo timingInfo) {
        
        BatchSaveResult result = new BatchSaveResult();
        result.setTotalRequested(cardNumbers.size());
        
        // 1. 创建礼品卡对象
        List<GiftCard> giftCards = createGiftCardEntities(batchId,
            cardNumbers, cpgCode, denomination, issuerCode, timingInfo);
        
        // 2. 批量保存礼品卡（包含重试和错误处理）
        return saveBatchWithRetry(batchId,giftCards, cpgCode, denomination, issuerCode, timingInfo);
    }
    
    /**
     * 创建礼品卡实体列表
     */
    private List<GiftCard> createGiftCardEntities(
            String batchId,
            List<String> cardNumbers,
            String cpgCode,
            BigDecimal denomination,
            String issuerCode,
            GiftCardTimingInfo timingInfo) {
        
        return cardNumbers.stream().map(cardNumber -> {
            GiftCard giftCard = new GiftCard();
            giftCard.setCpgCode(cpgCode);
            giftCard.setCardBatchCode(batchId);
            giftCard.setDenomination(denomination);
            giftCard.setActivationDeadline(timingInfo.getActivationDeadline());
            giftCard.setCardNumber(cardNumber);
            giftCard.setIssuerCode(issuerCode);
            giftCard.setExpiryTime(timingInfo.getExpiryTime());
            giftCard.setBarCode(generateBarcode(cardNumber)); //TODO 创建时是否需要生成条形码
            giftCard.setActivationCode(ActivationCodeGenerator.generate());
            giftCard.setPinCode(voucherNumberHelper.pinCode());
            giftCard.setStatus(GcCardStatus.PURCHASED);
            giftCard.setActivationGracePeriod(timingInfo.getActivationGracePeriod());
            giftCard.setEffectivePeriod(timingInfo.getEffectivePeriod());
            return giftCard;
        }).collect(Collectors.toList());
    }
    
    /**
     * 使用重试机制批量保存礼品卡
     */
    private BatchSaveResult saveBatchWithRetry(
            String batchId,
            List<GiftCard> giftCards,
            String cpgCode,
            BigDecimal denomination,
            String issuerCode,
            GiftCardTimingInfo timingInfo) {
        
        BatchSaveResult result = new BatchSaveResult();
        Set<String> savedCardNumbers = new HashSet<>();
        List<GiftCard> remainingCards = new ArrayList<>(giftCards);
        
        // 最多重试次数
        int maxRetries = 2;
        int retryCount = 0;
        
        while (!remainingCards.isEmpty() && retryCount <= maxRetries) {
            try {
                // 尝试批量保存当前剩余的全部卡片
                giftCardRepository.batchSave(remainingCards);
                
                // 保存成功，记录已保存的卡号
                savedCardNumbers.addAll(remainingCards.stream().map(GiftCard::getCardNumber).collect(Collectors.toList()));
                remainingCards.clear();
                
                log.info("批量保存礼品卡成功, 本批次数量: {}", giftCards.size());
                
            } catch (Exception e) {
                retryCount++;
                log.warn("批量保存礼品卡异常，尝试次数: {}, 错误: {}", retryCount, e.getMessage());
                
                // 处理重复键异常
                if (isDuplicateKeyException(e)) {
                    handleDuplicateCardNumbers(batchId,
                        remainingCards, savedCardNumbers, cpgCode, 
                        denomination, issuerCode, timingInfo);
                } else if (retryCount < maxRetries) {
                    // 其他异常尝试分批保存
                    tryPartialBatchSave(remainingCards, savedCardNumbers);
                } else {
                    // 达到最大重试次数，终止
                    log.error("达到最大重试次数，终止批量保存");
                    break;
                }
            }
        }
        
        // 设置结果状态
        result.setSuccessfulCardNumbers(new ArrayList<>(savedCardNumbers));
        result.setStatus(getResultStatus(giftCards.size(), savedCardNumbers.size()));
        
        return result;
    }
    
    /**
     * 判断是否为重复键异常
     */
    private boolean isDuplicateKeyException(Exception e) {
        String message = e.getMessage();
        return message != null && (
            message.contains("Duplicate entry") || 
            message.contains("duplicate key") ||
            message.contains("UniqueConstraintViolation")
        );
    }
    
    /**
     * 处理重复卡号情况
     */
    private void handleDuplicateCardNumbers(
            String batchId,
            List<GiftCard> remainingCards,
            Set<String> savedCardNumbers,
            String cpgCode,
            BigDecimal denomination,
            String issuerCode,
            GiftCardTimingInfo timingInfo) {
        
        // 1. 找出重复的卡号
        List<String> duplicateCardNumbers = identifyDuplicateCardNumbers(remainingCards);
        
        if (!duplicateCardNumbers.isEmpty()) {
            log.info("发现{}个重复卡号，重新生成替代卡号", duplicateCardNumbers.size());
            
            // 2. 从待保存列表中移除重复卡号对应的礼品卡
            removeCardsWithDuplicateNumbers(remainingCards, duplicateCardNumbers);
            
            // 3. 重新生成替代卡号
            List<String> newCardNumbers = cardNumberService.regenerateDuplicateCardNumbers(
                duplicateCardNumbers, cpgCode);
            
            // 4. 使用新卡号创建新的礼品卡
            if (!newCardNumbers.isEmpty()) {
                List<GiftCard> newCards = createGiftCardEntities(batchId,
                    newCardNumbers, cpgCode, denomination, issuerCode, timingInfo);
                
                // 5. 添加到待保存列表
                remainingCards.addAll(newCards);
            }
        } else {
            // 无法确定哪些是重复的，切换到分批保存策略
            log.info("无法确定具体重复卡号，切换到分批保存策略");
            tryPartialBatchSave(remainingCards, savedCardNumbers);
        }
    }
    
    /**
     * 从礼品卡列表中移除具有重复卡号的卡片
     */
    private void removeCardsWithDuplicateNumbers(List<GiftCard> cards, List<String> duplicateNumbers) {
        Set<String> duplicateSet = new HashSet<>(duplicateNumbers);
        Iterator<GiftCard> iterator = cards.iterator();
        
        while (iterator.hasNext()) {
            GiftCard card = iterator.next();
            if (duplicateSet.contains(card.getCardNumber())) {
                iterator.remove();
            }
        }
    }
    
    /**
     * 识别重复的卡号
     */
    private List<String> identifyDuplicateCardNumbers(List<GiftCard> cards) {
        List<String> duplicates = new ArrayList<>();
        for (GiftCard card : cards) {
            if (giftCardRepository.existsByCardNumber(card.getCardNumber())) {
                duplicates.add(card.getCardNumber());
            }
        }
        return duplicates;
    }
    
    /**
     * 尝试分批保存礼品卡
     */
    private void tryPartialBatchSave(List<GiftCard> remainingCards, Set<String> savedCardNumbers) {
        // 如果剩余卡片过多，尝试保存一半
        if (remainingCards.size() > 10) {
            int halfSize = remainingCards.size() / 2;
            List<GiftCard> firstHalf = new ArrayList<>(remainingCards.subList(0, halfSize));
            
            try {
                // 尝试保存一半
                giftCardRepository.batchSave(firstHalf);
                
                // 保存成功，从待保存列表中移除，添加到已保存结果中
                firstHalf.forEach(card -> savedCardNumbers.add(card.getCardNumber()));
                remainingCards.subList(0, halfSize).clear();
                
                log.info("成功保存一半礼品卡: {}", halfSize);
            } catch (Exception ex) {
                log.warn("保存一半礼品卡失败: {}", ex.getMessage());
                
                // 如果一半还是太多，尝试逐个保存
                if (halfSize > 10) {
                    saveOneByOne(new ArrayList<>(firstHalf), savedCardNumbers);
                    // 移除已处理的卡片
                    remainingCards.subList(0, halfSize).clear();
                }
            }
        } else {
            // 剩余卡片较少，直接逐个保存
            saveOneByOne(remainingCards, savedCardNumbers);
            remainingCards.clear();
        }
    }
    
    /**
     * 逐个保存礼品卡
     */
    private void saveOneByOne(List<GiftCard> cards, Set<String> savedCardNumbers) {
        int savedCount = 0;
        for (GiftCard card : cards) {
            try {
                giftCardRepository.save(card);
                savedCardNumbers.add(card.getCardNumber());
                savedCount++;
            } catch (Exception e) {
                log.warn("单个保存礼品卡失败, 卡号: {}, 错误: {}", card.getCardNumber(), e.getMessage());
                throw e;
            }
        }
        log.info("逐个保存完成: 尝试={}, 成功={}", cards.size(), savedCount);
    }
    
    /**
     * 根据保存结果确定状态
     */
    private BatchSaveStatus getResultStatus(int totalRequested, int totalSaved) {
        if (totalSaved == 0) {
            return BatchSaveStatus.FAILED;
        } else if (totalSaved < totalRequested) {
            return BatchSaveStatus.PARTIAL;
        } else {
            return BatchSaveStatus.COMPLETE;
        }
    }

    public void cancelRelease(CustomerOrder customerOrder) {

        giftCardRepository.destroyFromOrder(customerOrder.getCustomerOrderCode());

    }

    public int update(GiftCard giftCard, BigDecimal oldBalance) {
       return giftCardRepository.updateBalance(giftCard,oldBalance);
    }

    public int update(GiftCard giftCard) {
        return giftCardRepository.updateCard(giftCard);
    }


    public int update(List<GiftCard> giftCard, Map<String,BigDecimal> oldBalance) {
        return giftCardRepository.updateBalance(giftCard,oldBalance);
    }

    public int bindCardholder(GcHolder holder) {

        return giftCardHolderRepository.bindCardholder(holder);
    }

    /**
     * 礼品卡批量保存状态
     */
    private enum BatchSaveStatus {
        COMPLETE("完成"),
        PARTIAL("部分完成"),
        FAILED("失败");
        
        private final String description;
        
        BatchSaveStatus(String description) {
            this.description = description;
        }
        
        public String getDescription() {
            return description;
        }
    }
    
    /**
     * 礼品卡批量保存结果
     */
    private static class BatchSaveResult {
        private List<String> successfulCardNumbers = new ArrayList<>();
        private BatchSaveStatus status = BatchSaveStatus.FAILED;
        private int totalRequested;
        
        public List<String> getSuccessfulCardNumbers() {
            return successfulCardNumbers;
        }
        
        public void setSuccessfulCardNumbers(List<String> successfulCardNumbers) {
            this.successfulCardNumbers = successfulCardNumbers;
        }
        
        public BatchSaveStatus getStatus() {
            return status;
        }
        
        public void setStatus(BatchSaveStatus status) {
            this.status = status;
        }
        
        public int getTotalRequested() {
            return totalRequested;
        }
        
        public void setTotalRequested(int totalRequested) {
            this.totalRequested = totalRequested;
        }
        
        public int getSuccessRate() {
            return totalRequested > 0 ? 
                (successfulCardNumbers.size() * 100 / totalRequested) : 0;
        }
    }
    
    /**
     * 礼品卡时间属性信息
     */
    @Data
    private static class GiftCardTimingInfo {
        private Date activationDeadline;
        private Date expiryTime;
        private boolean valid = true;
        private String errorMessage;
        private String activationGracePeriod;
        private String effectivePeriod;


    }

    /**
     * 生成条形码
     */
    private String generateBarcode(String cardNumber) {
        try {
            return barcodeConverter.toBarcode(cardNumber);
        } catch (Exception e) {
            log.error("生成条形码异常: {}", e.getMessage());
            return cardNumber; // 失败时使用卡号作为条形码
        }
    }

    /**
     * 查询所有已存在的卡号
     * 用于系统初始化时预加载卡号到Redis缓存中
     *
     * @return 所有已存在的卡号集合
     */
    public Set<String> findAllCardNumbers() {
        log.info("查询所有已存在的礼品卡卡号");
        try {
            return giftCardRepository.findAllCardNumbers();
        } catch (Exception e) {
            log.error("查询所有礼品卡卡号失败: {}", e.getMessage(), e);
            return new HashSet<>();
        }
    }


} 