package com.gtech.gvcore.service.report.impl.bo;

import com.gtech.commons.utils.ConvertUtils;
import com.gtech.commons.utils.DateUtil;
import com.gtech.gvcore.dao.model.UserAccount;
import com.gtech.gvcore.service.report.extend.joindate.JoinDataMap;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;
import org.apache.commons.lang3.StringUtils;

import java.math.BigDecimal;
import java.util.Date;

/**
 * @ClassName GcTransactionDetailedBo
 * @Description Gift Card Transaction Detailed Business Object
 * <AUTHOR>
 * @Date 2025-06-18
 * @Version V1.0
 */
@Setter
@Getter
@Accessors(chain = true)
public class GcTransactionDetailedBo {

    /**
     * Transaction type
     */
    private String transactionType;

    /**
     * Outlet code
     */
    private String outletCode;

    /**
     * Card number
     */
    private String cardNumber;

    /**
     * Create user
     */
    private String createUser;

    /**
     * POS code
     */
    private String posCode;

    /**
     * Merchant code
     */
    private String merchantCode;

    /**
     * Batch code
     */
    private String batchCode;

    /**
     * Login source
     */
    private String loginSource;

    /**
     * Transaction amount
     */
    private BigDecimal transactionAmount;

    /**
     * Balance before transaction
     */
    private BigDecimal balanceBefore;

    /**
     * Balance after transaction
     */
    private BigDecimal balanceAfter;

    /**
     * Actual outlet
     */
    private String actualOutlet;

    /**
     * Forwarding entity id
     */
    private String forwardingEntityId;

    /**
     * Response message
     */
    private String responseMessage;

    /**
     * Transaction mode
     */
    private String transactionMode;

    /**
     * Customer salutation
     */
    private String customerSalutation;

    /**
     * Customer first name
     */
    private String customerFirstName;

    /**
     * Customer last name
     */
    private String customerLastName;

    /**
     * Mobile
     */
    private String mobile;

    /**
     * Invoice number
     */
    private String invoiceNumber;

    /**
     * Other input parameter
     */
    private String otherInputParameter;

    /**
     * Transaction date string
     */
    private String transactionDate;

    /**
     * Transaction reference number
     */
    private String transactionReferenceNumber;

    /**
     * Terminal ID
     */
    private String terminalId;

    /**
     * Get transaction date as Date object
     */
    public Date getTransactionDate() {
        return DateUtil.parseDate(transactionDate, DateUtil.FORMAT_YYYYMMDDHHMISS);
    }

    /**
     * Get create user full name
     */
    public String getCreateUser(JoinDataMap<UserAccount> userAccountMap) {
        UserAccount userAccount = userAccountMap.findValue(createUser);
        if (userAccount == null) {
            return StringUtils.EMPTY;
        }
        return ConvertUtils.toString(userAccount.getFirstName(), StringUtils.EMPTY) + " " + 
               ConvertUtils.toString(userAccount.getLastName(), StringUtils.EMPTY);
    }
}
