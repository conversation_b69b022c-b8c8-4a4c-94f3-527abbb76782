<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.gtech.gvcore.dao.mapper.GcReportBusinessMapper">

    <select id="gcSalesReport" parameterType="com.gtech.gvcore.service.report.impl.param.SalesDetailedQueryData"
            resultType="com.gtech.gvcore.service.report.impl.bo.GcSalesBo">
        SELECT
        sales.card_number as voucher_code,
        sales.sales_code as transaction_code,
        DATE_FORMAT(sales.sales_time, '%Y-%m-%d %H:%i:%S') AS transaction_date,
        sales.merchant_code,
        sales.outlet_code,
        sales.invoice_number,
        sales.cpg_code,
        card.denomination,
        sales.notes,
        sales.approval_code
        FROM
        gc_sales sales
        LEFT JOIN gc_gift_card card ON sales.card_number = card.card_number
        WHERE
        card.management_status != 'DESTROY'

        <if test="transactionDateStart != null and transactionDateEnd != null ">
            AND (sales.sales_time BETWEEN #{transactionDateStart} AND #{transactionDateEnd})
        </if>
        <if test="issuerCode != null and issuerCode.length() != 0">AND card.issuer_code = #{issuerCode}</if>
        <if test="merchantCodeList != null and merchantCodeList.size() != 0">
            AND sales.merchant_code IN
            <foreach collection="merchantCodeList" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="outletCodeList != null and outletCodeList.size() != 0">
            AND sales.outlet_code IN
            <foreach collection="outletCodeList" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="cpgCodeList != null and cpgCodeList.size() != 0">
            AND sales.cpg_code IN
            <foreach collection="cpgCodeList" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="invoiceNumber != null and invoiceNumber.length() != 0">AND sales.invoice_number = #{invoiceNumber}
        </if>
        order by sales.sales_time, sales.id
    </select>

    <select id="selectGcRedemption" parameterType="com.gtech.gvcore.service.report.impl.param.RedemptionQueryData"
            resultType="com.gtech.gvcore.service.report.impl.bo.GcRedemptionBo">
        SELECT
        DATE_FORMAT(t.redemption_time, '%Y-%m-%d %H:%i:%S') AS transaction_date,
        t.redemption_code AS transaction_code,
        t.merchant_code,
        t.outlet_code,
        t.invoice_number,
        t.amount,
        t.denomination,
        t.cpg_code,
        t.card_number,
        t.notes,
        balance_after,
        balance_before
        FROM
        gc_redemption t
        WHERE
        t.`status` = 'REDEEMED'

        <if test="transactionDateStart != null and transactionDateEnd != null ">
            AND (t.redemption_time BETWEEN #{transactionDateStart} AND #{transactionDateEnd})
        </if>
        <if test="invoiceNumber != null and invoiceNumber.trim().length() != 0 ">AND t.invoice_number =
            #{invoiceNumber}
        </if>
        <if test="merchantCodeList != null and merchantCodeList.size() != 0 ">
            AND t.merchant_code IN
            <foreach collection="merchantCodeList" item="item" open="( " separator=", " close=") ">
                #{item}
            </foreach>
        </if>
        <if test="outletCodeList != null and outletCodeList.size() != 0 ">
            AND t.outlet_code IN
            <foreach collection="outletCodeList" item="item" open="( " separator=", " close=") ">
                #{item}
            </foreach>
        </if>
        <if test="cpgCodeList != null and cpgCodeList.size() != 0 ">
            AND t.cpg_code IN
            <foreach collection="cpgCodeList" item="item" open="( " separator=", " close=") ">
                #{item}
            </foreach>
        </if>
        <if test="voucherCodeNumStart != null ">AND t.card_number <![CDATA[ >= ]]> #{voucherCodeNumStart}</if>
        <if test="voucherCodeNumEnd != null ">AND t.card_number <![CDATA[ <= ]]> #{voucherCodeNumEnd}</if>
        order by t.redemption_time, t.id
    </select>
    <select id="selectCancelSales" parameterType="com.gtech.gvcore.service.report.impl.param.CancelSalesQueryData" resultType="com.gtech.gvcore.service.report.impl.bo.GcCancelSalesBo">
        select
        card_number,
        cancel_code AS transaction_code,
        merchant_code,
        outlet_code,
        cpg_code,
        denomination,
        DATE_FORMAT(cancel_time, '%Y-%m-%d %H:%i:%S'),
        invoice_number,
        t.owner_customer as customer_code
        from gc_cancel_sales
        <where>
            <if test="transactionDateStart != null and transactionDateEnd != null ">
                AND (cancel_time BETWEEN #{transactionDateStart} AND #{transactionDateEnd})
            </if>
            <if test="issuerCode != null and issuerCode.trim().length() != 0">AND issuer_code = #{issuerCode}</if>
            <if test="merchantCodeList != null and merchantCodeList.size() != 0">
                AND merchant_code IN
                <foreach collection="merchantCodeList" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="outletCodeList != null and outletCodeList.size() != 0">
                AND outlet_code IN
                <foreach collection="outletCodeList" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="cpgCodeList != null and cpgCodeList.size() != 0">
                AND cpg_code IN
                <foreach collection="cpgCodeList" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="invoiceNumber != null and invoiceNumber.trim().length() != 0">AND invoice_number =
                #{invoiceNumber}
            </if>
            <if test="voucherCodeNumStart != null">AND card_number <![CDATA[ >= ]]> #{voucherCodeNumStart}</if>
            <if test="voucherCodeNumEnd != null">AND card_number <![CDATA[ <= ]]> #{voucherCodeNumEnd}</if>

            <if test="customerCodeList != null and customerCodeList.size() != 0">
                AND owner_customer IN
                <foreach collection="customerCodeList" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>

        </where>
    </select>

    <select id="selectGcDeactivated" resultType="com.gtech.gvcore.service.report.impl.bo.DeactivatedBo">

        SELECT
        t.card_number as transaction_code
        , t.card_number as voucher_code
        , t.merchant_code
        , t.outlet_code
        , t.cpg_code
        , t.denomination
        , t.owner_customer as customer_code
        , t.invoice_number
        , t.block_reason
        , DATE_FORMAT(t.block_time, '%Y%m%d%H%i%S') AS transaction_date
        FROM gc_block t
        <where>
            <if test="transactionDateStart != null and transactionDateEnd != null ">
                AND (t.block_time BETWEEN #{transactionDateStart} AND #{transactionDateEnd})
            </if>
            <if test="issuerCodeList != null and issuerCodeList.size() != 0">
                AND t.issuer_code IN
                <foreach collection="issuerCodeList" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="merchantCodeList != null and merchantCodeList.size() != 0">
                AND t.merchant_code IN
                <foreach collection="merchantCodeList" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="outletCodeList != null and outletCodeList.size() != 0">
                AND t.outlet_code IN
                <foreach collection="outletCodeList" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="cpgCodeList != null and cpgCodeList.size() != 0">
                AND t.cpg_code IN
                <foreach collection="cpgCodeList" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="invoiceNumber != null and invoiceNumber.trim().length() != 0"> AND t.invoice_number = #{invoiceNumber} </if>
            <if test="voucherCodeNumStart != null"> AND t.card_number <![CDATA[ >= ]]> #{voucherCodeNumStart} </if>
            <if test="voucherCodeNumEnd != null"> AND t.card_number <![CDATA[ <= ]]> #{voucherCodeNumEnd} </if>

        </where>
    </select>

    <select id="selectGcReactivated" resultType="com.gtech.gvcore.service.report.impl.bo.GcReactivatedBo">

        SELECT
        t.card_number as transaction_code
        , t.card_number as voucher_code
        , t.merchant_code
        , t.outlet_code
        , t.cpg_code
        , t.denomination
        , t.owner_customer
        , t.invoice_number
        , t.owner_customer as customer_code
        , t.unblock_reason
        , DATE_FORMAT(t.unblock_time, '%Y%m%d%H%i%S') AS transaction_date
        FROM gc_unblock t
        <where>
            <if test="transactionDateStart != null and transactionDateEnd != null ">
                AND (t.unblock_time BETWEEN #{transactionDateStart} AND #{transactionDateEnd})
            </if>
            <if test="issuerCodeList != null and issuerCodeList.size() != 0">
                AND t.issuer_code IN
                <foreach collection="issuerCodeList" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="merchantCodeList != null and merchantCodeList.size() != 0">
                AND t.merchant_code IN
                <foreach collection="merchantCodeList" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="outletCodeList != null and outletCodeList.size() != 0">
                AND t.outlet_code IN
                <foreach collection="outletCodeList" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="cpgCodeList != null and cpgCodeList.size() != 0">
                AND t.cpg_code IN
                <foreach collection="cpgCodeList" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="invoiceNumber != null and invoiceNumber.trim().length() != 0"> AND t.invoice_number = #{invoiceNumber} </if>
            <if test="voucherCodeNumStart != null"> AND t.card_number <![CDATA[ >= ]]> #{voucherCodeNumStart} </if>
            <if test="voucherCodeNumEnd != null"> AND t.card_number <![CDATA[ <= ]]> #{voucherCodeNumEnd} </if>

        </where>
    </select>

    <select id="selectGcExpiry" resultType="com.gtech.gvcore.service.report.impl.bo.GcExpiryBo">

        SELECT t.card_number as voucher_code
        , t.sales_outlet as outlet_code
        , t.cpg_code
        , t.denomination
        , t.balance
        , t.owner_customer
        , t.status as card_status
        , DATE_FORMAT(t.expiry_time, '%Y%m%d%H%i%S') AS expiry_time
        , DATE_FORMAT(t.sales_time, '%Y%m%d%H%i%S') AS sales_time
        , DATE_FORMAT(t.activation_time, '%Y%m%d%H%i%S') AS activation_time
        FROM gc_gift_card t
        <where>
            <if test="expiryDateStart != null and expiryDateEnd != null ">
                AND (t.expiry_time BETWEEN #{expiryDateStart} AND #{expiryDateEnd})
            </if>
            <if test="salesTimeStart != null and salesTimeEnd != null ">
                AND (t.sales_time BETWEEN #{salesTimeStart} AND #{salesTimeEnd})
            </if>
            <if test="activationTimeStart != null and activationTimeEnd != null ">
                AND (t.activation_time BETWEEN #{activationTimeStart} AND #{activationTimeEnd})
            </if>
            <if test="issuerCodeList != null and issuerCodeList.size() != 0">
                AND t.issuer_code IN
                <foreach collection="issuerCodeList" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="outletCodeList != null and outletCodeList.size() != 0">
                AND t.sales_outlet IN
                <foreach collection="outletCodeList" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="cpgCodeList != null and cpgCodeList.size() != 0">
                AND t.cpg_code IN
                <foreach collection="cpgCodeList" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="customerCodeList != null and customerCodeList.size() != 0">
                AND t.owner_customer IN
                <foreach collection="customerCodeList" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="cardStatusList != null and cardStatusList.size() != 0">
                AND t.status IN
                <foreach collection="cardStatusList" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>


        </where>
    </select>

    <select id="selectGcExtendExpiry" resultType="com.gtech.gvcore.service.report.impl.bo.GcExtendExpiryBo">

        SELECT t.card_number
        , t.extension_code
        , t.outlet_code
        , t.extension_count
        , t.issuer_code
        , t.merchant_code
        , t.invoice_number
        , t.approval_code
        , t.notes
        , t.batch_number
        , t.source
        , DATE_FORMAT(t.extension_time, '%Y%m%d%H%i%S') AS extension_time
        , DATE_FORMAT(t.old_activation_deadline, '%Y%m%d%H%i%S') AS old_activation_deadline
        , DATE_FORMAT(t.new_activation_deadline, '%Y%m%d%H%i%S') AS new_activation_deadline
        , gc.cpg_code
        , gc.owner_customer
        , COALESCE(gc.balance, gc.denomination) AS amount
        , DATE_FORMAT(gc.sales_time, '%Y%m%d%H%i%S') AS sales_time
        , DATE_FORMAT(gc.activation_time, '%Y%m%d%H%i%S') AS activation_time
        FROM gc_extend_activation_period t
        LEFT JOIN gc_gift_card gc ON t.card_number = gc.card_number

        <where>
            <if test="transactionDateStart != null and transactionDateEnd != null ">
                AND (t.extension_time BETWEEN #{transactionDateStart} AND #{transactionDateEnd})
            </if>
            <if test="issuerCodeList != null and issuerCodeList.size() != 0">
                AND t.issuer_code IN
                <foreach collection="issuerCodeList" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="merchantCodeList != null and merchantCodeList.size() != 0">
                AND t.merchant_code IN
                <foreach collection="merchantCodeList" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="outletCodeList != null and outletCodeList.size() != 0">
                AND t.outlet_code IN
                <foreach collection="outletCodeList" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="cpgCodeList != null and cpgCodeList.size() != 0">
                AND gc.cpg_code IN
                <foreach collection="cpgCodeList" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="sourceList != null and sourceList.size() != 0">
                AND t.source IN
                <foreach collection="sourceList" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
        </where>
    </select>

    <!-- Gift card transaction time queries -->
    <select id="getLatestGcActivationTime" resultType="java.util.Date">
        SELECT MAX(create_time)
        FROM gc_activation
        WHERE card_number = #{cardNumber}
    </select>

    <select id="getLatestGcSalesTime" resultType="java.util.Date">
        SELECT MAX(create_time)
        FROM gc_sales
        WHERE card_number = #{cardNumber}
    </select>

    <select id="getLatestGcRedemptionTime" resultType="java.util.Date">
        SELECT MAX(create_time)
        FROM gc_redemption
        WHERE card_number = #{cardNumber}
    </select>

    <select id="getLatestGcBlockTime" resultType="java.util.Date">
        SELECT MAX(create_time)
        FROM gc_block
        WHERE card_number = #{cardNumber}
    </select>

    <select id="getLatestGcUnblockTime" resultType="java.util.Date">
        SELECT MAX(create_time)
        FROM gc_unblock
        WHERE card_number = #{cardNumber}
    </select>

    <select id="getLatestGcCancelSalesTime" resultType="java.util.Date">
        SELECT MAX(create_time)
        FROM gc_cancel_sales
        WHERE card_number = #{cardNumber}
    </select>

    <select id="getLatestGcCancelActivationTime" resultType="java.util.Date">
        SELECT MAX(create_time)
        FROM gc_cancel_activation
        WHERE card_number = #{cardNumber}
    </select>

    <select id="getLatestGcExtendActivationTime" resultType="java.util.Date">
        SELECT MAX(create_time)
        FROM gc_extend_activation_period
        WHERE card_number = #{cardNumber}
    </select>

    <!-- Get card to merchant/outlet mapping from sales records -->
    <select id="getCardToMerchantMapFromGcSales" resultType="java.util.HashMap">
        SELECT card_number as `key`, merchant_code as `value`
        FROM gc_sales
        WHERE card_number IN
        <foreach collection="cardNumbers" item="cardNumber" open="(" separator="," close=")">
            #{cardNumber}
        </foreach>
        AND merchant_code IS NOT NULL
    </select>

    <select id="getCardToOutletMapFromGcSales" resultType="java.util.HashMap">
        SELECT card_number as `key`, outlet_code as `value`
        FROM gc_sales
        WHERE card_number IN
        <foreach collection="cardNumbers" item="cardNumber" open="(" separator="," close=")">
            #{cardNumber}
        </foreach>
        AND outlet_code IS NOT NULL
    </select>

    <!-- Gift Card Transaction Detail Report -->
    <select id="gcTransactionDetailReport" parameterType="com.gtech.gvcore.service.report.impl.param.GcTransactionDetailQueryData"
            resultType="com.gtech.gvcore.service.report.impl.bo.GcTransactionDetailedBo">
        SELECT
            'ACTIVATION' as transactionType,
            ga.outlet_code as outletCode,
            ga.card_number as cardNumber,
            ga.create_user as createUser,
            ga.pos_code as posCode,
            ga.merchant_code as merchantCode,
            ga.card_batch_code as batchCode,
            ga.login_source as loginSource,
            ga.denomination as transactionAmount,
            0 as balanceBefore,
            ga.denomination as balanceAfter,
            ga.actual_outlet as actualOutlet,
            ga.forwarding_entity_id as forwardingEntityId,
            ga.response_message as responseMessage,
            ga.transaction_mode as transactionMode,
            ga.customer_salutation as customerSalutation,
            ga.customer_first_name as customerFirstName,
            ga.customer_last_name as customerLastName,
            ga.mobile as mobile,
            ga.invoice_number as invoiceNumber,
            ga.transaction_reference_number as transactionReferenceNumber,
            ga.terminal_id as terminalId,
            '' as otherInputParameter,
            DATE_FORMAT(ga.create_time, '%Y%m%d%H%i%s') as transactionDate
        FROM gc_activation ga
        WHERE 1=1
            <if test="transactionDateStart != null">
                AND ga.create_time <![CDATA[ >= ]]> #{transactionDateStart}
            </if>
            <if test="transactionDateEnd != null">
                AND ga.create_time <![CDATA[ <= ]]> #{transactionDateEnd}
            </if>
            <if test="issuerCodes != null and issuerCodes.size() > 0">
                AND ga.issuer_code IN
                <foreach collection="issuerCodes" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="merchantCodes != null and merchantCodes.size() > 0">
                AND ga.merchant_code IN
                <foreach collection="merchantCodes" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="outletCodes != null and outletCodes.size() > 0">
                AND ga.outlet_code IN
                <foreach collection="outletCodes" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="cpgCodes != null and cpgCodes.size() > 0">
                AND ga.cpg_code IN
                <foreach collection="cpgCodes" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="transactionType != null and transactionType.size() > 0 and !transactionType.contains('ACTIVATION')">
                AND 1=0
            </if>
            <if test="cardNumberStart != null and cardNumberStart.length() > 0">
                AND ga.card_number <![CDATA[ >= ]]> #{cardNumberStart}
            </if>
            <if test="cardNumberEnd != null and cardNumberEnd.length() > 0">
                AND ga.card_number <![CDATA[ <= ]]> #{cardNumberEnd}
            </if>
            <if test="purchaseOrderCardNumberList != null and purchaseOrderCardNumberList.size() > 0">
                AND ga.card_number IN
                <foreach collection="purchaseOrderCardNumberList" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="invoiceNumber != null and invoiceNumber.length() > 0">
                AND ga.invoice_number = #{invoiceNumber}
            </if>

        UNION ALL

        SELECT
            'SALES' as transactionType,
            gs.outlet_code as outletCode,
            gs.card_number as cardNumber,
            gs.create_user as createUser,
            gs.pos_code as posCode,
            gs.merchant_code as merchantCode,
            gs.card_batch_code as batchCode,
            gs.login_source as loginSource,
            gs.denomination as transactionAmount,
            0 as balanceBefore,
            gs.denomination as balanceAfter,
            gs.actual_outlet as actualOutlet,
            gs.forwarding_entity_id as forwardingEntityId,
            gs.response_message as responseMessage,
            gs.transaction_mode as transactionMode,
            gs.customer_salutation as customerSalutation,
            gs.customer_first_name as customerFirstName,
            gs.customer_last_name as customerLastName,
            gs.mobile as mobile,
            gs.invoice_number as invoiceNumber,
            gs.transaction_reference_number as transactionReferenceNumber,
            gs.terminal_id as terminalId,
            '' as otherInputParameter,
            DATE_FORMAT(gs.create_time, '%Y%m%d%H%i%s') as transactionDate
        FROM gc_sales gs
        WHERE 1=1
            <if test="transactionDateStart != null">
                AND gs.create_time <![CDATA[ >= ]]> #{transactionDateStart}
            </if>
            <if test="transactionDateEnd != null">
                AND gs.create_time <![CDATA[ <= ]]> #{transactionDateEnd}
            </if>
            <if test="issuerCodes != null and issuerCodes.size() > 0">
                AND gs.issuer_code IN
                <foreach collection="issuerCodes" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="merchantCodes != null and merchantCodes.size() > 0">
                AND gs.merchant_code IN
                <foreach collection="merchantCodes" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="outletCodes != null and outletCodes.size() > 0">
                AND gs.outlet_code IN
                <foreach collection="outletCodes" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="cpgCodes != null and cpgCodes.size() > 0">
                AND gs.cpg_code IN
                <foreach collection="cpgCodes" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="transactionType != null and transactionType.size() > 0 and !transactionType.contains('SALES')">
                AND 1=0
            </if>
            <if test="cardNumberStart != null and cardNumberStart.length() > 0">
                AND gs.card_number <![CDATA[ >= ]]> #{cardNumberStart}
            </if>
            <if test="cardNumberEnd != null and cardNumberEnd.length() > 0">
                AND gs.card_number <![CDATA[ <= ]]> #{cardNumberEnd}
            </if>
            <if test="purchaseOrderCardNumberList != null and purchaseOrderCardNumberList.size() > 0">
                AND gs.card_number IN
                <foreach collection="purchaseOrderCardNumberList" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="invoiceNumber != null and invoiceNumber.length() > 0">
                AND gs.invoice_number = #{invoiceNumber}
            </if>

        UNION ALL

        SELECT
            'REDEMPTION' as transactionType,
            gr.outlet_code as outletCode,
            gr.card_number as cardNumber,
            gr.create_user as createUser,
            gr.pos_code as posCode,
            gr.merchant_code as merchantCode,
            gr.card_batch_code as batchCode,
            gr.login_source as loginSource,
            gr.redemption_amount as transactionAmount,
            gr.balance_before as balanceBefore,
            gr.balance_after as balanceAfter,
            gr.actual_outlet as actualOutlet,
            gr.forwarding_entity_id as forwardingEntityId,
            gr.response_message as responseMessage,
            gr.transaction_mode as transactionMode,
            gr.customer_salutation as customerSalutation,
            gr.customer_first_name as customerFirstName,
            gr.customer_last_name as customerLastName,
            gr.mobile as mobile,
            gr.invoice_number as invoiceNumber,
            gr.transaction_reference_number as transactionReferenceNumber,
            gr.terminal_id as terminalId,
            '' as otherInputParameter,
            DATE_FORMAT(gr.create_time, '%Y%m%d%H%i%s') as transactionDate
        FROM gc_redemption gr
        WHERE 1=1
            <if test="transactionDateStart != null">
                AND gr.create_time <![CDATA[ >= ]]> #{transactionDateStart}
            </if>
            <if test="transactionDateEnd != null">
                AND gr.create_time <![CDATA[ <= ]]> #{transactionDateEnd}
            </if>
            <if test="issuerCodes != null and issuerCodes.size() > 0">
                AND gr.issuer_code IN
                <foreach collection="issuerCodes" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="merchantCodes != null and merchantCodes.size() > 0">
                AND gr.merchant_code IN
                <foreach collection="merchantCodes" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="outletCodes != null and outletCodes.size() > 0">
                AND gr.outlet_code IN
                <foreach collection="outletCodes" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="cpgCodes != null and cpgCodes.size() > 0">
                AND gr.cpg_code IN
                <foreach collection="cpgCodes" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="transactionType != null and transactionType.size() > 0 and !transactionType.contains('REDEMPTION')">
                AND 1=0
            </if>
            <if test="cardNumberStart != null and cardNumberStart.length() > 0">
                AND gr.card_number <![CDATA[ >= ]]> #{cardNumberStart}
            </if>
            <if test="cardNumberEnd != null and cardNumberEnd.length() > 0">
                AND gr.card_number <![CDATA[ <= ]]> #{cardNumberEnd}
            </if>
            <if test="purchaseOrderCardNumberList != null and purchaseOrderCardNumberList.size() > 0">
                AND gr.card_number IN
                <foreach collection="purchaseOrderCardNumberList" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="invoiceNumber != null and invoiceNumber.length() > 0">
                AND gr.invoice_number = #{invoiceNumber}
            </if>

        UNION ALL

        SELECT
            'BLOCK' as transactionType,
            gb.outlet_code as outletCode,
            gb.card_number as cardNumber,
            gb.create_user as createUser,
            gb.pos_code as posCode,
            gb.merchant_code as merchantCode,
            gb.card_batch_code as batchCode,
            gb.login_source as loginSource,
            0 as transactionAmount,
            gb.balance as balanceBefore,
            gb.balance as balanceAfter,
            gb.actual_outlet as actualOutlet,
            gb.forwarding_entity_id as forwardingEntityId,
            gb.response_message as responseMessage,
            gb.transaction_mode as transactionMode,
            gb.customer_salutation as customerSalutation,
            gb.customer_first_name as customerFirstName,
            gb.customer_last_name as customerLastName,
            gb.mobile as mobile,
            gb.invoice_number as invoiceNumber,
            gb.transaction_reference_number as transactionReferenceNumber,
            gb.terminal_id as terminalId,
            '' as otherInputParameter,
            DATE_FORMAT(gb.create_time, '%Y%m%d%H%i%s') as transactionDate
        FROM gc_block gb
        WHERE 1=1
            <if test="transactionDateStart != null">
                AND gb.create_time <![CDATA[ >= ]]> #{transactionDateStart}
            </if>
            <if test="transactionDateEnd != null">
                AND gb.create_time <![CDATA[ <= ]]> #{transactionDateEnd}
            </if>
            <if test="issuerCodes != null and issuerCodes.size() > 0">
                AND gb.issuer_code IN
                <foreach collection="issuerCodes" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="merchantCodes != null and merchantCodes.size() > 0">
                AND gb.merchant_code IN
                <foreach collection="merchantCodes" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="outletCodes != null and outletCodes.size() > 0">
                AND gb.outlet_code IN
                <foreach collection="outletCodes" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="cpgCodes != null and cpgCodes.size() > 0">
                AND gb.cpg_code IN
                <foreach collection="cpgCodes" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="transactionType != null and transactionType.size() > 0 and !transactionType.contains('BLOCK')">
                AND 1=0
            </if>
            <if test="cardNumberStart != null and cardNumberStart.length() > 0">
                AND gb.card_number <![CDATA[ >= ]]> #{cardNumberStart}
            </if>
            <if test="cardNumberEnd != null and cardNumberEnd.length() > 0">
                AND gb.card_number <![CDATA[ <= ]]> #{cardNumberEnd}
            </if>
            <if test="purchaseOrderCardNumberList != null and purchaseOrderCardNumberList.size() > 0">
                AND gb.card_number IN
                <foreach collection="purchaseOrderCardNumberList" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>

        UNION ALL

        SELECT
            'UNBLOCK' as transactionType,
            gub.outlet_code as outletCode,
            gub.card_number as cardNumber,
            gub.create_user as createUser,
            gub.pos_code as posCode,
            gub.merchant_code as merchantCode,
            gub.card_batch_code as batchCode,
            gub.login_source as loginSource,
            0 as transactionAmount,
            gub.balance as balanceBefore,
            gub.balance as balanceAfter,
            gub.actual_outlet as actualOutlet,
            gub.forwarding_entity_id as forwardingEntityId,
            gub.response_message as responseMessage,
            gub.transaction_mode as transactionMode,
            gub.customer_salutation as customerSalutation,
            gub.customer_first_name as customerFirstName,
            gub.customer_last_name as customerLastName,
            gub.mobile as mobile,
            gub.invoice_number as invoiceNumber,
            gub.transaction_reference_number as transactionReferenceNumber,
            gub.terminal_id as terminalId,
            '' as otherInputParameter,
            DATE_FORMAT(gub.create_time, '%Y%m%d%H%i%s') as transactionDate
        FROM gc_unblock gub
        WHERE 1=1
            <if test="transactionDateStart != null">
                AND gub.create_time <![CDATA[ >= ]]> #{transactionDateStart}
            </if>
            <if test="transactionDateEnd != null">
                AND gub.create_time <![CDATA[ <= ]]> #{transactionDateEnd}
            </if>
            <if test="issuerCodes != null and issuerCodes.size() > 0">
                AND gub.issuer_code IN
                <foreach collection="issuerCodes" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="merchantCodes != null and merchantCodes.size() > 0">
                AND gub.merchant_code IN
                <foreach collection="merchantCodes" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="outletCodes != null and outletCodes.size() > 0">
                AND gub.outlet_code IN
                <foreach collection="outletCodes" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="cpgCodes != null and cpgCodes.size() > 0">
                AND gub.cpg_code IN
                <foreach collection="cpgCodes" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="transactionType != null and transactionType.size() > 0 and !transactionType.contains('UNBLOCK')">
                AND 1=0
            </if>
            <if test="cardNumberStart != null and cardNumberStart.length() > 0">
                AND gub.card_number <![CDATA[ >= ]]> #{cardNumberStart}
            </if>
            <if test="cardNumberEnd != null and cardNumberEnd.length() > 0">
                AND gub.card_number <![CDATA[ <= ]]> #{cardNumberEnd}
            </if>
            <if test="purchaseOrderCardNumberList != null and purchaseOrderCardNumberList.size() > 0">
                AND gub.card_number IN
                <foreach collection="purchaseOrderCardNumberList" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>

        UNION ALL

        SELECT
            'CANCEL_SALES' as transactionType,
            gcs.outlet_code as outletCode,
            gcs.card_number as cardNumber,
            gcs.create_user as createUser,
            gcs.pos_code as posCode,
            gcs.merchant_code as merchantCode,
            gcs.card_batch_code as batchCode,
            gcs.login_source as loginSource,
            gcs.denomination as transactionAmount,
            gcs.balance_before as balanceBefore,
            gcs.balance_after as balanceAfter,
            gcs.actual_outlet as actualOutlet,
            gcs.forwarding_entity_id as forwardingEntityId,
            gcs.response_message as responseMessage,
            gcs.transaction_mode as transactionMode,
            gcs.customer_salutation as customerSalutation,
            gcs.customer_first_name as customerFirstName,
            gcs.customer_last_name as customerLastName,
            gcs.mobile as mobile,
            gcs.invoice_number as invoiceNumber,
            gcs.transaction_reference_number as transactionReferenceNumber,
            gcs.terminal_id as terminalId,
            '' as otherInputParameter,
            DATE_FORMAT(gcs.create_time, '%Y%m%d%H%i%s') as transactionDate
        FROM gc_cancel_sales gcs
        WHERE 1=1
            <if test="transactionDateStart != null">
                AND gcs.create_time <![CDATA[ >= ]]> #{transactionDateStart}
            </if>
            <if test="transactionDateEnd != null">
                AND gcs.create_time <![CDATA[ <= ]]> #{transactionDateEnd}
            </if>
            <if test="issuerCodes != null and issuerCodes.size() > 0">
                AND gcs.issuer_code IN
                <foreach collection="issuerCodes" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="merchantCodes != null and merchantCodes.size() > 0">
                AND gcs.merchant_code IN
                <foreach collection="merchantCodes" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="outletCodes != null and outletCodes.size() > 0">
                AND gcs.outlet_code IN
                <foreach collection="outletCodes" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="cpgCodes != null and cpgCodes.size() > 0">
                AND gcs.cpg_code IN
                <foreach collection="cpgCodes" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="transactionType != null and transactionType.size() > 0 and !transactionType.contains('CANCEL_SALES')">
                AND 1=0
            </if>
            <if test="cardNumberStart != null and cardNumberStart.length() > 0">
                AND gcs.card_number <![CDATA[ >= ]]> #{cardNumberStart}
            </if>
            <if test="cardNumberEnd != null and cardNumberEnd.length() > 0">
                AND gcs.card_number <![CDATA[ <= ]]> #{cardNumberEnd}
            </if>
            <if test="purchaseOrderCardNumberList != null and purchaseOrderCardNumberList.size() > 0">
                AND gcs.card_number IN
                <foreach collection="purchaseOrderCardNumberList" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>

        UNION ALL

        SELECT
            'CANCEL_ACTIVATION' as transactionType,
            gca.outlet_code as outletCode,
            gca.card_number as cardNumber,
            gca.create_user as createUser,
            gca.pos_code as posCode,
            gca.merchant_code as merchantCode,
            gca.card_batch_code as batchCode,
            gca.login_source as loginSource,
            gca.denomination as transactionAmount,
            gca.balance_before as balanceBefore,
            gca.balance_after as balanceAfter,
            gca.actual_outlet as actualOutlet,
            gca.forwarding_entity_id as forwardingEntityId,
            gca.response_message as responseMessage,
            gca.transaction_mode as transactionMode,
            gca.customer_salutation as customerSalutation,
            gca.customer_first_name as customerFirstName,
            gca.customer_last_name as customerLastName,
            gca.mobile as mobile,
            gca.invoice_number as invoiceNumber,
            gca.transaction_reference_number as transactionReferenceNumber,
            gca.terminal_id as terminalId,
            '' as otherInputParameter,
            DATE_FORMAT(gca.create_time, '%Y%m%d%H%i%s') as transactionDate
        FROM gc_cancel_activation gca
        WHERE 1=1
            <if test="transactionDateStart != null">
                AND gca.create_time <![CDATA[ >= ]]> #{transactionDateStart}
            </if>
            <if test="transactionDateEnd != null">
                AND gca.create_time <![CDATA[ <= ]]> #{transactionDateEnd}
            </if>
            <if test="issuerCodes != null and issuerCodes.size() > 0">
                AND gca.issuer_code IN
                <foreach collection="issuerCodes" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="merchantCodes != null and merchantCodes.size() > 0">
                AND gca.merchant_code IN
                <foreach collection="merchantCodes" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="outletCodes != null and outletCodes.size() > 0">
                AND gca.outlet_code IN
                <foreach collection="outletCodes" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="cpgCodes != null and cpgCodes.size() > 0">
                AND gca.cpg_code IN
                <foreach collection="cpgCodes" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="transactionType != null and transactionType.size() > 0 and !transactionType.contains('CANCEL_ACTIVATION')">
                AND 1=0
            </if>
            <if test="cardNumberStart != null and cardNumberStart.length() > 0">
                AND gca.card_number <![CDATA[ >= ]]> #{cardNumberStart}
            </if>
            <if test="cardNumberEnd != null and cardNumberEnd.length() > 0">
                AND gca.card_number <![CDATA[ <= ]]> #{cardNumberEnd}
            </if>
            <if test="purchaseOrderCardNumberList != null and purchaseOrderCardNumberList.size() > 0">
                AND gca.card_number IN
                <foreach collection="purchaseOrderCardNumberList" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>

        UNION ALL

        SELECT
            'EXTEND_ACTIVATION' as transactionType,
            geap.outlet_code as outletCode,
            geap.card_number as cardNumber,
            geap.create_user as createUser,
            geap.pos_code as posCode,
            geap.merchant_code as merchantCode,
            geap.card_batch_code as batchCode,
            geap.login_source as loginSource,
            0 as transactionAmount,
            geap.balance as balanceBefore,
            geap.balance as balanceAfter,
            geap.actual_outlet as actualOutlet,
            geap.forwarding_entity_id as forwardingEntityId,
            geap.response_message as responseMessage,
            geap.transaction_mode as transactionMode,
            geap.customer_salutation as customerSalutation,
            geap.customer_first_name as customerFirstName,
            geap.customer_last_name as customerLastName,
            geap.mobile as mobile,
            geap.invoice_number as invoiceNumber,
            geap.transaction_reference_number as transactionReferenceNumber,
            geap.terminal_id as terminalId,
            '' as otherInputParameter,
            DATE_FORMAT(geap.create_time, '%Y%m%d%H%i%s') as transactionDate
        FROM gc_extend_activation_period geap
        WHERE 1=1
            <if test="transactionDateStart != null">
                AND geap.create_time <![CDATA[ >= ]]> #{transactionDateStart}
            </if>
            <if test="transactionDateEnd != null">
                AND geap.create_time <![CDATA[ <= ]]> #{transactionDateEnd}
            </if>
            <if test="issuerCodes != null and issuerCodes.size() > 0">
                AND geap.issuer_code IN
                <foreach collection="issuerCodes" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="merchantCodes != null and merchantCodes.size() > 0">
                AND geap.merchant_code IN
                <foreach collection="merchantCodes" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="outletCodes != null and outletCodes.size() > 0">
                AND geap.outlet_code IN
                <foreach collection="outletCodes" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="cpgCodes != null and cpgCodes.size() > 0">
                AND geap.cpg_code IN
                <foreach collection="cpgCodes" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="transactionType != null and transactionType.size() > 0 and !transactionType.contains('EXTEND_ACTIVATION')">
                AND 1=0
            </if>
            <if test="cardNumberStart != null and cardNumberStart.length() > 0">
                AND geap.card_number <![CDATA[ >= ]]> #{cardNumberStart}
            </if>
            <if test="cardNumberEnd != null and cardNumberEnd.length() > 0">
                AND geap.card_number <![CDATA[ <= ]]> #{cardNumberEnd}
            </if>
            <if test="purchaseOrderCardNumberList != null and purchaseOrderCardNumberList.size() > 0">
                AND geap.card_number IN
                <foreach collection="purchaseOrderCardNumberList" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>

        ORDER BY transactionDate DESC
    </select>

</mapper>