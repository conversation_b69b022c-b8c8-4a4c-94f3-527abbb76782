package com.gtech.gvcore.common.response.gc;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * Gift Card Information
 */
@Data
@ApiModel(value = "GiftCardInfo", description = "Gift Card Information")
public class GiftCardInfo {

    @ApiModelProperty(value = "Item No", notes = "Item number")
    private String itemNo;

    @ApiModelProperty(value = "Bar Code Number", notes = "Bar code number")
    private String barCodeNumber;

    @ApiModelProperty(value = "Gift Card Number", notes = "16 digit gift card number")
    private String giftCardNumber;

    @ApiModelProperty(value = "Gift Card PIN", notes = "6 digit numeric PIN")
    private String giftCardPIN;

    @ApiModelProperty(value = "Currency", notes = "Currency code")
    private String currency;

    @ApiModelProperty(value = "Gift Card Status", notes = "Latest Status")
    private String giftCardStatus;

    @ApiModelProperty(value = "Gift Card Status ID", notes = "Gift card status ID")
    private String giftCardStatusId;

    @ApiModelProperty(value = "Remaining Balance", notes = "Remaining Balance for Gift card")
    private BigDecimal remainingBalance;

    @ApiModelProperty(value = "Denomination", notes = "Denomination when first time purchased gift card")
    private BigDecimal denomination;

    @ApiModelProperty(value = "Redemption Amount", notes = "Amount to be redeemed")
    @NotNull(message = "redemptionAmount can't be empty")
    private BigDecimal redemptionAmount;

    @ApiModelProperty(value = "Approval Code", notes = "Approval code")
    private String approvalCode;

    @ApiModelProperty(value = "GCPG", notes = "GCPG name")
    private String gcpg;

    @ApiModelProperty(value = "Issuer", notes = "Issuer")
    private String issuer;

    @ApiModelProperty(value = "Transaction Date", notes = "Transaction date")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd'T'HH:mm:ss")
    private Date transactionDate;

    @ApiModelProperty(value = "Article Code", notes = "MAP SAP article code (SAP mapping)")
    private String articleCode;

    @ApiModelProperty(value = "MOP Code", notes = "MAP SAP MOP code (SAP mapping)")
    private String mopCode;

    @ApiModelProperty(value = "Activation Code", notes = "Activation code")
    private String activationCode;

    @ApiModelProperty(value = "Gift Card Expiry", notes = "Gift card Activation Expiry")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd'T'HH:mm:ss")
    private Date giftCardExpiry;

    @ApiModelProperty(value = "Balance Expiry", notes = "Gift card Balance Expiry (after Activation)")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd'T'HH:mm:ss")
    private Date balanceExpiry;

    @ApiModelProperty(value = "Inquiry Date", notes = "Inquiry date")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd'T'HH:mm:ss")
    private Date inquiryDate;

    @ApiModelProperty(value = "Holder", notes = "Gift card holder")
    private String holder;

    @ApiModelProperty(value = "Gift Card History", notes = "Details of Gift Card Transaction")
    private List<GiftCardHistory> giftCardHistory;


    /**
     * Gift Card Transaction History
     */
    @Data
    @ApiModel(value = "GiftCardHistory", description = "Gift Card Transaction History")
    public static class GiftCardHistory {

        @ApiModelProperty(value = "Invoice Number", notes = "Invoice number")
        private String invoiceNumber;

        @ApiModelProperty(value = "Transaction Date", notes = "Transaction date")
        @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd'T'HH:mm:ss")
        private Date transactionDate;

        @ApiModelProperty(value = "Store Code", notes = "Store code")
        private String storeCode;

        @ApiModelProperty(value = "Store Name", notes = "Store name")
        private String storeName;

        @ApiModelProperty(value = "Transaction Type", notes = "Transaction type: GIFT CARD PURCHASED, GIFT CARD ACTIVATED, GIFT CARD REDEEM, GIFT CARD DESTROY, GIFT CARD BLOCKED")
        private String transactionType;

        @ApiModelProperty(value = "Transaction Amount", notes = "Transaction amount")
        private BigDecimal transactionAmount;

        @ApiModelProperty(value = "Notes", notes = "Notes")
        private String notes;
    }
}