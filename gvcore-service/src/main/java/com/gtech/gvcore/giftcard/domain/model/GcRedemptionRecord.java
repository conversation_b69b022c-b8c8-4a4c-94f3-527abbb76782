package com.gtech.gvcore.giftcard.domain.model;

import lombok.Data;
import lombok.Getter;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.Date;
import java.util.UUID;

/**
 * 礼品卡使用记录领域模型
 */
@Data
public class GcRedemptionRecord {
    private final String cardNumber;
    private final String redemptionCode;
    private final BigDecimal amount;
    private final BigDecimal balanceBefore;
    private final BigDecimal balanceAfter;
    private final String outletCode;
    private final String issuerCode;
    private final String cpgCode;
    private final String merchantCode;
    private final String invoiceNumber;
    private final String approvalCode;
    private final String notes;
    private final Date redemptionTime;
    private final String batchNumber;
    private GcRedemptionStatus status;

    /**
     * 创建使用记录
     */
    public GcRedemptionRecord(
            String redemptionCode,
            String cardNumber,
            String cpgCode,
            BigDecimal amount,
            BigDecimal balanceBefore,
            String outletCode,
            String issuerCode,
            String merchantCode,
            String invoiceNumber,
            String approvalCode,
            String notes,
            String batchNumber
    ) {
        this.redemptionCode = redemptionCode;
        this.cardNumber = cardNumber;
        this.amount = amount;
        this.balanceBefore = balanceBefore;
        this.balanceAfter = calculateBalanceAfter(balanceBefore, amount);
        this.invoiceNumber = invoiceNumber;
        this.redemptionTime = new Date();
        this.approvalCode = approvalCode;
        this.outletCode = outletCode;
        this.issuerCode = issuerCode;
        this.merchantCode = merchantCode;
        this.cpgCode = cpgCode;
        this.notes = notes;
        this.status = GcRedemptionStatus.SUCCESS;
        this.batchNumber = batchNumber;
    }

    /**
     * 取消使用记录
     */
    public void markAsCancelled() {
        if (this.status == GcRedemptionStatus.CANCELLED) {
            throw new IllegalStateException("使用记录已经被取消");
        }
        this.status = GcRedemptionStatus.CANCELLED;
    }

    /**
     * 计算交易后余额
     */
    private static BigDecimal calculateBalanceAfter(BigDecimal balanceBefore, BigDecimal transactionAmount) {
        if (balanceBefore.compareTo(transactionAmount) < 0) {
            throw new IllegalArgumentException("余额不足，无法完成交易");
        }
        return balanceBefore.subtract(transactionAmount);
    }

    /**
     * 标记为失败
     */
    public void markAsFailed() {
        this.status = GcRedemptionStatus.FAILED;
    }
} 