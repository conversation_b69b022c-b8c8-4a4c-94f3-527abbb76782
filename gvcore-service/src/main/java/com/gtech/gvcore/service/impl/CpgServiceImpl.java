package com.gtech.gvcore.service.impl;

import com.github.pagehelper.PageInfo;
import com.github.pagehelper.page.PageMethod;
import com.gtech.commons.redis.GTechRedisTemplate;
import com.gtech.commons.result.PageResult;
import com.gtech.commons.result.Result;
import com.gtech.gvcore.cache.MasterDataCache;
import com.gtech.gvcore.common.constants.GvcoreConstants;
import com.gtech.gvcore.common.enums.CpgTypeAutomaticActivateEnum;
import com.gtech.gvcore.common.enums.ResultErrorCodeEnum;
import com.gtech.gvcore.common.enums.VoucherStatusEnum;
import com.gtech.gvcore.common.request.cpg.CreateCpgRequest;
import com.gtech.gvcore.common.request.cpg.GetCpgRequest;
import com.gtech.gvcore.common.request.cpg.QueryCpgVoucherInventory;
import com.gtech.gvcore.common.request.cpg.QueryCpgsByPageRequest;
import com.gtech.gvcore.common.request.cpg.UpdateCpgRequest;
import com.gtech.gvcore.common.request.cpg.UpdateCpgStatusRequest;
import com.gtech.gvcore.common.response.cpg.CpgPrinterVo;
import com.gtech.gvcore.common.response.cpg.GetCpgResponse;
import com.gtech.gvcore.common.response.cpg.QueryCpgByOutletCodeResponse;
import com.gtech.gvcore.common.response.cpg.QueryCpgVoucherInventoryResponse;
import com.gtech.gvcore.common.response.cpg.QueryCpgsByPageResponse;
import com.gtech.gvcore.common.utils.UUIDUtils;
import com.gtech.gvcore.dao.dto.CpgDto;
import com.gtech.gvcore.dao.mapper.CpgMapper;
import com.gtech.gvcore.dao.mapper.VoucherMapper;
import com.gtech.gvcore.dao.model.ArticleMop;
import com.gtech.gvcore.dao.model.Cpg;
import com.gtech.gvcore.dao.model.CpgPrinter;
import com.gtech.gvcore.dao.model.CpgType;
import com.gtech.gvcore.dao.model.Issuer;
import com.gtech.gvcore.dao.model.Printer;
import com.gtech.gvcore.dao.model.Voucher;
import com.gtech.gvcore.service.ArticleMopService;
import com.gtech.gvcore.service.CpgPrinterService;
import com.gtech.gvcore.service.CpgService;
import com.gtech.gvcore.service.CpgTypeService;
import com.gtech.gvcore.service.IssuerService;
import com.gtech.gvcore.service.PrinterService;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.dao.DuplicateKeyException;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import tk.mybatis.mapper.entity.Example;

import java.util.ArrayList;
import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2022年2月16日
 */
@Service
public class CpgServiceImpl implements CpgService {

    @Autowired
    private CpgMapper cpgMapper;

    @Autowired
    private CpgPrinterService cpgPrinterService;

    @Autowired
    private PrinterService printerService;

    @Autowired
    private CpgTypeService cpgTypeService;

    @Autowired
    private ArticleMopService articleMopService;

    @Autowired
    private IssuerService issuerService;


    @Autowired
    private VoucherMapper voucherMapper;
    @Autowired
    private GTechRedisTemplate redisTemplate;
    @Value("#{${gv.issuer.warehouse}}")
    private Map<String, String> issuerWarehouseMap;

    @Autowired
    private MasterDataCache masterDataCache;

    String APP_CODE = "GV";
    String CPG = "CPG:";
    String PATH = "CACHE:";
    @Transactional
    @Override
    public Result<Long> createCpg(CreateCpgRequest request) {

        Result<Long> result = checkEffectiveTypeAndSetDefault(request);
        if (!result.isSuccess()) {
            return result;
        }

        // toUpperCase
        request.setCpgName(request.getCpgName().toUpperCase());

        Cpg cpg = new Cpg();
        cpg.setCpgName(request.getCpgName());
        int i = cpgMapper.selectCount(cpg);
        if (i > 0) {
            return Result.failed(ResultErrorCodeEnum.NAME_ALREADY_USED.code(),
                    ResultErrorCodeEnum.NAME_ALREADY_USED.desc());
        }

        Result<String> checkResult = checkBasicData(request.getIssuerCode(), request.getCpgTypeCode(),
                request.getArticleMopCode(), request.getPrinterCodeList());
        if (!checkResult.isSuccess()) {
            return Result.failed(checkResult.getCode(), checkResult.getMessage());
        }

        BeanUtils.copyProperties(request, cpg);
        cpg.setCpgCode(UUIDUtils.generateCode());
        cpg.setStatus(GvcoreConstants.STATUS_ENABLE);
        cpg.setCreateTime(new Date());

        try {
            cpgMapper.insertSelective(cpg);
        } catch (DuplicateKeyException e) {
            return Result.failed(ResultErrorCodeEnum.DATA_ALREADY_EXISTS.code(),
                    ResultErrorCodeEnum.DATA_ALREADY_EXISTS.desc());
        }

        if (CollectionUtils.isNotEmpty(request.getPrinterCodeList())) {
            List<String> printerCodeList = request.getPrinterCodeList().stream().distinct()
                    .collect(Collectors.toList());
            List<CpgPrinter> printerList = new ArrayList<>(printerCodeList.size());
            for (String printerCode : printerCodeList) {
                CpgPrinter cpgPrinter = new CpgPrinter();
                cpgPrinter.setCpgCode(cpg.getCpgCode());
                cpgPrinter.setPrinterCode(printerCode);
                cpgPrinter.setDeleteStatus(GvcoreConstants.DELETE_STATUS_DISABLE);
                cpgPrinter.setCreateUser(cpg.getCreateUser());
                cpgPrinter.setCreateTime(cpg.getCreateTime());
                printerList.add(cpgPrinter);
            }
            cpgPrinterService.insertList(printerList);
        }

        String cpgCode = cpg.getCpgCode();
        cpg = new Cpg();
        cpg.setCpgCode(cpgCode);
        cpg = cpgMapper.selectOne(cpg);

        masterDataCache.updateCpgCache(cpg);
        return Result.ok(cpg.getId());
    }

    /**
     * 
     * @param issuerCode
     * @param cpgTypeCode
     * @param articleMopCode
     * @param printerCodeList
     * @return
     * <AUTHOR>
     * @date 2022年5月9日
     */
    private Result<String> checkBasicData(String issuerCode, String cpgTypeCode, String articleMopCode,
            List<String> printerCodeList) {

        Issuer issuer = issuerService.queryByIssuerCode(issuerCode);
        if (issuer == null) {
            return Result.failed(ResultErrorCodeEnum.NO_ISSUER_DATA_FOUND.code(),
                    ResultErrorCodeEnum.NO_ISSUER_DATA_FOUND.desc());
        }

        CpgType cpgType = cpgTypeService.queryByCpgTypeCode(cpgTypeCode);
        if (cpgType == null) {
            return Result.failed(ResultErrorCodeEnum.NO_CPG_TYPE_DATA_FOUND.code(),
                    ResultErrorCodeEnum.NO_CPG_TYPE_DATA_FOUND.desc());
        }

        ArticleMop articleMop = articleMopService.queryByArticleMopCode(articleMopCode);
        if (articleMop == null) {
            return Result.failed(ResultErrorCodeEnum.NO_ARTICLE_MOP_DATA_FOUND.code(),
                    ResultErrorCodeEnum.NO_ARTICLE_MOP_DATA_FOUND.desc());
        }

        if (CollectionUtils.isNotEmpty(printerCodeList)) {
            printerCodeList = printerCodeList.stream().distinct().collect(Collectors.toList());
            int count = printerService.countByPrinterCodeList(printerCodeList);
            if (count != printerCodeList.size()) {
                return Result.failed(ResultErrorCodeEnum.NO_PRINTER_DATA_FOUND.code(),
                        ResultErrorCodeEnum.NO_PRINTER_DATA_FOUND.desc());
            }
        }

        return Result.ok();
    }

    /**
     * @param createCpgRequest
     * @return
     * <AUTHOR>
     * @date 2022年3月22日
     */
    private Result<Long> checkEffectiveTypeAndSetDefault(CreateCpgRequest createCpgRequest) {

        if (invalidNum(createCpgRequest.getEffectiveYears()) && invalidNum(createCpgRequest.getEffectiveMonth())
                && invalidNum(createCpgRequest.getEffectiveDay()) && invalidNum(createCpgRequest.getEffectiveHour())) {
            return Result.failed(ResultErrorCodeEnum.CPG_EFFECTIVE_YEAR_MONTH_DAY_CANOT_BE_NULL.code(),
                    ResultErrorCodeEnum.CPG_EFFECTIVE_YEAR_MONTH_DAY_CANOT_BE_NULL.desc());
        }

        if (invalidNum(createCpgRequest.getEffectiveYears())) {
            createCpgRequest.setEffectiveYears(0);
        }

        if (invalidNum(createCpgRequest.getEffectiveMonth())) {
            createCpgRequest.setEffectiveMonth(0);
        }

        if (invalidNum(createCpgRequest.getEffectiveDay())) {
            createCpgRequest.setEffectiveDay(0);
        }

        if (invalidNum(createCpgRequest.getEffectiveHour())) {
            createCpgRequest.setEffectiveHour(0);
        }

        return Result.ok();
    }

    private boolean invalidNum(Integer num) {
        return Objects.isNull(num) || num <= 0;
    }

    /**
     * @param updateCpgRequest
     * @return
     * <AUTHOR>
     * @date 2022年3月22日
     */
    private Result<String> checkEffectiveTypeAndSetDefault(UpdateCpgRequest updateCpgRequest) {

        if (invalidNum(updateCpgRequest.getEffectiveYears()) && invalidNum(updateCpgRequest.getEffectiveMonth())
                && invalidNum(updateCpgRequest.getEffectiveDay()) && invalidNum(updateCpgRequest.getEffectiveHour())) {
            return Result.failed(ResultErrorCodeEnum.CPG_EFFECTIVE_YEAR_MONTH_DAY_CANOT_BE_NULL.code(),
                    ResultErrorCodeEnum.CPG_EFFECTIVE_YEAR_MONTH_DAY_CANOT_BE_NULL.desc());
        }

        if (invalidNum(updateCpgRequest.getEffectiveYears())) {
            updateCpgRequest.setEffectiveYears(0);
        }

        if (invalidNum(updateCpgRequest.getEffectiveMonth())) {
            updateCpgRequest.setEffectiveMonth(0);
        }

        if (invalidNum(updateCpgRequest.getEffectiveDay())) {
            updateCpgRequest.setEffectiveDay(0);
        }

        if (invalidNum(updateCpgRequest.getEffectiveHour())) {
            updateCpgRequest.setEffectiveHour(0);
        }
        return Result.ok();
    }

    @Override
    public Result<String> updateCpg(UpdateCpgRequest request) {

        Result<String> result = checkEffectiveTypeAndSetDefault(request);
        if (!result.isSuccess()) {
            return result;
        }

        Cpg queryCpg = cpgMapper.selectByPrimaryKey(request.getId());
        if (queryCpg == null) {
            return Result.failed(ResultErrorCodeEnum.NO_DATA_FOUND.code(), ResultErrorCodeEnum.NO_DATA_FOUND.desc());
        }

        Cpg cpg = new Cpg();
        if (!queryCpg.getCpgName().equalsIgnoreCase(request.getCpgName())) {
            cpg.setCpgName(request.getCpgName());
            int i = cpgMapper.selectCount(cpg);
            if (i > 0) {
                return Result.failed(ResultErrorCodeEnum.NAME_ALREADY_USED.code(),
                        ResultErrorCodeEnum.NAME_ALREADY_USED.desc());
            }
        }

        result = checkBasicData(request.getIssuerCode(), request.getCpgTypeCode(), request.getArticleMopCode(),
                request.getPrinterCodeList());
        if (!result.isSuccess()) {
            return result;
        }

        Map<String, CpgPrinter> printerMap = cpgPrinterService.queryMapByCpgCode(queryCpg.getCpgCode());

        BeanUtils.copyProperties(request, cpg);
        cpg.setUpdateTime(new Date());
        int i = cpgMapper.updateByPrimaryKeySelective(cpg);
        if (i == 0) {
            return Result.failed(ResultErrorCodeEnum.FAILED_TO_UPDATE_DATA.code(),
                    ResultErrorCodeEnum.FAILED_TO_UPDATE_DATA.desc());
        }

        insertOrUpdatePrinter(request, queryCpg, cpg, printerMap);
        cpg.setCpgCode(queryCpg.getCpgCode());
        masterDataCache.updateCpgCache(cpg);
        return Result.ok(queryCpg.getCpgCode());
    }

    private void insertOrUpdatePrinter(UpdateCpgRequest request, Cpg queryCpg, Cpg cpg,
                                       Map<String, CpgPrinter> printerMap) {
        if (CollectionUtils.isNotEmpty(request.getPrinterCodeList())) {
            List<String> printerCodeList = request.getPrinterCodeList().stream().distinct()
                    .collect(Collectors.toList());
            List<CpgPrinter> printerList = new ArrayList<>(printerCodeList.size());
            for (String printerCode : printerCodeList) {
                CpgPrinter cpgPrinter = printerMap.remove(printerCode);
                if (cpgPrinter == null) {
                    cpgPrinter = new CpgPrinter();
                    cpgPrinter.setCpgCode(queryCpg.getCpgCode());
                    cpgPrinter.setPrinterCode(printerCode);
                    cpgPrinter.setDeleteStatus(GvcoreConstants.DELETE_STATUS_DISABLE);
                    cpgPrinter.setCreateUser(cpg.getUpdateUser());
                    cpgPrinter.setCreateTime(cpg.getUpdateTime());
                    printerList.add(cpgPrinter);
                } else if (GvcoreConstants.DELETE_STATUS_ENABLE.intValue() == cpgPrinter.getDeleteStatus()) {
                    CpgPrinter update = new CpgPrinter();
                    update.setId(cpgPrinter.getId());
                    update.setDeleteStatus(GvcoreConstants.DELETE_STATUS_DISABLE);
                    update.setUpdateUser(cpg.getUpdateUser());
                    update.setUpdateTime(cpg.getUpdateTime());
                    cpgPrinterService.updateById(update);
                }
            }

            if (!printerList.isEmpty()) {
                cpgPrinterService.insertList(printerList);
            }
        }

        for (Map.Entry<String, CpgPrinter> entry : printerMap.entrySet()) {
            CpgPrinter update = new CpgPrinter();
            update.setId(entry.getValue().getId());
            update.setDeleteStatus(GvcoreConstants.DELETE_STATUS_ENABLE);
            update.setUpdateUser(cpg.getUpdateUser());
            update.setUpdateTime(cpg.getUpdateTime());
            cpgPrinterService.updateById(update);
        }
    }

    @Override
    public Result<String> updateCpgStatus(UpdateCpgStatusRequest request) {

        Cpg cpg = new Cpg();
        cpg.setId(request.getId());
        cpg = cpgMapper.selectOne(cpg);
        if (cpg == null) {
            return Result.failed(ResultErrorCodeEnum.NO_DATA_FOUND.code(), ResultErrorCodeEnum.NO_DATA_FOUND.desc());
        }

        cpg.setStatus(request.getStatus());
        cpg.setUpdateUser(request.getUpdateUser());
        cpg.setUpdateTime(new Date());
        int i = cpgMapper.updateByPrimaryKeySelective(cpg);
        if (i == 0) {
            return Result.failed(ResultErrorCodeEnum.FAILED_TO_UPDATE_DATA.code(),
                    ResultErrorCodeEnum.FAILED_TO_UPDATE_DATA.desc());
        }
        masterDataCache.updateCpgCache(cpg);
        return Result.ok(cpg.getCpgCode());
    }

    @Override
    public PageResult<QueryCpgsByPageResponse> queryCpgsByPage(QueryCpgsByPageRequest request) {

    	CpgDto dto = new CpgDto();
        dto.setIssuerCode(request.getIssuerCode());
        dto.setCpgName(request.getCpgName());
        dto.setCpgTypeCode(request.getCpgTypeCode());
        dto.setMopCode(request.getMopCode());
        dto.setDisableGeneration(request.getDisableGeneration());
        dto.setIssuerCodeList(request.getIssuerCodeList());

        PageMethod.startPage(request.getPageNum(), request.getPageSize());
        List<Cpg> list = cpgMapper.selectSelective(dto);
        PageInfo<Cpg> pageInfo = new PageInfo<>(list);

        List<String> articleMopCodeList = new ArrayList<>(request.getPageSize());
        List<String> cpgTypeCodeList = new ArrayList<>(request.getPageSize());
        for (Cpg cpg2 : list) {
            articleMopCodeList.add(cpg2.getArticleMopCode());
            cpgTypeCodeList.add(cpg2.getCpgTypeCode());
        }
        Map<String, CpgType> cpgTypeMap = cpgTypeService.queryCpgTypeCodeNameByCpgTypeCodeList(cpgTypeCodeList);
        Map<String, ArticleMop> articleMopMap = articleMopService.queryByArticleMopCodeList(articleMopCodeList);

        List<QueryCpgsByPageResponse> responses = new ArrayList<>(request.getPageSize());
        list.stream().forEach(item -> {
            QueryCpgsByPageResponse response = new QueryCpgsByPageResponse();
            BeanUtils.copyProperties(item, response);
            CpgType cpgType = cpgTypeMap.get(item.getCpgTypeCode());
            if (cpgType != null) {
                response.setCpgTypeName(cpgType.getCpgTypeName());
                response.setPrefix(cpgType.getPrefix());
            }
            ArticleMop articleMop = articleMopMap.get(item.getArticleMopCode());
            if (articleMop != null) {
                response.setMopCode(articleMop.getMopCode());
                response.setArticleCode(articleMop.getArticleCode());
            }
            responses.add(response);
        });

        return PageResult.ok(responses, pageInfo.getTotal());
    }

    @Override
    public List<Cpg> queryCpgAll() {
        return cpgMapper.selectAll();
    }

    @Override
    public Map<String, Cpg> queryCpgMapByCpgCodeList(List<String> cpgCodeList) {

        if (CollectionUtils.isEmpty(cpgCodeList)) {
            return Collections.emptyMap();
        }

        List<Cpg> cpgs = cpgMapper.queryByCpgCodeList(cpgCodeList);
        if (CollectionUtils.isEmpty(cpgs)) {
            return Collections.emptyMap();
        }

        return cpgs.stream().collect(Collectors.toMap(Cpg::getCpgCode, v -> v));
    }

    @Override
    public Result<GetCpgResponse> getCpg(GetCpgRequest request) {

        Cpg cpg = new Cpg();
        cpg.setCpgCode(request.getCpgCode());
        Cpg queryCpg = cpgMapper.selectOne(cpg);
        if (queryCpg == null) {
            return Result.failed(ResultErrorCodeEnum.NO_DATA_FOUND.code(), ResultErrorCodeEnum.NO_DATA_FOUND.desc());
        }

        GetCpgResponse response = new GetCpgResponse();
        BeanUtils.copyProperties(queryCpg, response);

        ArticleMop articleMop = articleMopService.queryByArticleMopCode(queryCpg.getArticleMopCode());
        if (articleMop != null) {
            response.setArticleCode(articleMop.getArticleCode());
            response.setMopCode(articleMop.getMopCode());
        }

        List<String> printerCodeList = cpgPrinterService.queryPrinterCodeByCpgCode(queryCpg.getCpgCode());
        List<Printer> printerList = printerService.queryBasicInfoByPrinterCodeList(printerCodeList);
        if (!printerList.isEmpty()) {
            List<CpgPrinterVo> printerVoList = new ArrayList<>(printerList.size());
            for (Printer printer : printerList) {
                CpgPrinterVo printerVo = new CpgPrinterVo();
                printerVo.setPrinterCode(printer.getPrinterCode());
                printerVo.setPrinterName(printer.getPrinterName());
                printerVoList.add(printerVo);
            }
            response.setPrinterList(printerVoList);
        }
        return Result.ok(response);
    }

    @Override
    public Result<List<QueryCpgByOutletCodeResponse>> queryOutletDenomination(String outletCode) {
        List<QueryCpgByOutletCodeResponse> queryCpgByOutletCodeResponses = cpgMapper.selectDenomination(outletCode);
        return Result.ok(queryCpgByOutletCodeResponses);
    }

    @Override
    public List<String> queryAutomaticActivateCpg(List<String> cpgCodeList, String issuerCode) {

        if (CollectionUtils.isEmpty(cpgCodeList)) {
            return Collections.emptyList();
        }

        List<String> list = cpgMapper.queryAutomaticActivateCpg(cpgCodeList, CpgTypeAutomaticActivateEnum.YES.code(),
                issuerCode);
        if (CollectionUtils.isEmpty(list)) {
            return Collections.emptyList();
        }
        return list;
    }

    @Override
    public Result<GetCpgResponse> getCpgByCpgName(String cpgName) {
        Cpg cpg = new Cpg();
        cpg.setCpgName(cpgName);
        Cpg queryCpg = cpgMapper.selectOne(cpg);
        GetCpgResponse response = new GetCpgResponse();
        BeanUtils.copyProperties(queryCpg, response);
        ArticleMop articleMop = articleMopService.queryByArticleMopCode(queryCpg.getArticleMopCode());
        if (articleMop != null) {
            response.setArticleCode(articleMop.getArticleCode());
            response.setMopCode(articleMop.getMopCode());
        }

        return Result.ok(response);
    }

    //TODO 性能问题
    @Override
    public Result<QueryCpgVoucherInventoryResponse> queryCpgVoucherInventory(QueryCpgVoucherInventory queryCpgVoucherInventory) {
        String wh01 = issuerWarehouseMap.get(queryCpgVoucherInventory.getIssuerCode());
        queryCpgVoucherInventory.setIssuerCode(wh01);
        Integer size =  this.queryInventoryCount(queryCpgVoucherInventory);
        return Result.ok(QueryCpgVoucherInventoryResponse.builder().count(size).build());
    }


    @Override
    public List<Voucher> getVoucherInventory(QueryCpgVoucherInventory queryCpgVoucherInventory) {



        //TODO CREATE INDEX idx_voucher_search ON gv_voucher_${} (cpg_code, voucher_owner_code, status, circulation_status, voucher_status);
        Example example = queryCpgVoucherInventoryExample(queryCpgVoucherInventory);
        List<Voucher> vouchers = voucherMapper.selectByCondition(example);
        return vouchers;
    }


    public Integer queryInventoryCount(QueryCpgVoucherInventory inventory){
        Example example = queryCpgVoucherInventoryExample(inventory);
        return voucherMapper.selectCountByCondition(example);
    }

    private static Example queryCpgVoucherInventoryExample(QueryCpgVoucherInventory queryCpgVoucherInventory) {
        Example example = new Example(Voucher.class);
        Example.Criteria criteria = example.createCriteria();
        if (StringUtils.isNotEmpty(queryCpgVoucherInventory.getCpgCode())){
            criteria.andEqualTo(Voucher.C_CPG_CODE, queryCpgVoucherInventory.getCpgCode());
        }else if (CollectionUtils.isNotEmpty(queryCpgVoucherInventory.getCpgCodes())){
            criteria.andIn(Voucher.C_CPG_CODE, queryCpgVoucherInventory.getCpgCodes());
        }
        criteria.andEqualTo(Voucher.C_VOUCHER_OWNER_CODE, queryCpgVoucherInventory.getIssuerCode());
        criteria.andEqualTo(Voucher.C_STATUS, VoucherStatusEnum.VOUCHER_NEWLY_GENERATED.getCode());
        criteria.andEqualTo(Voucher.C_CIRCULATION_STATUS,"3");
        criteria.andEqualTo(Voucher.C_VOUCHER_STATUS,"1");
        return example;
    }

    @Override
    public Cpg getCpgByCode(String cpgCode) {

        if (StringUtils.isBlank(cpgCode)) return null;

        return cpgMapper.selectOne(new Cpg().setCpgCode(cpgCode));
    }
}
