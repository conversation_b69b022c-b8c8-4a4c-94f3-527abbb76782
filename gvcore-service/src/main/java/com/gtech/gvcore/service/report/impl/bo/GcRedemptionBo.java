package com.gtech.gvcore.service.report.impl.bo;

import com.gtech.commons.utils.DateUtil;
import com.gtech.gvcore.service.report.extend.poll.GroupNewTransactionByVoucherCodeSupport;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

import java.math.BigDecimal;
import java.util.Date;

/**
 * <AUTHOR>
 * @Date 2022/6/22 10:28
 */
@Getter
@Setter
@Accessors(chain = true)
public class GcRedemptionBo implements GroupNewTransactionByVoucherCodeSupport {

    private String transactionDate;
    private String cardNumber;
    private String merchantCode;
    private String outletCode;
    private String cpgCode;
    private String transactionType;
    private BigDecimal denomination;
    private String invoiceNumber;
    private BigDecimal amount;
    private BigDecimal balanceBefore;
    private BigDecimal balanceAfter;
    private String approveCode;
    private String subCompanyName;
    private BigDecimal transactionNumber;

    public Date getTransactionDate() {

        return DateUtil.parseDate(transactionDate, DateUtil.FORMAT_YYYYMMDDHHMISS);
    }

    public void setTransactionDate(String transactionDate) {
        String format = DateUtil.format(DateUtil.parseDate(transactionDate, DateUtil.FORMAT_YYYYMMDDHHMISS), DateUtil.FORMAT_YYYYMMDDHHMISS_14);
        this.transactionNumber = new BigDecimal(format);
        this.transactionDate = transactionDate;
    }

    @Override
    public String getVoucherCode() {
        return cardNumber;
    }
}
