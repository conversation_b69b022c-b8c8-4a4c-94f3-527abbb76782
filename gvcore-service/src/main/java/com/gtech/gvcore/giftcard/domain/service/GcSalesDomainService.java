package com.gtech.gvcore.giftcard.domain.service;

import com.gtech.gvcore.giftcard.application.dto.GcSalesDTO;
import com.gtech.gvcore.giftcard.application.dto.SalesCancelDTO;
import com.gtech.gvcore.giftcard.domain.model.GcSalesRecord;
import com.gtech.gvcore.giftcard.domain.model.SalesCancelRecord;

import com.gtech.gvcore.giftcard.domain.repository.GcCancelSalesRepository;

import com.gtech.gvcore.giftcard.domain.repository.GcSalesRepository;
import com.gtech.gvcore.helper.GvCodeHelper;
import lombok.RequiredArgsConstructor;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

@Service
@RequiredArgsConstructor
public class GcSalesDomainService {

    private final GcSalesRepository salesRepository;
    private final GcCancelSalesRepository cancelSalesRepository;

    private final GvCodeHelper gvCodeHelper;

    @Transactional
    public int recordSales(GcSalesDTO gcSalesDTO) {
        //批量创建实体类，组装之后统一添加
        List<GcSalesRecord> salesRecords = gcSalesDTO.getCardNumber().stream()
                .map(cardNumber -> new GcSalesRecord(
                        gvCodeHelper.generateTransactionCode(),
                        cardNumber,
                        gcSalesDTO.getCpgCode(),
                        gcSalesDTO.getIssuerCode(),
                        gcSalesDTO.getMerchantCode(),
                        gcSalesDTO.getOutletCode(),
                        gcSalesDTO.getCustomerCode(),
                        gcSalesDTO.getInvoiceNumber(),
                        gcSalesDTO.getApprovalCode(),
                        gcSalesDTO.getCustomerOrderCode(),
                        gcSalesDTO.getDenomination(),
                        gcSalesDTO.getSalesTime(),
                        gcSalesDTO.getNotes(),
                        gcSalesDTO.getBatchNumber()
                )).collect(Collectors.toList());

        return salesRepository.batchSave(salesRecords);
    }


    @Transactional
    public SalesCancelDTO cancelSales(SalesCancelDTO salesCancelDTO) {
        // 验证销售记录是否存在
        salesRepository.findBySalesCode(salesCancelDTO.getSalesCode())
                .orElseThrow(() -> new IllegalArgumentException("销售记录不存在: " + salesCancelDTO.getSalesCode()));

        // 创建销售取消记录
        SalesCancelRecord cancelRecord = new SalesCancelRecord(
                salesCancelDTO.getIssuerCode(),
                salesCancelDTO.getOutletCode(),
                salesCancelDTO.getMerchantCode(),
                gvCodeHelper.generateTransactionCode(),
                salesCancelDTO.getInvoiceNumber(),
                salesCancelDTO.getApprovalNumber(),
                salesCancelDTO.getCpgCode(),
                salesCancelDTO.getCardNumber(),
                salesCancelDTO.getCancelReason()
        );

        cancelSalesRepository.save(cancelRecord);

        // 返回取消结果
        salesCancelDTO.setCancelTime(cancelRecord.getCancelTime());

        return salesCancelDTO;
    }


    @Transactional
    public SalesCancelDTO cancelSalesByPoNumber(SalesCancelDTO salesCancelDTO) {
        // 验证销售记录是否存在
        List<GcSalesRecord> gcSalesRecords = salesRepository.findByPoNumber(salesCancelDTO.getSalesCode())
                .orElseThrow(() -> new IllegalArgumentException("销售记录不存在: " + salesCancelDTO.getSalesCode()));

        // 创建销售取消记录

        List<SalesCancelRecord> cancelRecordList = gcSalesRecords.stream().map(gcSalesRecord -> new SalesCancelRecord(
                gcSalesRecord.getIssuerCode(),
                gcSalesRecord.getOutletCode(),
                gcSalesRecord.getMerchantCode(),
                gvCodeHelper.generateTransactionCode(),
                gcSalesRecord.getInvoiceNumber(),
                gcSalesRecord.getApprovalCode(),
                gcSalesRecord.getCpgCode(),
                gcSalesRecord.getCardNumber(),
                salesCancelDTO.getCancelReason()
        )).collect(Collectors.toList());
        cancelSalesRepository.saveList(cancelRecordList);
        return salesCancelDTO;
    }

    public List<GcSalesRecord> getSalesRecordByCardNumber(String cardNumbers) {
        if (StringUtils.isBlank(cardNumbers)) return Collections.emptyList();
        return this.salesRepository.findByCardNumber(cardNumbers);
    }

    public List<GcSalesRecord> getSalesRecordByBatchNumber(String batchNumber) {
        if (null == batchNumber) return Collections.emptyList();
        return this.salesRepository.findByBatchNumber(batchNumber);
    }


    public List<GcSalesRecord> getSalesRecordByInvoiceNumber(String invoiceNumber) {
        if (StringUtils.isBlank(invoiceNumber)) {
            return Collections.emptyList();
        }
        return this.salesRepository.findByInvoiceNumber(invoiceNumber);
    }

    public List<GcSalesRecord> getSalesRecordByCardNumber(List<String> cardNumbers) {
        if (cardNumbers.isEmpty()) {
            return Collections.emptyList();
        }
        return this.salesRepository.findByCardNumber(cardNumbers);
    }

    /**
     * 根据卡号查询取消销售记录
     */
    public List<SalesCancelRecord> getCancelSalesRecordByCardNumber(String cardNumber) {
        if (StringUtils.isBlank(cardNumber)) {
            return Collections.emptyList();
        }
        return this.cancelSalesRepository.findByCardNumber(cardNumber);
    }


}
