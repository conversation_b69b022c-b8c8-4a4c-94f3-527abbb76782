package com.gtech.gvcore.service.report.impl.param;

import com.gtech.gvcore.service.report.ReportQueryParam;
import com.gtech.gvcore.service.report.extend.row.TransactionDataPageParam;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

import java.util.Date;
import java.util.List;

/**
 * @ClassName GcExpiryQueryData
 * @Description Gift Card Expiry Query Data
 * <AUTHOR>
 * @Date 2023/5/10 10:36
 * @Version V1.0
 **/
@Getter
@Setter
@Accessors(chain = true)
public class GcExpiryQueryData extends TransactionDataPageParam implements ReportQueryParam {

    //expiry date start 过期时间 开始
    private Date expiryDateStart;

    //expiry date end 过期时间 结束
    private Date expiryDateEnd;

    //sales time start 销售时间 开始
    private Date salesTimeStart;

    //sales time end 销售时间 结束
    private Date salesTimeEnd;

    //activation time start 激活时间 开始
    private Date activationTimeStart;

    //activation time end 激活时间 结束
    private Date activationTimeEnd;

    //merchant code list
    private List<String> merchantCodeList;

    //cpg code list
    private List<String> cpgCodeList;

    //invoice number 发票编号
    private String invoiceNumber;

    private List<String> issuerCodeList;

    private Long voucherCodeNumStart;

    private Long voucherCodeNumEnd;

    private List<String> outletCodeList;

    //customer code list
    private List<String> customerCodeList;

    //card status list
    private List<String> cardStatusList;
}
