package com.gtech.gvcore.service.report.impl.param;

import com.gtech.gvcore.service.report.ReportQueryParam;
import com.gtech.gvcore.service.report.extend.row.TransactionDataPageParam;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

import java.util.Date;
import java.util.List;

/**
 * @ClassName GcTransactionDetailQueryData
 * @Description Gift Card Transaction Detail Query Data
 * <AUTHOR>
 * @Date 2025-06-18
 * @Version V1.0
 */
@Getter
@Setter
@Accessors(chain = true)
public class GcTransactionDetailQueryData extends TransactionDataPageParam implements ReportQueryParam {

    /**
     * Transaction date start
     */
    private Date transactionDateStart;

    /**
     * Transaction date end
     */
    private Date transactionDateEnd;

    /**
     * Issuer code
     */
    private String issuerCode;

    /**
     * Issuer codes list
     */
    private List<String> issuerCodes;

    /**
     * Merchant codes list
     */
    private List<String> merchantCodes;

    /**
     * Outlet codes list
     */
    private List<String> outletCodes;

    /**
     * CPG codes list
     */
    private List<String> cpgCodes;

    /**
     * Transaction types list
     */
    private List<String> transactionType;

    /**
     * Transaction status
     */
    private String transactionStatus;

    /**
     * Gift card status list
     */
    private List<String> cardStatusList;

    /**
     * Invoice number
     */
    private String invoiceNumber;

    /**
     * Customer type
     */
    private String customerType;

    /**
     * Customer codes list
     */
    private List<String> customerCodes;

    /**
     * Corporate
     */
    private String corporate;

    /**
     * Bulk order status list
     */
    private List<String> bulkOrderStatus;

    /**
     * Expiry status start date
     */
    private Date expiryStatusStartDate;

    /**
     * Expiry status end date
     */
    private Date expiryStatusEndDate;

    /**
     * Card effective date start
     */
    private Date cardEffectiveDateStart;

    /**
     * Card effective date end
     */
    private Date cardEffectiveDateEnd;

    /**
     * Card number start
     */
    private String cardNumberStart;

    /**
     * Card number end
     */
    private String cardNumberEnd;

    /**
     * Purchase order card number list
     */
    private List<String> purchaseOrderCardNumberList;

    /**
     * Bulk order status transaction id list
     */
    private List<String> bulkOrderStatusTransactionIdList;

}
