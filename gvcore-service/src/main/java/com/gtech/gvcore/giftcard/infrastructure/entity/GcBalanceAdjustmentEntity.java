package com.gtech.gvcore.giftcard.infrastructure.entity;

import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

import javax.persistence.*;

/**
 * 礼品卡余额调整记录实体
 */
@Data
@Entity
@Table(name = "gc_balance_adjustment")
public class GcBalanceAdjustmentEntity {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @Column(name = "adjustment_code", nullable = false, unique = true)
    private String adjustmentCode;

    @Column(name = "card_number", nullable = false)
    private String cardNumber;

    @Column(name = "issuer_code", nullable = false)
    private String issuerCode;

    @Column(name = "outlet_code", nullable = false)
    private String outletCode;

    @Column(name = "adjustment_amount", nullable = false)
    private BigDecimal adjustmentAmount;

    @Column(name = "balance_before", nullable = false)
    private BigDecimal balanceBefore;

    @Column(name = "balance_after", nullable = false)
    private BigDecimal balanceAfter;

    @Column(name = "adjustment_type", nullable = false)
    private String adjustmentType;

    @Column(name = "merchant_code", nullable = false)
    private String merchantCode;

    @Column(name = "terminal_id")
    private String terminalId;

    @Column(name = "invoice_number")
    private String invoiceNumber;

    @Column(name = "adjustment_time", nullable = false)
    private Date adjustmentTime;

    @Column(name = "create_time", nullable = false)
    private Date createTime;

    @Column(name = "update_time", nullable = false)
    private Date updateTime;
}