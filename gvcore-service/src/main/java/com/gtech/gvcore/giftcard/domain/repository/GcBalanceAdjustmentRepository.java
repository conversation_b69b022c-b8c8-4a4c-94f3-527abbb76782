package com.gtech.gvcore.giftcard.domain.repository;

import com.gtech.commons.utils.BeanCopyUtils;
import com.gtech.gvcore.giftcard.domain.model.GcBalanceAdjustmentRecord;
import com.gtech.gvcore.giftcard.infrastructure.entity.GcBalanceAdjustmentEntity;
import com.gtech.gvcore.giftcard.infrastructure.mapper.GcBalanceAdjustmentMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * 礼品卡余额调整记录仓储实现
 */
@Repository
public class GcBalanceAdjustmentRepository {

    @Autowired
    private GcBalanceAdjustmentMapper gcBalanceAdjustmentMapper;
    

    /**
     * 保存余额调整记录
     */
    @Transactional
    public GcBalanceAdjustmentRecord save(GcBalanceAdjustmentRecord record) {
        GcBalanceAdjustmentEntity entity = BeanCopyUtils.jsonCopyBean(record, GcBalanceAdjustmentEntity.class);
        
        if (entity.getId() == null) {
            gcBalanceAdjustmentMapper.insert(entity);
        } else {
            gcBalanceAdjustmentMapper.updateByPrimaryKey(entity);
        }
        
        return record;
    }
    
    /**
     * 根据ID查询余额调整记录
     */
    public Optional<GcBalanceAdjustmentRecord> findById(String id) {
        GcBalanceAdjustmentEntity entity = gcBalanceAdjustmentMapper.selectByPrimaryKey(id);
        return Optional.ofNullable(entity).map(x  -> BeanCopyUtils.jsonCopyBean(x, GcBalanceAdjustmentRecord.class));
    }
    
    /**
     * 根据卡号查询余额调整记录
     */
    public List<GcBalanceAdjustmentRecord> findByCardNumber(String cardNumber) {
        List<GcBalanceAdjustmentEntity> entities = gcBalanceAdjustmentMapper.selectByCardNumber(cardNumber);
        return BeanCopyUtils.jsonCopyList(entities, GcBalanceAdjustmentRecord.class);
    }
    
    /**
     * 根据调整类型查询余额调整记录
     */
    public List<GcBalanceAdjustmentRecord> findByAdjustmentType(String adjustmentType) {
        List<GcBalanceAdjustmentEntity> entities = gcBalanceAdjustmentMapper.selectByAdjustmentType(adjustmentType);
        return BeanCopyUtils.jsonCopyList(entities, GcBalanceAdjustmentRecord.class);
    }

} 