package com.gtech.gvcore.service.impl.issuehandle;

import com.gtech.gvcore.common.enums.IssueHandlingProcessStatusEnum;
import com.gtech.gvcore.common.enums.IssueHandlingTypeEnum;
import com.gtech.gvcore.common.response.outlet.OutletResponse;
import com.gtech.gvcore.dao.model.IssueHandlingDetails;
import com.gtech.gvcore.giftcard.infrastructure.entity.GcBalanceAdjustmentEntity;
import com.gtech.gvcore.giftcard.infrastructure.entity.GiftCardEntity;
import com.gtech.gvcore.giftcard.infrastructure.mapper.GcBalanceAdjustmentMapper;
import com.gtech.gvcore.helper.GvCodeHelper;
import com.gtech.gvcore.service.IssueHandlerBaseService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

@Service
@Slf4j
public class GcIssueHandlerBulkRedeemService extends GcIssueHandlerValidateService implements IssueHandlerBaseService {

    @Autowired
    GvCodeHelper gcCodeHelper;

    @Autowired
    GcBalanceAdjustmentMapper gcBalanceAdjustmentMapper;

    @Override
    public IssueHandlingTypeEnum getIssueHandlingType() {
        return IssueHandlingTypeEnum.GC_BULK_REDEEM;
    }

    @Override
    public List<IssueHandlingDetails> validate(List<IssueHandlingDetails> details, String issuerCode) {

        return check(details, issuerCode);
    }

    @Override
    public List<IssueHandlingDetails> execute(List<IssueHandlingDetails> details, String issuerCode) {

        List<IssueHandlingDetails> check = check(details, issuerCode);

        List<IssueHandlingDetails> successVoucherCodes = check.stream()
                .filter(detail -> IssueHandlingProcessStatusEnum.SUCCESS.code().equals(detail.getProcessStatus()))
                .collect(Collectors.toList());

        if (CollectionUtils.isNotEmpty(successVoucherCodes)) {
            successVoucherCodes.forEach(this::performAction);
        }

        return check;
    }

    private List<IssueHandlingDetails> check(List<IssueHandlingDetails> details, String issuerCode) {

        if (CollectionUtils.isEmpty(details)) {
            return Collections.emptyList();
        }
        checkIfExist(details, getIssueHandlingType(), issuerCode);
        checkOutletName(details);
        //check original invoice number
        details.forEach(x -> {
            if (StringUtils.isBlank(x.getInvoiceNo())) {
                x.setProcessStatus(IssueHandlingProcessStatusEnum.FAILED.code());
                x.setResult(x.getResult() + "Original Invoice Number can not be empty!");
            }
        });
        return details;
    }

    private int performAction(IssueHandlingDetails handlingDetails) {
        //get current balance by card number
        GiftCardEntity balanceCheckEntity = new GiftCardEntity();
        balanceCheckEntity.setCardNumber(handlingDetails.getVoucherCode());
        balanceCheckEntity = giftCardMapper.selectOne(balanceCheckEntity);
        BigDecimal balance = balanceCheckEntity.getBalance();
        GiftCardEntity giftCard = new GiftCardEntity();
        giftCard.setBalance(handlingDetails.getAmount().negate());
        giftCard.setCardNumber(handlingDetails.getVoucherCode());
        int i = giftCardMapper.updateBalanceByCardNumber(giftCard);
        if (i > 0) {
            //add balance adjustment record
            GcBalanceAdjustmentEntity adjustmentEntity = new GcBalanceAdjustmentEntity();
            adjustmentEntity.setAdjustmentCode(gcCodeHelper.generateBalanceAdjustmentCode());
            adjustmentEntity.setCardNumber(handlingDetails.getVoucherCode());
            adjustmentEntity.setIssuerCode(balanceCheckEntity.getIssuerCode());
            OutletResponse outletByOutletName = null;
            if (StringUtils.isNotEmpty(handlingDetails.getOutletName())) {
                outletByOutletName = outletService.getOutletByOutletName(handlingDetails.getOutletName());
            }
            if (null != outletByOutletName) {
                adjustmentEntity.setOutletCode(outletByOutletName.getOutletCode());
                adjustmentEntity.setMerchantCode(outletByOutletName.getMerchantCode());
            }
            adjustmentEntity.setAdjustmentAmount(handlingDetails.getAmount());
            adjustmentEntity.setBalanceBefore(balance);
            adjustmentEntity.setBalanceAfter(balance.subtract(handlingDetails.getAmount()));
            adjustmentEntity.setAdjustmentType(IssueHandlingTypeEnum.GC_BULK_REDEEM.code());
            adjustmentEntity.setInvoiceNumber(handlingDetails.getInvoiceNo());
            adjustmentEntity.setAdjustmentTime(new Date());
            gcBalanceAdjustmentMapper.insertSelective(adjustmentEntity);
        } else {
            log.info("redemption action fail, no record changed. cardNumber:{}", handlingDetails.getVoucherCode());
        }
        return i;
    }
}


