package com.gtech.gvcore.service.report.impl;

import com.gtech.commons.utils.DateUtil;
import com.gtech.commons.utils.StringUtil;
import com.gtech.gvcore.common.enums.ReportExportTypeEnum;
import com.gtech.gvcore.common.request.orderreport.CreateReportRequest;
import com.gtech.gvcore.dao.model.Merchant;
import com.gtech.gvcore.dao.model.Outlet;
import com.gtech.gvcore.dao.model.Pos;
import com.gtech.gvcore.dao.model.UserAccount;
import com.gtech.gvcore.giftcard.infrastructure.entity.GiftCardEntity;
import com.gtech.gvcore.giftcard.infrastructure.mapper.GiftCardMapper;
import com.gtech.gvcore.service.report.BusinessReport;
import com.gtech.gvcore.service.report.PollReport;
import com.gtech.gvcore.service.report.extend.joindate.JoinDataMap;
import com.gtech.gvcore.service.report.extend.poll.ManualPollBusinessReport;
import com.gtech.gvcore.service.report.impl.bean.GcTransactionsDetailedBean;
import com.gtech.gvcore.service.report.impl.bo.GcTransactionDetailedBo;
import com.gtech.gvcore.service.report.impl.param.GcTransactionDetailQueryData;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.ibatis.session.RowBounds;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;


@Service
public class GcTransactionsDetailedImpl extends ManualPollBusinessReport<GcTransactionDetailQueryData, GcTransactionsDetailedBean, GcTransactionDetailedBo>
        implements BusinessReport<GcTransactionDetailQueryData, GcTransactionsDetailedBean>, PollReport {

    @Override
    public ReportExportTypeEnum exportTypeEnum() {
        return ReportExportTypeEnum.GC_TRANSACTIONS_DETAILED_SUMMARY_REPORT;
    }

    @Override
    public GcTransactionDetailQueryData builderQueryParam(CreateReportRequest reportParam) {
        GcTransactionDetailQueryData queryData = new GcTransactionDetailQueryData();

        // Set transaction date range
        queryData.setTransactionDateStart(reportParam.getTransactionDateStart());
        queryData.setTransactionDateEnd(reportParam.getTransactionDateEnd());
        
        // Set issuer codes
        queryData.setIssuerCodes(StringUtils.isNotBlank(reportParam.getIssuerCode()) ? 
            Collections.singletonList(reportParam.getIssuerCode()) : null);
        
        // Set merchant and outlet codes
        queryData.setMerchantCodes(reportParam.getMerchantCodes());
        queryData.setOutletCodes(reportParam.getOutletCodes());
        
        // Set CPG codes
        queryData.setCpgCodes(reportParam.getCpgCodes());
        
        // Set transaction types
        queryData.setTransactionType(reportParam.getTransactionTypes());
        queryData.setTransactionStatus(reportParam.getTransactionStatus());

        // Set invoice number
        queryData.setInvoiceNumber(reportParam.getInvoiceNo());
        
        // Set customer information
        queryData.setCustomerType(reportParam.getCustomerType());
        queryData.setCustomerCodes(reportParam.getCustomerCodes());

        // Set bulk order status
        queryData.setBulkOrderStatus(reportParam.getOrderStatuses());
        
        // Set card effective date range
        queryData.setCardEffectiveDateStart(reportParam.getVoucherEffectiveDateStart());
        queryData.setCardEffectiveDateEnd(reportParam.getVoucherEffectiveDateEnd());
        
        // Set card number range
        queryData.setCardNumberStart(reportParam.getVoucherCodeNumStart() != null ? 
            reportParam.getVoucherCodeNumStart().toString() : null);
        queryData.setCardNumberEnd(reportParam.getVoucherCodeNumEnd() != null ? 
            reportParam.getVoucherCodeNumEnd().toString() : null);

        return queryData;
    }

    @Override
    protected List<GcTransactionDetailedBo> findDate(GcTransactionDetailQueryData param, RowBounds rowBounds) {
        // Select gift card transaction detail report
        return gcReportBusinessMapper.gcTransactionDetailReport(param, rowBounds);
    }

    @Override
    public List<GcTransactionDetailedBo> filter(GcTransactionDetailQueryData param, List<GcTransactionDetailedBo> boList) {
        // Filter card status
        this.filterCardStatus(boList, param.getCardStatusList());
        return boList;
    }

    @Override
    protected List<GcTransactionsDetailedBean> getExportData(GcTransactionDetailQueryData param, List<GcTransactionDetailedBo> boList) {
        // Get related data maps
        final JoinDataMap<UserAccount> userAccountMap = super.getMapByCode(boList, GcTransactionDetailedBo::getCreateUser, UserAccount.class);
        final JoinDataMap<Merchant> merchantMap = super.getMapByCode(boList, GcTransactionDetailedBo::getMerchantCode, Merchant.class);
        final JoinDataMap<Outlet> outletMap = super.getMapByCode(boList, GcTransactionDetailedBo::getOutletCode, Outlet.class);
        final JoinDataMap<Pos> posMap = super.getMapByCode(boList, GcTransactionDetailedBo::getPosCode, Pos.class);

        if (CollectionUtils.isEmpty(boList)) {
            return Collections.emptyList();
        }

        // Convert to export data
        return boList.stream()
                .map(e -> new GcTransactionsDetailedBean()
                        .setTransactionType(getGcTransactionTypeDesc(e.getTransactionType()))
                        .setMerchant(merchantMap.findValue(e.getMerchantCode()).getMerchantName())
                        .setOutlet(outletMap.findValue(e.getOutletCode()).getOutletName())
                        .setTransactionDate(DateUtil.format(e.getTransactionDate(), DateUtil.FORMAT_DEFAULT))
                        .setCardNumber(e.getCardNumber())
                        .setInitiatedBy(e.getCreateUser(userAccountMap))
                        .setPosName(null != posMap.findValue(e.getPosCode()) && StringUtil.isNotEmpty(posMap.findValue(e.getPosCode()).getPosName()) ? 
                            posMap.findValue(e.getPosCode()).getPosName() : "GC POS")
                        .setTerminal(posMap.findValue(e.getPosCode()).getMachineId())
                        .setOrganizationName(merchantMap.findValue(e.getMerchantCode()).getMerchantName())
                        .setBatchNumber(e.getBatchCode())
                        .setLoginSource(e.getLoginSource())
                        .setTransactionAmount(toAmount(e.getTransactionAmount()))
                        .setBalanceBefore(toAmount(e.getBalanceBefore()))
                        .setBalanceAfter(toAmount(e.getBalanceAfter()))
                        .setActualOutlet(e.getActualOutlet())
                        .setForwardingEntityId(e.getForwardingEntityId())
                        .setResponseMessage(e.getResponseMessage())
                        .setTransactionMode(e.getTransactionMode())
                        .setCustomerSalutation(e.getCustomerSalutation())
                        .setCustomerFirstName(e.getCustomerFirstName())
                        .setCustomerLastName(e.getCustomerLastName())
                        .setMobile(e.getMobile())
                        .setInvoiceNumber(e.getInvoiceNumber())
                        .setTransactionReference(e.getTransactionReferenceNumber())
                        .setOtherInputParameter(e.getOtherInputParameter())
                ).collect(Collectors.toList());
    }

    /**
     * Get gift card transaction type description
     */
    private String getGcTransactionTypeDesc(String transactionType) {
        if (StringUtils.isBlank(transactionType)) {
            return "";
        }
        
        switch (transactionType.toUpperCase()) {
            case "ACTIVATION":
                return "GIFT CARD ACTIVATED";
            case "SALES":
                return "GIFT CARD SOLD";
            case "REDEMPTION":
                return "GIFT CARD REDEEMED";
            case "BLOCK":
                return "GIFT CARD BLOCKED";
            case "UNBLOCK":
                return "GIFT CARD UNBLOCKED";
            case "CANCEL_SALES":
                return "GIFT CARD SALES CANCELLED";
            case "CANCEL_ACTIVATION":
                return "GIFT CARD ACTIVATION CANCELLED";
            case "EXTEND_ACTIVATION":
                return "GIFT CARD ACTIVATION EXTENDED";
            default:
                return "GIFT CARD " + transactionType;
        }
    }

    /**
     * Filter card status
     */
    private void filterCardStatus(List<GcTransactionDetailedBo> boList, List<String> cardStatusList) {
        if (CollectionUtils.isEmpty(cardStatusList)) {
            return;
        }

        // Get gift card map with only status field
        JoinDataMap<GiftCardEntity> cardMap = super.getMapByCode(boList, GcTransactionDetailedBo::getCardNumber, GiftCardEntity.class,
                new String[]{"status"});

        // Filter card status
        boList.removeIf(transaction -> {
            GiftCardEntity giftCard = cardMap.get(transaction.getCardNumber());
            return giftCard != null && !cardStatusList.contains(giftCard.getStatus());
        });
    }
}
