package com.gtech.gvcore.common.response.gc;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

/**
 * Dynamic Barcode Response
 */
@Data
@ApiModel(value = "DynamicBarcodeResponse", description = "Dynamic Barcode Response")
public class DynamicBarcodeResponse extends BaseApiResponse {


    @ApiModelProperty(value = "Gift Card Number", notes = "16 digit gift card number")
    private String giftCardNumber;

    @ApiModelProperty(value = "Barcode", notes = "Dynamically generated barcode")
    private String barcode;

} 