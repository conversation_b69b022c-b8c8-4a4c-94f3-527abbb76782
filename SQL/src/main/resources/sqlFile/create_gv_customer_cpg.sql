-- 创建客户与CPG关联表
CREATE TABLE `gv_customer_cpg` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT 'ID',
  `customer_cpg_code` varchar(50) NOT NULL COMMENT '客户CPG关联编码',
  `customer_code` varchar(50) NOT NULL COMMENT '客户编码',
  `cpg_code` varchar(50) NOT NULL COMMENT 'CPG编码',
  `cpg_type` varchar(20) NOT NULL DEFAULT 'voucher' COMMENT '分类类型，可能的值：voucher, gc',
  `status` tinyint(4) NOT NULL DEFAULT '1' COMMENT '状态，0：禁用，1：启用',
  `create_user` varchar(50) DEFAULT NULL COMMENT '创建人',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_user` varchar(50) DEFAULT NULL COMMENT '更新人',
  `update_time` datetime DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `gvcc_customer_cpg_code` (`customer_cpg_code`),
  KEY `gvcc_customer_code` (`customer_code`),
  KEY `gvcc_cpg_code` (`cpg_code`),
  KEY `gvcc_cpg_type` (`cpg_type`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='客户与产品关联表'; 