package com.gtech.gvcore.giftcard.domain.service;

import org.springframework.stereotype.Component;
import java.util.Random;

/**
 * 条形码转换器
 * 将16位卡号转换为27位条形码
 * 条形码格式：PQRSTUABCDEFGHIJKLMNOPVWXYZ (27位纯数字)
 * P = 礼品卡类型固定值"4"
 * Q = 钱包主机标识(与卡号第1位相同)
 * R = 内部编码固定值"9"
 * S = 概念标识符第一位(与卡号第2位相同)
 * T = 概念标识符第二位(与卡号第3位相同)
 * U = 条形码版本号"1"
 * ABCDEFGHIJKLMNOP = 原始16位卡号
 * V = 初始状态码"0"
 * WX = 两位校验码(基于前23位计算)
 * YZ = 两位面额代码
 */
@Component
public class BarcodeConverter {
    
    private static final Random RANDOM = new Random();
    
    /**
     * 将16位卡号转换为27位条形码
     * 
     * @param cardNumber 16位卡号（纯数字）
     * @return 27位条形码（纯数字）
     */
    public String toBarcode(String cardNumber) {
        if (cardNumber == null || cardNumber.length() != 16) {
            throw new IllegalArgumentException("卡号必须是16位");
        }
        
        if (!isNumeric(cardNumber)) {
            throw new IllegalArgumentException("卡号必须是纯数字");
        }
        
        char[] barcodeChars = new char[27];
        
        barcodeChars[0]  = cardNumber.charAt(14);
        barcodeChars[1]  = cardNumber.charAt(15);
        barcodeChars[2]  = (char) ('0' + RANDOM.nextInt(10));
        barcodeChars[3]  = cardNumber.charAt(4);
        barcodeChars[4]  = cardNumber.charAt(5);
        barcodeChars[5]  = (char) ('0' + RANDOM.nextInt(10));
        barcodeChars[6]  = cardNumber.charAt(2);
        barcodeChars[7]  = cardNumber.charAt(3);
        barcodeChars[8]  = (char) ('0' + RANDOM.nextInt(10));
        barcodeChars[9]  = cardNumber.charAt(0);
        barcodeChars[10] = cardNumber.charAt(1);
        barcodeChars[11] = cardNumber.charAt(8);
        barcodeChars[12] = (char) ('0' + RANDOM.nextInt(10));
        barcodeChars[13] = cardNumber.charAt(9);
        barcodeChars[14] = (char) ('0' + RANDOM.nextInt(10));
        barcodeChars[15] = cardNumber.charAt(10);
        barcodeChars[16] = (char) ('0' + RANDOM.nextInt(10));
        barcodeChars[17] = cardNumber.charAt(11);
        barcodeChars[18] = (char) ('0' + RANDOM.nextInt(10));
        barcodeChars[19] = cardNumber.charAt(12);
        barcodeChars[20] = (char) ('0' + RANDOM.nextInt(10));
        barcodeChars[21] = cardNumber.charAt(13);
        barcodeChars[22] = (char) ('0' + RANDOM.nextInt(10));
        barcodeChars[23] = (char) ('0' + RANDOM.nextInt(10));
        barcodeChars[24] = (char) ('0' + RANDOM.nextInt(10));
        barcodeChars[25] = cardNumber.charAt(6);
        barcodeChars[26] = cardNumber.charAt(7);
        
        String result = new String(barcodeChars);
        
        if (!isNumeric(result)) {
            throw new IllegalStateException("生成的条形码包含非数字字符");
        }
        
        return result;
    }
    
    /**
     * 从27位条形码提取16位卡号
     * 
     * @param barcode 27位条形码（纯数字）
     * @return 16位卡号（纯数字）
     */
    public String extractCardNumber(String barcode) {
        if (barcode == null || barcode.length() != 27) {
            throw new IllegalArgumentException("条形码必须是27位");
        }
        
        if (!isNumeric(barcode)) {
            throw new IllegalArgumentException("条形码必须是纯数字");
        }
        
        char[] cardNumberChars = new char[16];
        
        cardNumberChars[0]  = barcode.charAt(9);
        cardNumberChars[1]  = barcode.charAt(10);
        cardNumberChars[2]  = barcode.charAt(6);
        cardNumberChars[3]  = barcode.charAt(7);
        cardNumberChars[4]  = barcode.charAt(3);
        cardNumberChars[5]  = barcode.charAt(4);
        cardNumberChars[6]  = barcode.charAt(25);
        cardNumberChars[7]  = barcode.charAt(26);
        cardNumberChars[8]  = barcode.charAt(11);
        cardNumberChars[9]  = barcode.charAt(13);
        cardNumberChars[10] = barcode.charAt(15);
        cardNumberChars[11] = barcode.charAt(17);
        cardNumberChars[12] = barcode.charAt(19);
        cardNumberChars[13] = barcode.charAt(21);
        cardNumberChars[14] = barcode.charAt(0);
        cardNumberChars[15] = barcode.charAt(1);
        
        return new String(cardNumberChars);
    }
    
    /**
     * 检查字符串是否为纯数字
     */
    private boolean isNumeric(String str) {
        if (str == null) {
            return false;
        }
        for (char c : str.toCharArray()) {
            if (!Character.isDigit(c)) {
                return false;
            }
        }
        return true;
    }
} 