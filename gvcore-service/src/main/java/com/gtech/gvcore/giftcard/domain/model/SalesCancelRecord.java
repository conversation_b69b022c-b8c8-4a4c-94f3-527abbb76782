package com.gtech.gvcore.giftcard.domain.model;

import lombok.Getter;
import java.time.LocalDateTime;
import java.util.Date;
import java.util.UUID;

/**
 * 礼品卡销售取消记录
 */
@Getter
public class SalesCancelRecord {
    private final String issuerCode;
    private final String cpgCode;
    private final String merchantCode;
    private final String invoiceNumber;
    private final String approvalCode;
    private final String cancelCode;
    private final String outletCode;
    private final String cardNumber;
    private final String cancelReason;
    private final Date cancelTime;
    
    /**
     * 创建销售取消记录
     */
    public SalesCancelRecord(
            String issuerCode,
            String outletCode,
            String merchantCode,
            String cancelCode,
            String invoiceNumber,
            String approvalCode,
            String cpgCode,
            String cardNumber,
            String cancelReason
    ) {
        this.issuerCode = issuerCode;
        this.outletCode = outletCode;
        this.merchantCode = merchantCode;
        this.invoiceNumber = invoiceNumber;
        this.approvalCode = approvalCode;
        this.cpgCode = cpgCode;
        this.cancelCode = cancelCode;
        this.cardNumber = cardNumber;
        this.cancelReason = cancelReason;
        this.cancelTime = new Date();
    }
    

} 