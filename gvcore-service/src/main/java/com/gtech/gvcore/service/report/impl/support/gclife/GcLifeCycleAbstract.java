package com.gtech.gvcore.service.report.impl.support.gclife;

import com.gtech.commons.utils.BeanCopyUtils;
import com.gtech.gvcore.giftcard.domain.service.GcDomainService;
import com.gtech.gvcore.service.report.base.ReportSupport;
import com.gtech.gvcore.service.report.impl.param.GcLifeCycleQueryData;
import com.gtech.gvcore.service.report.impl.support.gclife.excel.GcLifeCycleSheet;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.ArrayList;
import java.util.List;

/**
 * @ClassName GcLifeCycleAbstract
 * @Description Gift Card Life Cycle Abstract Class
 * <AUTHOR> based on VoucherLifeCycleAbstract
 * @Date 2025年6月19日
 * @Version V1.0
 **/
public abstract class GcLifeCycleAbstract<T> extends ReportSupport implements GcLifeCycle {

    @Autowired 
    protected GcDomainService gcDomainService;

    @Override
    public GcLifeCycleSheet builderSheet(GcLifeCycleQueryData param) {

        return GcLifeCycleSheet.newSheet(getFillKey(), exportTypeEnum(), builderSheetData(param));
    }

    private List<T> builderSheetData(GcLifeCycleQueryData param) {

        final Long cardNumberBegin = param.getCardNumberStart();
        final Long cardNumberEnd = param.getCardNumberEnd();

        List<T> list = new ArrayList<>();

        //单个卡号
        if (null == cardNumberEnd) {
            return builder(param);
        }

        //都存在值
        for (long i = cardNumberBegin; i <= cardNumberEnd; i++) {
            GcLifeCycleQueryData reportBasicQueryData = BeanCopyUtils.jsonCopyBean(param, GcLifeCycleQueryData.class);
            reportBasicQueryData.setCardNumberStart(i);
            reportBasicQueryData.setCardNumber(String.valueOf(i));
            reportBasicQueryData.setCardNumberEnd(null);
            list.addAll(builder(reportBasicQueryData));
        }

        return list;
    }

    protected abstract List<T> builder(GcLifeCycleQueryData queryData);

    protected abstract String getFillKey();

}
