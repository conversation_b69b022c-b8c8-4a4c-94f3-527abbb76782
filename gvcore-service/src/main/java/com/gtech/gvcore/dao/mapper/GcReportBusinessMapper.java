package com.gtech.gvcore.dao.mapper;

import com.gtech.gvcore.service.report.impl.bo.*;
import com.gtech.gvcore.service.report.impl.param.*;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.session.RowBounds;

import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * @ClassName ReportMapper
 * @Description report mapper
 * <AUTHOR>
 * @Date 2023/1/5 10:20
 * @Version V1.0
 **/
@Mapper
public interface GcReportBusinessMapper {
    List<GcSalesBo> gcSalesReport(GcSalesQueryData salesDetailed, @Param("rowBounds") RowBounds rowBounds);

    List<GcRedemptionBo> selectGcRedemption(RedemptionQueryData request, @Param("rowBounds") RowBounds rowBounds);

    List<GcCancelSalesBo> selectCancelSales(CancelSalesQueryData cancelSalesDetail, @Param("rowBounds") RowBounds rowBounds);

    List<DeactivatedBo> selectGcDeactivated(DeactivatedQueryData param, @Param("rowBounds") RowBounds rowBounds);

    List<GcReactivatedBo> selectGcReactivated(GcReactivatedQueryData param, @Param("rowBounds") RowBounds rowBounds);

    List<GcExpiryBo> selectGcExpiry(GcExpiryQueryData param, @Param("rowBounds") RowBounds rowBounds);

    List<GcExtendExpiryBo> selectGcExtendExpiry(GcExtendExpiryQueryData param, @Param("rowBounds") RowBounds rowBounds);

    // Gift card transaction time queries
    Date getLatestGcActivationTime(@Param("cardNumber") String cardNumber);
    Date getLatestGcSalesTime(@Param("cardNumber") String cardNumber);
    Date getLatestGcRedemptionTime(@Param("cardNumber") String cardNumber);
    Date getLatestGcBlockTime(@Param("cardNumber") String cardNumber);
    Date getLatestGcUnblockTime(@Param("cardNumber") String cardNumber);
    Date getLatestGcCancelSalesTime(@Param("cardNumber") String cardNumber);
    Date getLatestGcCancelActivationTime(@Param("cardNumber") String cardNumber);
    Date getLatestGcExtendActivationTime(@Param("cardNumber") String cardNumber);

    // Get card to merchant/outlet mapping from sales records
    List<Map<String, Object>> getCardToMerchantMapFromGcSales(@Param("cardNumbers") List<String> cardNumbers);
    List<Map<String, Object>> getCardToOutletMapFromGcSales(@Param("cardNumbers") List<String> cardNumbers);

    // Gift card transaction detail report
    List<GcTransactionDetailedBo> gcTransactionDetailReport(GcTransactionDetailQueryData param, @Param("rowBounds") RowBounds rowBounds);

}
