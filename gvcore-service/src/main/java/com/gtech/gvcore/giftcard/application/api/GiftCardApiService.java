package com.gtech.gvcore.giftcard.application.api;

import com.alibaba.fastjson.JSON;
import com.gtech.commons.exception.GTechBaseException;
import com.gtech.commons.page.PageData;
import com.gtech.commons.result.Result;
import com.gtech.commons.utils.BeanCopyUtils;
import com.gtech.commons.utils.DateUtil;
import com.gtech.commons.utils.StringUtil;
import com.gtech.gvcore.cache.MasterDataCache;
import com.gtech.gvcore.common.enums.*;
import com.gtech.gvcore.common.enums.GiftCardResponseCodesEnum;
import com.gtech.gvcore.common.request.customerorder.CreateCustomerOrderDetailsRequest;
import com.gtech.gvcore.common.request.customerorder.CreateCustomerOrderRequest;
import com.gtech.gvcore.common.request.gcapi.*;
import com.gtech.gvcore.common.request.transaction.BatchcloseRequest;
import com.gtech.gvcore.common.response.gc.*;
import com.gtech.gvcore.common.response.transaction.BatchcloseResponse;
import com.gtech.gvcore.dao.model.Outlet;
import com.gtech.gvcore.dao.model.Pos;
import com.gtech.gvcore.giftcard.application.dto.ActivationDTO;
import com.gtech.gvcore.giftcard.application.dto.ExtensionDTO;
import com.gtech.gvcore.giftcard.application.dto.GcSalesDTO;
import com.gtech.gvcore.giftcard.application.dto.GiftCardDTO;
import com.gtech.gvcore.giftcard.application.dto.RedemptionDTO;
import com.gtech.gvcore.giftcard.domain.model.*;
import com.gtech.gvcore.giftcard.domain.service.*;
import com.gtech.gvcore.giftcard.domain.model.GcActivationExtensionRecord;
import com.gtech.gvcore.giftcard.domain.model.GcBlockRecord;
import com.gtech.gvcore.giftcard.domain.model.GcUnblockRecord;
import com.gtech.gvcore.giftcard.domain.model.SalesCancelRecord;
import com.gtech.gvcore.giftcard.masterdata.gcpg.dto.GcCpgDTO;
import com.gtech.gvcore.giftcard.masterdata.gcpg.entity.GcCpg;
import com.gtech.gvcore.giftcard.masterdata.gcpg.service.GcCpgService;
import com.gtech.gvcore.helper.GvCodeHelper;
import com.gtech.gvcore.service.CustomerOrderService;
import com.gtech.gvcore.service.TransactionDataService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Component;
import com.gtech.gvcore.common.exception.GvBusinessException;
import java.text.SimpleDateFormat;
import java.util.Date;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;

@Slf4j
@Component
public class GiftCardApiService {

    @Autowired
    private GcDomainService gcDomainService;

    @Autowired
    private GcCpgService gcCpgService;

    @Autowired
    private GcSalesDomainService gcSalesDomainService;

    @Autowired
    private MasterDataCache masterDataCache;

    @Autowired
    private GcActivateDomainService gcActivateDomainService;

    @Autowired
    private GcExtensionDomainService gcExtensionDomainService;

    @Autowired
    private GcBlockDomainService gcBlockDomainService;

    @Autowired
    private GvCodeHelper gvCodeHelper;

    @Autowired
    private GcRedemptionDomainService gcRedemptionDomainService;

    @Autowired
    private TransactionDataService transactionDataService;

    @Autowired
    private RedisTemplate redisTemplate;

    @Autowired
    private CustomerOrderService customerOrderService;

    @Autowired
    private BarcodeConverter barcodeConverter;

    public static final String API_CUSTOMER = "api customer";


    public GcIssuanceResponse gcIssuance(GcIssuanceRequest request, String terminalId, String batchNumber) {
        // Validate required fields
        if (request.getTransactionCode() == null) {
            throw new GvBusinessException(GiftCardResponseCodesEnum.TRANSACTION_CODE_IS_NULL);
        }
        if (request.getInputType() == null || request.getInputType().trim().isEmpty()) {
            throw new GvBusinessException(GiftCardResponseCodesEnum.INPUT_TYPE_IS_NULL);
        }
        if (request.getNumberOfGiftCards() == null) {
            throw new GvBusinessException(GiftCardResponseCodesEnum.NUMBER_OF_GIFT_CARDS_IS_NULL);
        }
        if (request.getInvoiceNumber() == null || request.getInvoiceNumber().trim().isEmpty()) {
            throw new GvBusinessException(GiftCardResponseCodesEnum.INVOICE_NUMBER_IS_NULL);
        }
        if (request.getGcpg() == null || request.getGcpg().trim().isEmpty()) {
            throw new GvBusinessException(GiftCardResponseCodesEnum.GCPG_IS_NULL);
        }

        // Validate numberOfGiftCards limit (performance consideration)
        if (request.getNumberOfGiftCards() > 100) { // Assuming 100 as max limit
            throw new GvBusinessException(GiftCardResponseCodesEnum.NUMBER_OF_GIFT_CARDS_EXCEEDED);
        }

        // Validate invoiceDate format if provided
        if (request.getInvoiceDate() != null ) {
            if (!isValidDateTimeFormat(request.getInvoiceDate())) {
                throw new GvBusinessException(GiftCardResponseCodesEnum.INVOICE_DATE_FORMAT_INVALID);
            }
        }

        Pos pos = getPosAndValidate(terminalId);
        Outlet outlet = getOutletAndValidate(pos.getOutletCode());
        GcCpgDTO cpgDto = getGcCpgDtoAndValidate(request.getGcpg());

        // Validate POS-CPG authorization
        validatePosCpg(pos, cpgDto);

        String customerOrderCode = gvCodeHelper.generateCustomerOrderCode();
        Integer transactionId = request.getTransactionCode();
        List<String> giftCardNumbers;

        String invoiceNumber = StringUtil.isNotEmpty(request.getInvoiceNumber())? request.getInvoiceNumber() : gvCodeHelper.generateInvoiceNumber();
        try {
            giftCardNumbers = gcDomainService.batchCreateGiftCards(customerOrderCode, cpgDto.getCpgCode(), cpgDto.getDenomination(), request.getNumberOfGiftCards(), cpgDto.getIssuerCode());
            if (giftCardNumbers == null || giftCardNumbers.isEmpty()) {
                log.error("gcIssuance: batchCreateGiftCards returned null or empty list for transactionId: {}", transactionId);
                throw new GvBusinessException(
                    GvPosCommonResponseCodesEnum.TRANSACTION_FAILED.getResponseCode(),
                    GvPosCommonResponseCodesEnum.TRANSACTION_FAILED.getResponseMessage()
                );
            }
        } catch (GvBusinessException e) {
            throw e;
        } catch (Exception e) {
            log.error("gcIssuance: Error during batchCreateGiftCards for transactionId: {}. Error: {}", transactionId, e.getMessage(), e);
            throw new GvBusinessException(
                String.valueOf(GvPosCommonResponseCodesEnum.TRANSACTION_FAILED.getResponseCode()),
                GvPosCommonResponseCodesEnum.TRANSACTION_FAILED.getResponseMessage(),
                e
            );
        }

        GcSalesDTO gcSalesDTO = new GcSalesDTO();
        gcSalesDTO.setIssuerCode(cpgDto.getIssuerCode());
        gcSalesDTO.setMerchantCode(outlet.getMerchantCode());
        gcSalesDTO.setOutletCode(outlet.getOutletCode());
        gcSalesDTO.setInvoiceNumber(invoiceNumber);
        gcSalesDTO.setApprovalCode(gvCodeHelper.generateApproveCode());
        gcSalesDTO.setCustomerOrderCode(customerOrderCode);
        gcSalesDTO.setSalesTime(new Date());
        gcSalesDTO.setCardNumber(giftCardNumbers);
        gcSalesDTO.setCpgCode(cpgDto.getCpgCode());
        gcSalesDTO.setDenomination(cpgDto.getDenomination());
        gcSalesDTO.setCustomerCode(request.getBuyer() != null ? request.getBuyer().getCustomerId() : null);
        gcSalesDTO.setNotes(request.getNotes());
        gcSalesDTO.setBatchNumber(batchNumber);


        CreateCustomerOrderDetailsRequest createCustomerOrderDetailsRequest = new CreateCustomerOrderDetailsRequest();
        createCustomerOrderDetailsRequest.setCpgCode(cpgDto.getCpgCode());
        createCustomerOrderDetailsRequest.setVoucherNum(request.getNumberOfGiftCards());
        createCustomerOrderDetailsRequest.setDenomination(cpgDto.getDenomination());

        try {
            gcSalesDomainService.recordSales(gcSalesDTO);
            gcDomainService.sellGiftCard(gcSalesDTO);
            this.createCustomerOrder(invoiceNumber,
                    outlet,
                    request.getBuyer(),
                    cpgDto.getIssuerCode(),
                    customerOrderCode,
                    request.getNotes(),
                    request.getNumberOfGiftCards(),
                    Collections.singletonList(createCustomerOrderDetailsRequest),
                    cpgDto.getDenomination().multiply(new BigDecimal(request.getNumberOfGiftCards()))
                    );
        } catch (GvBusinessException e) {
            log.error("gcIssuance: Business error during sales record/sell status update for transactionId: {}. Error: {}", transactionId, e.getCustomMessage());
            throw e;
        } catch (Exception e) {
            log.error("gcIssuance: Error during sales record or sell status update for transactionId: {}. Error: {}", transactionId, e.getMessage(), e);
            throw new GvBusinessException(
                String.valueOf(GvPosCommonResponseCodesEnum.TRANSACTION_FAILED.getResponseCode()),
                GvPosCommonResponseCodesEnum.TRANSACTION_FAILED.getResponseMessage(),
                e
            );
        }

        GcIssuanceResponse response = new GcIssuanceResponse();
        response.setResponseCode(GiftCardResponseCodesEnum.TRANSACTION_SUCCESS.getResponseCode());
        response.setResponseMessage(GiftCardResponseCodesEnum.TRANSACTION_SUCCESS.getResponseMessage());
        response.setTransactionCode(306);
        response.setInputType("4");
        response.setNumberOfGiftCards(giftCardNumbers.size());
        response.setInvoiceNumber(request.getInvoiceNumber());
        response.setInvoiceDate(new Date()); 
        response.setBuyer(request.getBuyer());
        response.setNotes(request.getNotes());
        response.setRetryKey(request.getRetryKey());
        response.setGcpg(request.getGcpg());
        response.setSource("POS");
        response.setTransactionDate(gcSalesDTO.getSalesTime());
        response.setBatchNumber(batchNumber.substring(batchNumber.length()-4));
        response.setStoreCode(outlet.getOutletCode());
        response.setStoreName(outlet.getOutletName());
        try {
            response.setGiftCards(getGiftCardInfo(customerOrderCode, cpgDto));
        } catch (GvBusinessException e) {
             log.warn("gcIssuance: Business exception while fetching gift card info for response: {}", e.getCustomMessage());
        } catch (Exception e) {
             log.error("gcIssuance: Error fetching gift card info for response: {}", e.getMessage(), e);
        }
        
        return response;
    }

    @Transactional
    public ActivateGiftCardResponse activateCard(ActivateCardRequest request, String terminalId,String batchNumber) {
        // Validate required fields
        if (request.getGiftCardNumber() == null || request.getGiftCardNumber().trim().isEmpty()) {
            throw new GvBusinessException(GiftCardResponseCodesEnum.GIFT_CARD_NUMBER_IS_NULL);
        }
        if (request.getGiftCardPIN() == null || request.getGiftCardPIN().trim().isEmpty()) {
            throw new GvBusinessException(GiftCardResponseCodesEnum.GIFT_CARD_PIN_IS_NULL);
        }
        if (request.getGiftCardActivationExpiry() == null) {
            throw new GvBusinessException(GiftCardResponseCodesEnum.GIFT_CARD_EXPIRY_IS_NULL);
        }
        if (request.getCustomer() == null) {
            throw new GvBusinessException(GiftCardResponseCodesEnum.CUSTOMER_IS_NULL);
        }

        Pos pos = getPosAndValidate(terminalId);
        GiftCard giftCardToActivate = getGiftCardAndValidate(request.getGiftCardNumber());

        // Validate PIN matches
        if (!request.getGiftCardPIN().equals(giftCardToActivate.getPinCode())) {
            throw new GvBusinessException(GiftCardResponseCodesEnum.GIFT_CARD_PIN_NOT_MATCHING);
        }

        // Validate activation code if provided
        if (request.getActivationCode() != null && !request.getActivationCode().trim().isEmpty()) {
            if (!request.getActivationCode().equals(giftCardToActivate.getActivationCode())) {
                throw new GvBusinessException(GiftCardResponseCodesEnum.ACTIVATION_CODE_NOT_MATCHING);
            }
        }

        GcCpg cpg = getCpgEntityAndValidate(giftCardToActivate.getCpgCode(), giftCardToActivate.getCardNumber());
        GcCpgDTO cpgDto = BeanCopyUtils.jsonCopyBean(cpg, GcCpgDTO.class);

        // Validate POS-CPG authorization
        validatePosCpg(pos, cpgDto);

        ActivationDTO record = new ActivationDTO();
        record.setCardNumber(giftCardToActivate.getCardNumber());
        record.setActivationCode(request.getActivationCode());
        record.setInvoiceNumber(gvCodeHelper.generateInvoiceNumber());
        record.setOutletCode(pos.getOutletCode());
        record.setApprovalCode(gvCodeHelper.generateApproveCode());
        record.setNotes(request.getNotes());

        GiftCard activatedGiftCard;
        try {
            activatedGiftCard = gcActivateDomainService.activateGiftCard(record);
        } catch (GvBusinessException e) { 
            throw e;
        } catch (IllegalArgumentException e) { 
            log.warn("activateCard: Activation failed for card {} due to: {}", request.getGiftCardNumber(), e.getMessage());
            if (e.getMessage() != null) {
                if (e.getMessage().toLowerCase().contains("already active")) {
                    throw new GvBusinessException(GvPosCommonResponseCodesEnum.CARD_ALREADY_ACTIVE.getResponseCode(), GvPosCommonResponseCodesEnum.CARD_ALREADY_ACTIVE.getResponseMessage());
                }
            }
            throw new GvBusinessException(
                String.valueOf(GvPosCommonResponseCodesEnum.TRANSACTION_FAILED.getResponseCode()), 
                GvPosCommonResponseCodesEnum.TRANSACTION_FAILED.getResponseMessage(),
                e);
        }
        catch (Exception e) { 
            log.error("activateCard: Unexpected error during activation for card {}: {}", request.getGiftCardNumber(), e.getMessage(), e);
            throw new GvBusinessException(
                String.valueOf(GvPosCommonResponseCodesEnum.TRANSACTION_FAILED.getResponseCode()), 
                GvPosCommonResponseCodesEnum.TRANSACTION_FAILED.getResponseMessage(),
                e
            );
        }

        if (activatedGiftCard == null) { 
            log.error("activateCard: gcActivateDomainService.activateGiftCard returned null for card {}", request.getGiftCardNumber());
            throw new GvBusinessException(
                GvPosCommonResponseCodesEnum.TRANSACTION_FAILED.getResponseCode(), 
                GvPosCommonResponseCodesEnum.TRANSACTION_FAILED.getResponseMessage()
                );
        }

        CustomerInfo customer = request.getCustomer();
        if (customer != null) {
            GcHolder gcHolder = new GcHolder();
            gcHolder.setCardNumber(activatedGiftCard.getCardNumber());
            gcHolder.setHolderName(customer.getCustomerName());
            gcHolder.setHolderEmail(customer.getEmail());
            gcHolder.setHolderAddress(customer.getAddressLine1());
            try {
                gcDomainService.bindCardholder(gcHolder);
            } catch (Exception e) { 
                log.warn("activateCard: Failed to bind cardholder for card {}. Activation itself succeeded. Error: {}", activatedGiftCard.getCardNumber(), e.getMessage());
                 throw new GvBusinessException(
                     String.valueOf(GvPosCommonResponseCodesEnum.TRANSACTION_FAILED.getResponseCode()), 
                     GvPosCommonResponseCodesEnum.TRANSACTION_FAILED.getResponseMessage(), 
                     e
                 );
            }
        }
        // Use the CPG already retrieved for validation


        ActivateGiftCardResponse activateGiftCardResponse = new ActivateGiftCardResponse();
        activateGiftCardResponse.setResponseCode(GiftCardResponseCodesEnum.TRANSACTION_SUCCESS.getResponseCode());
        activateGiftCardResponse.setResponseMessage(GiftCardResponseCodesEnum.TRANSACTION_SUCCESS.getResponseMessage());
        
        GiftCardInfo giftCardInfo = new GiftCardInfo();
        giftCardInfo.setGiftCardNumber(activatedGiftCard.getCardNumber());
        giftCardInfo.setGiftCardPIN(activatedGiftCard.getPinCode());
        giftCardInfo.setGiftCardStatus(activatedGiftCard.getStatus().name());
        giftCardInfo.setItemNo("1");
        //giftCardInfo.setCurrency(cpg.getCurrencyCode());
        giftCardInfo.setDenomination(activatedGiftCard.getDenomination());
        giftCardInfo.setApprovalCode("");
        giftCardInfo.setGcpg(cpg.getCpgName());
        giftCardInfo.setIssuer(activatedGiftCard.getIssuerCode());
        giftCardInfo.setGiftCardExpiry(activatedGiftCard.getExpiryTime());
        giftCardInfo.setTransactionDate(activatedGiftCard.getActivationTime() != null ? activatedGiftCard.getActivationTime() : new Date());
        giftCardInfo.setArticleCode(cpg.getArticleMopCode());
        giftCardInfo.setMopCode("GC");
        giftCardInfo.setActivationCode(activatedGiftCard.getActivationCode());
        activateGiftCardResponse.setGiftCards(giftCardInfo);
        
        activateGiftCardResponse.setTransactionDate(new Date());
        activateGiftCardResponse.setBatchNumber(batchNumber.substring(batchNumber.length()-4));
        activateGiftCardResponse.setSource("POS");
        activateGiftCardResponse.setNotes("");
        
        return activateGiftCardResponse;
    }

    @Transactional
    public CardRedeemResponse cardRedemption(CardRedeemRequest request,String terminalId ,String batchNumber) {
        // Validate required fields
        if (request.getGiftCards() == null || request.getGiftCards().isEmpty()) {
            throw new GvBusinessException(GiftCardResponseCodesEnum.GIFT_CARDS_IS_NULL);
        }
        if (request.getTransactionAmount() == null) {
            throw new GvBusinessException(GiftCardResponseCodesEnum.TRANSACTION_AMOUNT_IS_NULL);
        }
        if (request.getInvoiceNumber() == null || request.getInvoiceNumber().trim().isEmpty()) {
            throw new GvBusinessException(GiftCardResponseCodesEnum.INVOICE_NUMBER_IS_NULL);
        }
        if (request.getTransactionId() == null) {
            throw new GvBusinessException(GiftCardResponseCodesEnum.TRANSACTION_ID_IS_NULL);
        }
        if (request.getClientTime() == null) {
            throw new GvBusinessException(GiftCardResponseCodesEnum.CLIENT_TIME_IS_NULL);
        }

        // Validate clientTime format
        if (!isValidDateTimeFormat(request.getClientTime())) {
            throw new GvBusinessException(GiftCardResponseCodesEnum.CLIENT_TIME_FORMAT_INVALID);
        }

        Pos pos = getPosAndValidate(terminalId);
        Outlet outlet = getOutletAndValidate(pos.getOutletCode());

        List<GiftCardInfo> giftCards = request.getGiftCards();

        // Validate each gift card in the request
        for (GiftCardInfo giftCardInfo : giftCards) {
            if (giftCardInfo.getGiftCardNumber() == null || giftCardInfo.getGiftCardNumber().trim().isEmpty()) {
                throw new GvBusinessException(GiftCardResponseCodesEnum.GIFT_CARD_NUMBER_IN_GIFT_CARDS_DOES_NOT_EXIST);
            }
            if (giftCardInfo.getRedemptionAmount() == null || giftCardInfo.getRedemptionAmount().compareTo(BigDecimal.ZERO) <= 0) {
                throw new GvBusinessException(GiftCardResponseCodesEnum.REDEMPTION_AMOUNT_NOT_ALLOWED);
            }
        }

        Map<String, GiftCardInfo> cardInfoMap = giftCards.stream().collect(Collectors.toMap(GiftCardInfo::getGiftCardNumber, x -> x));

        List<String> giftCardNumbers = giftCards.stream().map(GiftCardInfo::getGiftCardNumber).collect(Collectors.toList());

        List<GiftCard> giftCard = queryGiftCardAndValidate(giftCardNumbers);

        HashMap<String, BigDecimal> oldBalanceMap = new HashMap<>();

        List<RedemptionDTO> redemptionDTOS = new ArrayList<>();

        String approvalCode = gvCodeHelper.generateApproveCode();

        List<GiftCardInfo> responseGiftCardsList = new ArrayList<>();

        for (GiftCard card : giftCard) {

            GiftCardInfo giftCardInfo = cardInfoMap.get(card.getCardNumber());


            if (!card.checkActivated()) {
                throw new GvBusinessException(GiftCardResponseCodesEnum.CARD_STATUS_NOT_ALLOWED);
            }

            if (card.getBalance().compareTo(giftCardInfo.getRedemptionAmount()) < 0) {
                throw new GvBusinessException(GiftCardResponseCodesEnum.REDEMPTION_AMOUNT_NOT_ALLOWED);
            }

            BigDecimal oldBalance = new BigDecimal(String.valueOf(card.getBalance()));
            card.deduct(giftCardInfo.getRedemptionAmount());

            if (card.getBalance().compareTo(BigDecimal.ZERO) == 0) {
                card.setStatus(GcCardStatus.ZERO_BALANCE);
            }
            oldBalanceMap.put(card.getCardNumber(), oldBalance);

            GcCpg cpg = getCpgEntityAndValidate(card.getCpgCode(), card.getCardNumber());

            RedemptionDTO redemptionDTO = new RedemptionDTO();
            redemptionDTO.setCardNumber(card.getCardNumber());
            redemptionDTO.setAmount(giftCardInfo.getRedemptionAmount());
            redemptionDTO.setOutletCode(outlet.getOutletCode());
            redemptionDTO.setIssuerCode(card.getIssuerCode());
            redemptionDTO.setMerchantCode(outlet.getMerchantCode());
            redemptionDTO.setTerminalId(terminalId);
            redemptionDTO.setInvoiceNumber(request.getInvoiceNumber());
            redemptionDTO.setApprovalCode(approvalCode);
            redemptionDTO.setStatus("SUCCESS");
            redemptionDTO.setNotes(request.getNotes());
            redemptionDTO.setRedemptionTime(new Date());
            redemptionDTO.setBalanceBefore(oldBalance);
            redemptionDTO.setBalanceAfter(card.getBalance());
            redemptionDTO.setBatchNumber(batchNumber);
            redemptionDTO.setCpgCode(card.getCpgCode());
            redemptionDTOS.add(redemptionDTO);

            GiftCardInfo processedGcResponseInfo = new GiftCardInfo();
            processedGcResponseInfo.setItemNo("1");
            processedGcResponseInfo.setGiftCardNumber(card.getCardNumber());
            //processedGcResponseInfo.setCurrency(cpg.getCurrencyCode());
            processedGcResponseInfo.setGiftCardStatus(card.getStatus().name());
            processedGcResponseInfo.setGiftCardStatusId(card.getStatus().name());
            processedGcResponseInfo.setRemainingBalance(card.getBalance());
            processedGcResponseInfo.setDenomination(card.getDenomination());
            processedGcResponseInfo.setApprovalCode(approvalCode);
            processedGcResponseInfo.setGcpg(cpg.getCpgName());
            processedGcResponseInfo.setIssuer(card.getIssuerCode());
            processedGcResponseInfo.setGiftCardExpiry(card.getExpiryTime());
            processedGcResponseInfo.setBalanceExpiry(card.getExpiryTime());
            processedGcResponseInfo.setInquiryDate(new Date());
            processedGcResponseInfo.setArticleCode(cpg.getArticleMopCode());
            processedGcResponseInfo.setMopCode("GC");
            processedGcResponseInfo.setActivationCode(card.getActivationCode());

            List<GiftCardInfo.GiftCardHistory> historyList = new ArrayList<>();
            GiftCardInfo.GiftCardHistory currentRedemptionTx = new GiftCardInfo.GiftCardHistory();
            currentRedemptionTx.setInvoiceNumber(request.getInvoiceNumber());
            currentRedemptionTx.setTransactionDate(new Date());
            currentRedemptionTx.setStoreCode(outlet.getOutletCode());
            currentRedemptionTx.setStoreName(outlet.getOutletName());
            currentRedemptionTx.setTransactionType("GIFT CARD REDEEM");
            currentRedemptionTx.setTransactionAmount(cardInfoMap.get(card.getCardNumber()).getRedemptionAmount());
            currentRedemptionTx.setNotes(request.getNotes());
            historyList.add(currentRedemptionTx);
            processedGcResponseInfo.setGiftCardHistory(historyList);
            responseGiftCardsList.add(processedGcResponseInfo);

        }

        try {
            int update = gcDomainService.update(giftCard, oldBalanceMap);
            if (update == 0) { 
                throw new GvBusinessException(
                        GvPosCommonResponseCodesEnum.TRANSACTION_FAILED.getResponseCode(), 
                        GvPosCommonResponseCodesEnum.TRANSACTION_FAILED.getResponseMessage() 
                );
            }
        } catch (Exception ex) {
            log.error("cardRedemption: Error updating gift card balances for transactionId: {}. Error: {}", request.getTransactionId(), ex.getMessage(), ex);
            throw new GvBusinessException(
                    String.valueOf(GvPosCommonResponseCodesEnum.TRANSACTION_FAILED.getResponseCode()),
                    GvPosCommonResponseCodesEnum.TRANSACTION_FAILED.getResponseMessage(), 
                    ex 
            );
        }


        try {
            int i = gcRedemptionDomainService.recordRedemption(redemptionDTOS);
            if (i == 0 || i < redemptionDTOS.size()) {
                throw new GvBusinessException(
                    GvPosCommonResponseCodesEnum.TRANSACTION_FAILED.getResponseCode(),
                    GvPosCommonResponseCodesEnum.TRANSACTION_FAILED.getResponseMessage()
                );
            }
        } catch (Exception e) {
            throw new GvBusinessException(
                String.valueOf(GvPosCommonResponseCodesEnum.TRANSACTION_FAILED.getResponseCode()), 
                GvPosCommonResponseCodesEnum.TRANSACTION_FAILED.getResponseMessage(), 
                e
            );
        }

        CardRedeemResponse response = new CardRedeemResponse();
        response.setResponseCode(GiftCardResponseCodesEnum.TRANSACTION_SUCCESS.getResponseCode());
        response.setResponseMessage(GiftCardResponseCodesEnum.TRANSACTION_SUCCESS.getResponseMessage());
        response.setNotes(request.getNotes());
        if (request.getTransactionId() != null) {
            response.setRetryKey(String.valueOf(request.getTransactionId()));
        }
        response.setSource("POS");
        response.setBatchNumber(batchNumber.substring(batchNumber.length()-4));

        response.setGiftCards(responseGiftCardsList);

        return response;
    }

    public CheckBalanceResponse checkBalance(CheckBalanceRequest request, String terminalId) {
        // Validate required fields
        if (request.getGiftCardNumber() == null || request.getGiftCardNumber().trim().isEmpty()) {
            throw new GvBusinessException(GiftCardResponseCodesEnum.GIFT_CARD_NUMBER_IS_NULL);
        }

        Pos pos = getPosAndValidate(terminalId);
        GiftCard giftCard = getGiftCardAndValidate(request.getGiftCardNumber());
        GcCpg cpg = getCpgEntityAndValidate(giftCard.getCpgCode(), giftCard.getCardNumber());

        CheckBalanceResponse response = new CheckBalanceResponse();
        response.setResponseCode(GiftCardResponseCodesEnum.TRANSACTION_SUCCESS.getResponseCode());
        response.setResponseMessage(GiftCardResponseCodesEnum.TRANSACTION_SUCCESS.getResponseMessage());
        response.setNotes(""); 
        response.setSource("POS");
        response.setBatchNumber(""); 
        
        GiftCardInfo processedGcResponseInfo = new GiftCardInfo();
        processedGcResponseInfo.setItemNo("1");
        processedGcResponseInfo.setGiftCardNumber(giftCard.getCardNumber());
        //processedGcResponseInfo.setCurrency(cpg.getCurrencyCode());
        processedGcResponseInfo.setGiftCardStatus(giftCard.getStatus() != null ? giftCard.getStatus().name() : "UNKNOWN");
        processedGcResponseInfo.setGiftCardStatusId(giftCard.getStatus() != null ? giftCard.getStatus().name() : "UNKNOWN"); 
        processedGcResponseInfo.setRemainingBalance(giftCard.getBalance());
        processedGcResponseInfo.setDenomination(giftCard.getDenomination());
        processedGcResponseInfo.setApprovalCode(""); 
        processedGcResponseInfo.setGcpg(cpg.getCpgName());
        processedGcResponseInfo.setIssuer(giftCard.getIssuerCode());
        processedGcResponseInfo.setGiftCardExpiry(giftCard.getExpiryTime());
        processedGcResponseInfo.setBalanceExpiry(giftCard.getExpiryTime());
        processedGcResponseInfo.setInquiryDate(new Date());
        processedGcResponseInfo.setArticleCode(cpg.getArticleMopCode());
        processedGcResponseInfo.setMopCode("GC");
        processedGcResponseInfo.setActivationCode(giftCard.getActivationCode());

        // Get gift card history records
        List<GiftCardInfo.GiftCardHistory> historyRecords = getGiftCardHistoryRecords(giftCard.getCardNumber());
        processedGcResponseInfo.setGiftCardHistory(historyRecords);

        response.setGiftCards(processedGcResponseInfo);
        
        return response;
    }

    public QueryCardDetailsResponse queryCardDetails(QueryCardDetailsRequest request, String terminalId) {
        GiftCard giftCard = getGiftCardAndValidate(request.getGiftCardNumber());
        GcCpg cpg = getCpgEntityAndValidate(giftCard.getCpgCode(), giftCard.getCardNumber());

        GcHolder gcHolder;
        try {
            gcHolder = gcDomainService.getGiftCardHolder(request.getGiftCardNumber());
            if (gcHolder == null) {
                log.warn("queryCardDetails: GcHolder not found for cardNumber: {}. Cardholder fields in response will be empty.", request.getGiftCardNumber());
                gcHolder = new GcHolder();
            }
        } catch (GvBusinessException e) { 
             throw e;
        } catch (Exception e) { 
            log.error("queryCardDetails: Error finding GcHolder by cardNumber: {}. Error: {}", request.getGiftCardNumber(), e.getMessage(), e);
             throw new GvBusinessException(
                String.valueOf(GvPosCommonResponseCodesEnum.TRANSACTION_FAILED.getResponseCode()),
                GvPosCommonResponseCodesEnum.TRANSACTION_FAILED.getResponseMessage(), 
                e
            );
        }

        QueryCardDetailsResponse response = new QueryCardDetailsResponse();
        response.setResponseCode(GvPosCommonResponseCodesEnum.TRANSACTION_SUCCESS.getResponseCode());
        response.setResponseMessage(GvPosCommonResponseCodesEnum.TRANSACTION_SUCCESS.getResponseMessage());
        response.setNotes("");

        response.setGiftCardNumber(giftCard.getCardNumber());
        response.setCardHolderName(gcHolder.getHolderName()); 
        response.setRemainingBalance(giftCard.getBalance());
        response.setDenomination(giftCard.getDenomination());
        response.setGiftCardStatus(giftCard.getStatus() != null ? giftCard.getStatus().name() : "UNKNOWN");
        //response.setCurrency(cpg.getCurrencyCode());
        response.setActivationDate(giftCard.getActivationTime());
        response.setExpiryDate(giftCard.getExpiryTime());
        response.setGcpg(cpg.getCpgName());
        response.setIssuer(giftCard.getIssuerCode());
        response.setCardHolderEmail(gcHolder.getHolderEmail()); 
        response.setCardHolderAddress(gcHolder.getHolderAddress()); 

        return response;
    }

    public QueryCardStatementResponse queryCardStatement(QueryCardStatementRequest request, String terminalId) {
        GiftCard card = getGiftCardAndValidate(request.getGiftCardNumber());

        List<GcSalesRecord> salesRecords;
        List<GcRedemptionRecord> redemptionRecords;
        List<GcActivationRecord> activationRecords;
        List<GcActivationExtensionRecord> extensionRecords;
        List<SalesCancelRecord> cancelSalesRecords;
        List<GcBlockRecord> blockRecords;
        List<GcUnblockRecord> unblockRecords;

        try {
            salesRecords = gcSalesDomainService.getSalesRecordByCardNumber(card.getCardNumber());
        } catch (Exception e) {
            log.error("queryCardStatement: Error fetching sales records for card {}: {}", card.getCardNumber(), e.getMessage(), e);
            throw new GvBusinessException(
                String.valueOf(GvPosCommonResponseCodesEnum.TRANSACTION_FAILED.getResponseCode()), 
                GvPosCommonResponseCodesEnum.TRANSACTION_FAILED.getResponseMessage(), 
                e); 
        }
        try {
            redemptionRecords = gcRedemptionDomainService.getRedemptionsByCardNumber(card.getCardNumber());
        } catch (Exception e) {
            log.error("queryCardStatement: Error fetching redemption records for card {}: {}", card.getCardNumber(), e.getMessage(), e);
            throw new GvBusinessException(
                String.valueOf(GvPosCommonResponseCodesEnum.TRANSACTION_FAILED.getResponseCode()), 
                GvPosCommonResponseCodesEnum.TRANSACTION_FAILED.getResponseMessage(), 
                e); 
        }
        try {
            activationRecords = gcActivateDomainService.getActivationRecord(card.getCardNumber());
        } catch (Exception e) {
            log.error("queryCardStatement: Error fetching activation records for card {}: {}", card.getCardNumber(), e.getMessage(), e);
            throw new GvBusinessException(
                String.valueOf(GvPosCommonResponseCodesEnum.TRANSACTION_FAILED.getResponseCode()),
                GvPosCommonResponseCodesEnum.TRANSACTION_FAILED.getResponseMessage(),
                e);
        }
        try {
            extensionRecords = gcExtensionDomainService.getExtensionRecordsByCardNumber(card.getCardNumber());
        } catch (Exception e) {
            log.error("queryCardStatement: Error fetching extension records for card {}: {}", card.getCardNumber(), e.getMessage(), e);
            throw new GvBusinessException(
                String.valueOf(GvPosCommonResponseCodesEnum.TRANSACTION_FAILED.getResponseCode()),
                GvPosCommonResponseCodesEnum.TRANSACTION_FAILED.getResponseMessage(),
                e);
        }
        try {
            cancelSalesRecords = gcSalesDomainService.getCancelSalesRecordByCardNumber(card.getCardNumber());
        } catch (Exception e) {
            log.error("queryCardStatement: Error fetching cancel sales records for card {}: {}", card.getCardNumber(), e.getMessage(), e);
            throw new GvBusinessException(
                String.valueOf(GvPosCommonResponseCodesEnum.TRANSACTION_FAILED.getResponseCode()),
                GvPosCommonResponseCodesEnum.TRANSACTION_FAILED.getResponseMessage(),
                e);
        }
        try {
            blockRecords = gcBlockDomainService.getBlockRecordsByCardNumber(card.getCardNumber());
        } catch (Exception e) {
            log.error("queryCardStatement: Error fetching block records for card {}: {}", card.getCardNumber(), e.getMessage(), e);
            throw new GvBusinessException(
                String.valueOf(GvPosCommonResponseCodesEnum.TRANSACTION_FAILED.getResponseCode()),
                GvPosCommonResponseCodesEnum.TRANSACTION_FAILED.getResponseMessage(),
                e);
        }
        try {
            unblockRecords = gcBlockDomainService.getUnblockRecordsByCardNumber(card.getCardNumber());
        } catch (Exception e) {
            log.error("queryCardStatement: Error fetching unblock records for card {}: {}", card.getCardNumber(), e.getMessage(), e);
            throw new GvBusinessException(
                String.valueOf(GvPosCommonResponseCodesEnum.TRANSACTION_FAILED.getResponseCode()),
                GvPosCommonResponseCodesEnum.TRANSACTION_FAILED.getResponseMessage(),
                e);
        }

        QueryCardStatementResponse response = new QueryCardStatementResponse();
        response.setCardNumber(card.getCardNumber());
        response.setCardProgramGroup(card.getCpgCode());
        response.setResponseCode(GvPosCommonResponseCodesEnum.TRANSACTION_SUCCESS.getResponseCode());
        response.setResponseMessage(GvPosCommonResponseCodesEnum.TRANSACTION_SUCCESS.getResponseMessage());
        
        List<Transaction> transactions = new ArrayList<>();

        if (salesRecords != null) {
            for (GcSalesRecord salesRecord : salesRecords) {
                Transaction transaction = new Transaction();
                transaction.setMerchant(salesRecord.getMerchantCode());
                transaction.setTransactionDate(salesRecord.getSalesTime());
                transaction.setTransactionType("SALES");
                transaction.setTransactionId(salesRecord.getSalesCode());
                transaction.setMerchantOutlet(salesRecord.getOutletCode());
                transactions.add(transaction);
            }
        }

        if (redemptionRecords != null) {
            for (GcRedemptionRecord redemptionRecord : redemptionRecords) {
                Transaction transaction = new Transaction();
                transaction.setMerchant(redemptionRecord.getMerchantCode());
                transaction.setTransactionAmount(redemptionRecord.getAmount());
                transaction.setTransactionDate(redemptionRecord.getRedemptionTime());
                transaction.setTransactionType("REDEMPTION");
                transaction.setRemainingBalance(redemptionRecord.getBalanceAfter());
                transaction.setPreviousBalance(redemptionRecord.getBalanceBefore());
                transaction.setNotes(redemptionRecord.getNotes());
                transaction.setMerchantOutlet(redemptionRecord.getOutletCode());
                transactions.add(transaction);
            }
        }

        if (activationRecords != null) {
            for (GcActivationRecord activationRecord : activationRecords) {
                Transaction transaction = new Transaction();
                transaction.setMerchant(activationRecord.getMerchantCode());
                transaction.setTransactionDate(activationRecord.getActivationTime());
                transaction.setTransactionType("ACTIVATION");
                transaction.setNotes(activationRecord.getNotes());
                transaction.setMerchantOutlet(activationRecord.getOutletCode());
                transactions.add(transaction);
            }
        }

        // 添加延长激活期限记录
        if (extensionRecords != null) {
            for (GcActivationExtensionRecord extensionRecord : extensionRecords) {
                Transaction transaction = new Transaction();
                transaction.setMerchant(extensionRecord.getMerchantCode());
                transaction.setTransactionDate(extensionRecord.getExtensionTime());
                transaction.setTransactionType("ACTIVATION_EXTENSION");
                transaction.setTransactionId(extensionRecord.getExtensionCode());
                transaction.setNotes(extensionRecord.getNotes());
                transaction.setMerchantOutlet(extensionRecord.getOutletCode());
                transactions.add(transaction);
            }
        }

        // 添加取消销售记录
        if (cancelSalesRecords != null) {
            for (SalesCancelRecord cancelRecord : cancelSalesRecords) {
                Transaction transaction = new Transaction();
                transaction.setMerchant(cancelRecord.getMerchantCode());
                transaction.setTransactionDate(cancelRecord.getCancelTime());
                transaction.setTransactionType("CANCEL_SALES");
                transaction.setTransactionId(cancelRecord.getCancelCode());
                transaction.setNotes(cancelRecord.getCancelReason());
                transaction.setMerchantOutlet(cancelRecord.getOutletCode());
                transactions.add(transaction);
            }
        }

        // 添加禁用记录
        if (blockRecords != null) {
            for (GcBlockRecord blockRecord : blockRecords) {
                Transaction transaction = new Transaction();
                transaction.setMerchant(blockRecord.getMerchantCode());
                transaction.setTransactionDate(blockRecord.getBlockTime());
                transaction.setTransactionType("BLOCK");
                transaction.setNotes(blockRecord.getBlockReason());
                transaction.setMerchantOutlet(blockRecord.getOutletCode());
                transactions.add(transaction);
            }
        }

        // 添加启用记录
        if (unblockRecords != null) {
            for (GcUnblockRecord unblockRecord : unblockRecords) {
                Transaction transaction = new Transaction();
                transaction.setMerchant(unblockRecord.getMerchantCode());
                transaction.setTransactionDate(unblockRecord.getUnblockTime());
                transaction.setTransactionType("UNBLOCK");
                transaction.setNotes(unblockRecord.getUnblockReason());
                transaction.setMerchantOutlet(unblockRecord.getOutletCode());
                transactions.add(transaction);
            }
        }

        response.setTransactions(transactions);
        return response;
    }

    public QueryCustomerCardListResponse queryCustomerCardList(QueryCustomerCardListRequest request, String terminalId) {

        List<GcSalesRecord> salesRecordByInvoiceNumber = gcSalesDomainService.getSalesRecordByInvoiceNumber(request.getInvoiceNumber());
        List<String> cardNumber = Optional.of(salesRecordByInvoiceNumber.stream().map(GcSalesRecord::getCardNumber).collect(Collectors.toList())).orElse(new ArrayList<>());

        if (CollectionUtils.isEmpty(cardNumber)){
            QueryCustomerCardListResponse queryCustomerCardListResponse = new QueryCustomerCardListResponse();

            queryCustomerCardListResponse.setCustomerId(request.getCustomerId());
            queryCustomerCardListResponse.setCards(new ArrayList<>());
            queryCustomerCardListResponse.setTotal(0L);
            queryCustomerCardListResponse.setPageNum(request.getPageNum());
            queryCustomerCardListResponse.setPageSize(request.getPageSize());
            queryCustomerCardListResponse.setResponseCode(GvPosCommonResponseCodesEnum.TRANSACTION_SUCCESS.getResponseCode());
            queryCustomerCardListResponse.setResponseMessage(GvPosCommonResponseCodesEnum.TRANSACTION_SUCCESS.getResponseMessage());

            return queryCustomerCardListResponse;
        }


        PageData<GiftCard> cardPageData;
        try {
            cardPageData = gcDomainService.getCustomerCardListWithPagination(request.getCustomerId(), cardNumber, request);
            if (cardPageData.getList() == null || cardPageData.getList().isEmpty()) {
                log.info("queryCustomerCardList: No gift cards found for customerId: {}", request.getCustomerId());
                QueryCustomerCardListResponse emptyResponse = new QueryCustomerCardListResponse();
                emptyResponse.setCustomerId(request.getCustomerId());
                emptyResponse.setCards(new ArrayList<>());
                emptyResponse.setTotal(0L);
                emptyResponse.setPageNum(request.getPageNum());
                emptyResponse.setPageSize(request.getPageSize());
                emptyResponse.setResponseCode(GvPosCommonResponseCodesEnum.TRANSACTION_SUCCESS.getResponseCode());
                emptyResponse.setResponseMessage(GvPosCommonResponseCodesEnum.TRANSACTION_SUCCESS.getResponseMessage());
                emptyResponse.setNotes("No cards found for the customer.");
                return emptyResponse;
            }


        } catch (Exception e) {
            log.error("queryCustomerCardList: Error fetching customer card list for customerId: {}. Error: {}", request.getCustomerId(), e.getMessage(), e);
            throw new GvBusinessException(
                String.valueOf(GvPosCommonResponseCodesEnum.TRANSACTION_FAILED.getResponseCode()),
                GvPosCommonResponseCodesEnum.TRANSACTION_FAILED.getResponseMessage(),
                e
            );
        }

        AtomicInteger item = new AtomicInteger();
        List<GiftCardInfo> giftCardInfos = cardPageData.getList().stream()
                .map(card -> {
                    int andIncrement = item.getAndIncrement();
                    GiftCardInfo giftCardInfo = new GiftCardInfo();
                    giftCardInfo.setGiftCardNumber(card.getCardNumber());
                    giftCardInfo.setGcpg(card.getCpgCode());
                    giftCardInfo.setIssuer(card.getIssuerCode());
                    giftCardInfo.setItemNo(String.valueOf(andIncrement));
                    giftCardInfo.setTransactionDate(card.getActivationTime());
                    giftCardInfo.setGiftCardStatus(card.getStatus() != null ? card.getStatus().name() : "UNKNOWN");
                    giftCardInfo.setGiftCardStatusId(card.getStatus() != null ? card.getStatus().name() : "UNKNOWN");
                    giftCardInfo.setDenomination(card.getDenomination());
                    giftCardInfo.setRemainingBalance(card.getBalance());
                    giftCardInfo.setGiftCardExpiry(card.getExpiryTime());
                    return giftCardInfo;
                })
                .collect(Collectors.toList());

        QueryCustomerCardListResponse queryCustomerCardListResponse = new QueryCustomerCardListResponse();
        queryCustomerCardListResponse.setCards(giftCardInfos);
        queryCustomerCardListResponse.setCustomerId(request.getCustomerId());
        queryCustomerCardListResponse.setTotal(cardPageData.getTotal());
        queryCustomerCardListResponse.setPageNum(request.getPageNum());
        queryCustomerCardListResponse.setPageSize(request.getPageSize());
        queryCustomerCardListResponse.setResponseCode(GvPosCommonResponseCodesEnum.TRANSACTION_SUCCESS.getResponseCode());
        queryCustomerCardListResponse.setResponseMessage(GvPosCommonResponseCodesEnum.TRANSACTION_SUCCESS.getResponseMessage());
        queryCustomerCardListResponse.setNotes("");

        return queryCustomerCardListResponse;
    }

    public List<GiftCardInfo> getGiftCardInfo(String batchCode, GcCpgDTO cpg) {
        List<GiftCardInfo> giftCardInfos = new ArrayList<>();
        List<GiftCard> giftCards;
        try {
            giftCards = gcDomainService.findByBatchCode(batchCode);
        } catch (Exception e) {
            log.error("getGiftCardInfo: Failed to find cards by batchCode {}: {}", batchCode, e.getMessage(), e);
            throw new GvBusinessException(
                String.valueOf(GvPosCommonResponseCodesEnum.TRANSACTION_FAILED.getResponseCode()),
                GvPosCommonResponseCodesEnum.TRANSACTION_FAILED.getResponseMessage(),
                e
            );
        }

        if (giftCards != null && !giftCards.isEmpty()) {
            if (cpg == null) { 
                 log.error("getGiftCardInfo: CPG DTO is null for batchCode {}", batchCode);
                 throw new GvBusinessException(
                    GvPosCommonResponseCodesEnum.TRANSACTION_FAILED.getResponseCode(),
                    GvPosCommonResponseCodesEnum.TRANSACTION_FAILED.getResponseMessage() 
                 );
            }
            Integer itemNo = 1;
            for (GiftCard giftCard : giftCards) {
                GiftCardInfo giftCardInfo = new GiftCardInfo();
                giftCardInfo.setItemNo(String.valueOf(itemNo++));
                giftCardInfo.setGiftCardNumber(giftCard.getCardNumber());
                giftCardInfo.setGiftCardPIN(giftCard.getPinCode());
                giftCardInfo.setCurrency(cpg.getCurrency());
                giftCardInfo.setGiftCardStatus(giftCard.getStatus() != null ? giftCard.getStatus().name() : "UNKNOWN");
                giftCardInfo.setDenomination(giftCard.getDenomination());
                giftCardInfo.setApprovalCode(""); 
                giftCardInfo.setGcpg(cpg.getCpgName());
                giftCardInfo.setIssuer(giftCard.getIssuerCode());
                giftCardInfo.setGiftCardExpiry(giftCard.getExpiryTime());
                giftCardInfo.setTransactionDate(giftCard.getSalesTime());
                giftCardInfo.setArticleCode(cpg.getArticleMopCode());
                giftCardInfo.setMopCode("GC");
                giftCardInfo.setActivationCode(giftCard.getActivationCode());
                giftCardInfos.add(giftCardInfo);
            }
        }
        return giftCardInfos;
    }

    private Pos getPosAndValidate(String terminalId) {
        Pos pos;
        try {
            pos = masterDataCache.getPos(terminalId);
            if (pos == null) {
                log.warn("getPosAndValidate: POS not found for terminalId: {}", terminalId);
                throw new GvBusinessException(
                    GvPosCommonResponseCodesEnum.INVALID_TERMINALID.getResponseCode(),
                    GvPosCommonResponseCodesEnum.INVALID_TERMINALID.getResponseMessage()
                );
            }
        } catch (GvBusinessException e) {
            throw e; 
        } catch (Exception e) {
            log.error("getPosAndValidate: Error fetching POS for terminalId: {}. Error: {}", terminalId, e.getMessage(), e);
            throw new GvBusinessException(
                String.valueOf(GvPosCommonResponseCodesEnum.TRANSACTION_FAILED.getResponseCode()),
                GvPosCommonResponseCodesEnum.TRANSACTION_FAILED.getResponseMessage(),
                e 
            );
        }
        return pos;
    }

    private Outlet getOutletAndValidate(String outletCode) {
        Outlet outlet;
        try {
            outlet = masterDataCache.getOutlet(outletCode);
            if (outlet == null) {
                log.warn("getOutletAndValidate: Outlet not found for outletCode: {}", outletCode);
                throw new GvBusinessException(
                    GvPosCommonResponseCodesEnum.MERCHANT_OUTLET_AUTH_FAILED.getResponseCode(),
                    GvPosCommonResponseCodesEnum.MERCHANT_OUTLET_AUTH_FAILED.getResponseMessage()
                );
            }
        } catch (GvBusinessException e) {
            throw e; 
        } catch (Exception e) {
            log.error("getOutletAndValidate: Error fetching Outlet for outletCode: {}. Error: {}", outletCode, e.getMessage(), e);
            throw new GvBusinessException(
                String.valueOf(GvPosCommonResponseCodesEnum.TRANSACTION_FAILED.getResponseCode()),
                GvPosCommonResponseCodesEnum.TRANSACTION_FAILED.getResponseMessage(),
                e 
            );
        }
        return outlet;
    }

    private GcCpgDTO getGcCpgDtoAndValidate(String gcpgName) {
        if (gcpgName == null || gcpgName.trim().isEmpty()) {
            throw new GvBusinessException(GiftCardResponseCodesEnum.GCPG_IS_NULL);
        }

        GcCpgDTO cpgDto;
        try {
            cpgDto = gcCpgService.getCpgByName(gcpgName);
            if (cpgDto == null) {
                log.warn("getGcCpgDtoAndValidate: GcpgDTO not found for name: {}", gcpgName);
                throw new GvBusinessException(GiftCardResponseCodesEnum.GCPG_DOES_NOT_EXIST);
            }

            // Check CPG status - must be enabled
            if (GvCpgStatusEnum.STATUS_DISABLE.getCode().equals(cpgDto.getStatus())) {
                log.warn("getGcCpgDtoAndValidate: GCPG '{}' is disabled. Status: {}", gcpgName, cpgDto.getStatus());
                throw new GvBusinessException(GiftCardResponseCodesEnum.CPG_IS_DISABLED);
            }
        } catch (GvBusinessException e) {
            throw e;
        } catch (Exception e) {
            log.error("getGcCpgDtoAndValidate: Error fetching GcpgDTO by name: {}. Error: {}", gcpgName, e.getMessage(), e);
            throw new GvBusinessException(GiftCardResponseCodesEnum.GCPG_DOES_NOT_EXIST, e);
        }
        return cpgDto;
    }

    private void validatePosCpg(Pos pos, GcCpgDTO cpgDto) {
        if (pos == null || cpgDto == null) {
            throw new GvBusinessException(GiftCardResponseCodesEnum.POS_CPG_AUTHORIZATION_FAILED);
        }

        if (masterDataCache.hasPosCpg(pos.getPosCode())) {
            if (!masterDataCache.hasPosCpgWithCpgCode(pos.getPosCode(), cpgDto.getCpgCode())) {
                throw new GvBusinessException(GiftCardResponseCodesEnum.POS_CPG_AUTHORIZATION_FAILED);
            }
        }
    }





    private GiftCard getGiftCardAndValidate(String cardNumber) {
        if (cardNumber == null || cardNumber.trim().isEmpty()) {
            throw new GvBusinessException(GiftCardResponseCodesEnum.GIFT_CARD_NUMBER_IS_NULL);
        }

        GiftCard giftCard;
        try {
            giftCard = gcDomainService.findByCardNumber(cardNumber);
            if (giftCard == null) {
                log.warn("getGiftCardAndValidate: GiftCard not found for cardNumber: {}", cardNumber);
                throw new GvBusinessException(GiftCardResponseCodesEnum.GIFT_CARD_NUMBER_DOES_NOT_EXIST);
            }
        } catch (GvBusinessException e) {
            throw e;
        } catch (Exception e) {
            log.error("getGiftCardAndValidate: Error finding gift card by cardNumber: {}. Error: {}", cardNumber, e.getMessage(), e);
            throw new GvBusinessException(GiftCardResponseCodesEnum.GIFT_CARD_NUMBER_DOES_NOT_EXIST, e);
        }
        return giftCard;
    }



    private List<GiftCard> queryGiftCardAndValidate(List<String> cardNumbers) {
        if (cardNumbers == null || cardNumbers.isEmpty()) {
            throw new GvBusinessException(GiftCardResponseCodesEnum.GIFT_CARDS_IS_NULL);
        }

        // Check for null or empty card numbers in the list
        for (String cardNumber : cardNumbers) {
            if (cardNumber == null || cardNumber.trim().isEmpty()) {
                throw new GvBusinessException(GiftCardResponseCodesEnum.GIFT_CARD_NUMBER_IN_GIFT_CARDS_DOES_NOT_EXIST);
            }
        }

        List<GiftCard> giftCard;
        try {
            giftCard = gcDomainService.findByCardNumber(cardNumbers);
            if (giftCard == null || giftCard.isEmpty()) {
                throw new GvBusinessException(GiftCardResponseCodesEnum.GIFT_CARD_NUMBER_IN_GIFT_CARDS_DOES_NOT_EXIST);
            }
        } catch (GvBusinessException e) {
            throw e;
        } catch (Exception e) {
            throw new GvBusinessException(GiftCardResponseCodesEnum.GIFT_CARD_NUMBER_IN_GIFT_CARDS_DOES_NOT_EXIST, e);
        }
        return giftCard;
    }

    private GcCpg getCpgEntityAndValidate(String cpgCode, String contextCardNumber) {
        GcCpg cpg;
        try {
            cpg = masterDataCache.getGcCpg(cpgCode);
            if (cpg == null) {
                String errorMessage = String.format("CPG entity not found for cpgCode: %s", cpgCode);
                if (contextCardNumber != null && !contextCardNumber.isEmpty()) {
                    errorMessage = String.format("CPG entity not found for cpgCode: %s (context card: %s)", cpgCode, contextCardNumber);
                }
                log.error("getCpgEntityAndValidate: {}", errorMessage);
                throw new GvBusinessException(
                    GvPosCommonResponseCodesEnum.TRANSACTION_FAILED.getResponseCode(), 
                    GvPosCommonResponseCodesEnum.TRANSACTION_FAILED.getResponseMessage()
                );
            }
        } catch (GvBusinessException e) {
            throw e;
        } catch (Exception e) {
            String errorLog = String.format("Error fetching CPG entity for cpgCode: %s", cpgCode);
             if (contextCardNumber != null && !contextCardNumber.isEmpty()) {
                errorLog = String.format("Error fetching CPG entity for cpgCode: %s (context card: %s). Error: %s", cpgCode, contextCardNumber, e.getMessage());
            }
            log.error("getCpgEntityAndValidate: {}. Error: {}", errorLog, e.getMessage(), e);
            throw new GvBusinessException(
                String.valueOf(GvPosCommonResponseCodesEnum.TRANSACTION_FAILED.getResponseCode()),
                GvPosCommonResponseCodesEnum.TRANSACTION_FAILED.getResponseMessage(), 
                e
            );
        }
        return cpg;
    }

    public BatchCloseResponse newBatchClose(NewBatchCloseRequest request, String terminalId, Integer transactionId, String token) {

        String batchNumber = (String) redisTemplate.opsForValue().get("GV:AUTHORIZE_BATCHID:" + token);

        BatchcloseRequest batchCloseRequest = new BatchcloseRequest();
        batchCloseRequest.setTransactionId(transactionId);
        BatchcloseResponse batchcloseResponse = transactionDataService.batchClose(batchCloseRequest, token);


        List<GcSalesRecord> salesRecords;
        List<GcRedemptionRecord> redemptionRecords;
        List<GcActivationRecord> activationRecords;

        try {
            salesRecords = gcSalesDomainService.getSalesRecordByBatchNumber(batchNumber);
        } catch (Exception e) {
            throw new GvBusinessException(
                    String.valueOf(GvPosCommonResponseCodesEnum.TRANSACTION_FAILED.getResponseCode()),
                    GvPosCommonResponseCodesEnum.TRANSACTION_FAILED.getResponseMessage(),
                    e);
        }
        try {
            redemptionRecords = gcRedemptionDomainService.getRedemptionsByBatchNumber(batchNumber);
        } catch (Exception e) {
            throw new GvBusinessException(
                    String.valueOf(GvPosCommonResponseCodesEnum.TRANSACTION_FAILED.getResponseCode()),
                    GvPosCommonResponseCodesEnum.TRANSACTION_FAILED.getResponseMessage(),
                    e);
        }
        /*try {
            activationRecords = gcActivateDomainService.getActivationByBatchNumber(batchNumberInteger);
        } catch (Exception e) {
            throw new GvBusinessException(
                    String.valueOf(GvPosCommonResponseCodesEnum.TRANSACTION_FAILED.getResponseCode()),
                    GvPosCommonResponseCodesEnum.TRANSACTION_FAILED.getResponseMessage(),
                    e);
        }*/

        BigDecimal salesAmount = salesRecords.stream().map(GcSalesRecord::getDenomination).filter(Objects::nonNull).reduce(BigDecimal.ZERO, BigDecimal::add);
        BigDecimal redeemAmount = redemptionRecords.stream().map(GcRedemptionRecord::getAmount).filter(Objects::nonNull).reduce(BigDecimal.ZERO, BigDecimal::add);


        BatchCloseResponse batchCloseResponse = new BatchCloseResponse();
        batchCloseResponse.setResponseCode(batchcloseResponse.getResponseCode());
        batchCloseResponse.setResponseMessage(batchcloseResponse.getResponseMessage());
        batchCloseResponse.setNewToken(batchcloseResponse.getNewAuthToken());
        batchCloseResponse.setNewBatchNumber(batchcloseResponse.getNewBatchNumber());
        batchCloseResponse.setVoucherRedemptionCount(batchcloseResponse.getRedemptionCount());
        batchCloseResponse.setVoucherRedemptionAmount(batchcloseResponse.getRedemptionAmount());
        batchCloseResponse.setVoucherSalesCount(batchcloseResponse.getActivationCount());
        batchCloseResponse.setVoucherSalesAmount(batchcloseResponse.getActivationAmount());

        batchCloseResponse.setGiftCardSalesCount(salesRecords.size());
        batchCloseResponse.setGiftCardSalesAmount(salesAmount);
        batchCloseResponse.setGiftCardRedemptionCount(redemptionRecords.size());
        batchCloseResponse.setGiftCardRedemptionAmount(redeemAmount);
        batchCloseResponse.setNotes(batchcloseResponse.getNotes());

        return batchCloseResponse;
    }


    public CreateCustomerOrderRequest createCustomerOrder(String invoiceNumber,
                                                          Outlet outlet,
                                                          CustomerInfo customerInfo,
                                                          String issuerCode,
                                                          String customerOrderCode,
                                                          String notes,
                                                          Integer numOfCards,
                                                          List<CreateCustomerOrderDetailsRequest> detailsRequestList,
                                                          BigDecimal voucherAmount) {
        CreateCustomerOrderRequest customerOrderRequest = new CreateCustomerOrderRequest();
        customerOrderRequest.setCustomerOrderCode(customerOrderCode);
        customerOrderRequest.setIssuerCode(issuerCode);
        customerOrderRequest.setOutletCode(outlet.getOutletCode());
        customerOrderRequest.setInvoiceNo(invoiceNumber);
        customerOrderRequest.setPurchaseOrderNo(invoiceNumber);
        customerOrderRequest.setMopCode("GC");
        customerOrderRequest.setVoucherNum(numOfCards);
        customerOrderRequest.setVoucherAmount(voucherAmount);
        customerOrderRequest.setDiscount(new BigDecimal("0"));
        customerOrderRequest.setAmount(BigDecimal.ZERO);
        customerOrderRequest.setVoucherBatchCode(customerOrderCode);


        customerOrderRequest.setContactFirstName(null != customerInfo && StringUtil.isNotEmpty(customerInfo.getFirstName()) ?
                customerInfo.getFirstName() : API_CUSTOMER);
        customerOrderRequest.setContactLastName(null != customerInfo && StringUtil.isNotEmpty(customerInfo.getLastName()) ?
                customerInfo.getLastName() : API_CUSTOMER);
        customerOrderRequest.setContactPhone(null != customerInfo && StringUtil.isNotEmpty(customerInfo.getMobile()) ?
                customerInfo.getMobile() : API_CUSTOMER);
        customerOrderRequest.setCompanyName(null != customerInfo && StringUtil.isNotEmpty(customerInfo.getCorporateName()) ?
                customerInfo.getCorporateName() : API_CUSTOMER);
        customerOrderRequest.setContactEmail(null != customerInfo && StringUtil.isNotEmpty(customerInfo.getEmail()) ?
                customerInfo.getEmail() : API_CUSTOMER);
        customerOrderRequest.setCustomerCode(null != customerInfo && StringUtil.isNotEmpty(customerInfo.getCustomerId()) ?
                customerInfo.getCustomerId() : API_CUSTOMER);
        customerOrderRequest.setCustomerName(null != customerInfo && StringUtil.isNotEmpty(customerInfo.getCustomerName()) ?
                customerInfo.getCustomerName() : API_CUSTOMER);
        customerOrderRequest.setCurrencyCode(API_CUSTOMER);
        customerOrderRequest.setCustomerType(API_CUSTOMER);
        customerOrderRequest.setProductCategoryCode("");
        customerOrderRequest.setDiscountType("");
        customerOrderRequest.setCreateUser("api");
        customerOrderRequest.setMeansOfPaymentCode("");
        customerOrderRequest.setShippingAddress("");
        customerOrderRequest.setCustomerRemarks(notes);
        customerOrderRequest.setStatus(CustomerOrderStatusEnum.API.getStatus());
        customerOrderRequest.setReleaseTime(new Date());

        //detailsRequestList将相同cpgCode的数据的voucherNum相加
        Map<String, CreateCustomerOrderDetailsRequest> map = new HashMap<>();
        for (CreateCustomerOrderDetailsRequest detailsRequest : detailsRequestList) {
            if (map.containsKey(detailsRequest.getCpgCode())) {
                CreateCustomerOrderDetailsRequest createCustomerOrderDetailsRequest = map.get(detailsRequest.getCpgCode());
                createCustomerOrderDetailsRequest.setVoucherNum(createCustomerOrderDetailsRequest.getVoucherNum() + detailsRequest.getVoucherNum());
            } else {
                map.put(detailsRequest.getCpgCode(), detailsRequest);
            }
        }
        //map转list
        List<CreateCustomerOrderDetailsRequest> list = new ArrayList<>();
        for (Map.Entry<String, CreateCustomerOrderDetailsRequest> entry : map.entrySet()) {
            list.add(entry.getValue());
        }

        customerOrderRequest.setCreateCustomerOrderDetailsRequests(list);

        log.info("createCustomerOrder:{}", JSON.toJSONString(customerOrderRequest));
        Result<String> customerOrder = customerOrderService.createCustomerOrder(customerOrderRequest);
        if (!customerOrder.isSuccess()) {
            throw new GTechBaseException(customerOrder.getCode(), customerOrder.getMessage());
        }
        log.info("createCustomerOrder Result :{}", JSON.toJSONString(customerOrder));
        return customerOrderRequest;
    }


    public DynamicBarcodeResponse dynamicBarcode(DynamicBarcodedRequest request) {
        if (StringUtil.isEmpty(request.getGiftCardNumber())) return new DynamicBarcodeResponse();
        GiftCardDTO giftCardInfo = gcDomainService.getGiftCardInfo(request.getGiftCardNumber());
        String barcode = barcodeConverter.toBarcode(giftCardInfo.getCardNumber());
        redisTemplate.opsForValue().set("GV:BARCODE:" + request.getGiftCardNumber(), barcode, 10, TimeUnit.MINUTES);
        DynamicBarcodeResponse dynamicBarcodeResponse = new DynamicBarcodeResponse();
        dynamicBarcodeResponse.setResponseCode(GvPosCommonResponseCodesEnum.TRANSACTION_SUCCESS.getResponseCode());
        dynamicBarcodeResponse.setResponseMessage(GvPosCommonResponseCodesEnum.TRANSACTION_SUCCESS.getResponseMessage());
        dynamicBarcodeResponse.setBarcode(barcode);
        return dynamicBarcodeResponse;
    }

    @Transactional
    public ExtendActivationPeriodResponse extendActivationPeriod(ExtendActivationPeriodRequest request, String terminalId, String batchNumber) {
        // 验证POS终端
        Pos pos = getPosAndValidate(terminalId);
        GiftCard giftCardInfo = getGiftCardAndValidate(request.getGiftCardNumber());
        GcCpg cpgEntityAndValidate = getCpgEntityAndValidate(giftCardInfo.getCpgCode(), giftCardInfo.getCardNumber());
        GcCpgDTO cpgDto = BeanCopyUtils.jsonCopyBean(cpgEntityAndValidate, GcCpgDTO.class);
        this.validatePosCpg(pos,cpgDto);

        // 创建延长操作DTO
        ExtensionDTO extensionDTO = new ExtensionDTO();
        extensionDTO.setCardNumber(request.getGiftCardNumber());
        extensionDTO.setOutletCode(pos.getOutletCode());
        extensionDTO.setApprovalCode(gvCodeHelper.generateApproveCode());
        extensionDTO.setInvoiceNumber(gvCodeHelper.generateInvoiceNumber());
        extensionDTO.setBatchNumber(batchNumber);
        extensionDTO.setNotes(request.getNotes());
        extensionDTO.setSource(ExtensionSourceEnum.API.getCode()); // 标识为API调用

        GiftCard giftCard;
        try {
            // 使用延长领域服务执行延长操作并记录
            giftCard = gcExtensionDomainService.extendActivationPeriod(extensionDTO);
        } catch (IllegalArgumentException e) {
            log.warn("extendActivationPeriod: Extension failed for card {} due to: {}", request.getGiftCardNumber(), e.getMessage());
            throw new GvBusinessException(
                String.valueOf(GvPosCommonResponseCodesEnum.TRANSACTION_FAILED.getResponseCode()),
                GvPosCommonResponseCodesEnum.TRANSACTION_FAILED.getResponseMessage(),
                e);
        } catch (Exception e) {
            log.error("extendActivationPeriod: Unexpected error during extension for card {}: {}", request.getGiftCardNumber(), e.getMessage(), e);
            throw new GvBusinessException(
                String.valueOf(GvPosCommonResponseCodesEnum.TRANSACTION_FAILED.getResponseCode()),
                GvPosCommonResponseCodesEnum.TRANSACTION_FAILED.getResponseMessage(),
                e);
        }

        ExtendActivationPeriodResponse response = new ExtendActivationPeriodResponse();
        response.setResponseCode(GvPosCommonResponseCodesEnum.TRANSACTION_SUCCESS.getResponseCode());
        response.setResponseMessage(GvPosCommonResponseCodesEnum.TRANSACTION_SUCCESS.getResponseMessage());
        response.setGiftCardNumber(giftCard.getCardNumber());
        response.setNewActivationPeriod(DateUtil.parseDate(DateUtil.format(giftCard.getActivationDeadline(), DateUtil.FORMAT_YYYYMMDDHHMISS),DateUtil.FORMAT_YYYYMMDDHHMISS));
        return response;
    }

    /**
     * Get gift card history records for a specific card number
     * This method consolidates all transaction history including PURCHASE, ACTIVATE, REDEEM, DESTROY, BLOCKED operations
     *
     * @param cardNumber Gift card number
     * @return List of gift card history records
     */
    private List<GiftCardInfo.GiftCardHistory> getGiftCardHistoryRecords(String cardNumber) {
        List<GiftCardInfo.GiftCardHistory> historyList = new ArrayList<>();

        try {
            // Get sales records (PURCHASE)
            List<GcSalesRecord> salesRecords = gcSalesDomainService.getSalesRecordByCardNumber(cardNumber);
            if (salesRecords != null) {
                for (GcSalesRecord salesRecord : salesRecords) {
                    GiftCardInfo.GiftCardHistory history = new GiftCardInfo.GiftCardHistory();
                    history.setInvoiceNumber(salesRecord.getInvoiceNumber());
                    history.setTransactionDate(salesRecord.getSalesTime());
                    history.setStoreCode(salesRecord.getOutletCode());
                    history.setStoreName(getOutletName(salesRecord.getOutletCode()));
                    history.setTransactionType("GIFT CARD PURCHASED");
                    history.setTransactionAmount(salesRecord.getDenomination());
                    history.setNotes("Gift card purchased");
                    historyList.add(history);
                }
            }
        } catch (Exception e) {
            log.error("getGiftCardHistoryRecords: Error fetching sales records for card {}: {}", cardNumber, e.getMessage(), e);
        }

        try {
            // Get activation records (ACTIVATE)
            List<GcActivationRecord> activationRecords = gcActivateDomainService.getActivationRecord(cardNumber);
            if (activationRecords != null) {
                for (GcActivationRecord activationRecord : activationRecords) {
                    GiftCardInfo.GiftCardHistory history = new GiftCardInfo.GiftCardHistory();
                    history.setInvoiceNumber(activationRecord.getInvoiceNumber());
                    history.setTransactionDate(activationRecord.getActivationTime());
                    history.setStoreCode(activationRecord.getOutletCode());
                    history.setStoreName(getOutletName(activationRecord.getOutletCode()));
                    history.setTransactionType("GIFT CARD ACTIVATED");
                    history.setTransactionAmount(null); // Activation doesn't have amount
                    history.setNotes(activationRecord.getNotes());
                    historyList.add(history);
                }
            }
        } catch (Exception e) {
            log.error("getGiftCardHistoryRecords: Error fetching activation records for card {}: {}", cardNumber, e.getMessage(), e);
        }

        try {
            // Get redemption records (REDEEM)
            List<GcRedemptionRecord> redemptionRecords = gcRedemptionDomainService.getRedemptionsByCardNumber(cardNumber);
            if (redemptionRecords != null) {
                for (GcRedemptionRecord redemptionRecord : redemptionRecords) {
                    GiftCardInfo.GiftCardHistory history = new GiftCardInfo.GiftCardHistory();
                    history.setInvoiceNumber(redemptionRecord.getInvoiceNumber());
                    history.setTransactionDate(redemptionRecord.getRedemptionTime());
                    history.setStoreCode(redemptionRecord.getOutletCode());
                    history.setStoreName(getOutletName(redemptionRecord.getOutletCode()));
                    history.setTransactionType("GIFT CARD REDEEM");
                    history.setTransactionAmount(redemptionRecord.getAmount());
                    history.setNotes(redemptionRecord.getNotes());
                    historyList.add(history);
                }
            }
        } catch (Exception e) {
            log.error("getGiftCardHistoryRecords: Error fetching redemption records for card {}: {}", cardNumber, e.getMessage(), e);
        }

        try {
            // Get block records (BLOCKED)
            List<GcBlockRecord> blockRecords = gcBlockDomainService.getBlockRecordsByCardNumber(cardNumber);
            if (blockRecords != null) {
                for (GcBlockRecord blockRecord : blockRecords) {
                    GiftCardInfo.GiftCardHistory history = new GiftCardInfo.GiftCardHistory();
                    history.setInvoiceNumber(null); // Block operations may not have invoice
                    history.setTransactionDate(blockRecord.getBlockTime());
                    history.setStoreCode(blockRecord.getOutletCode());
                    history.setStoreName(getOutletName(blockRecord.getOutletCode()));
                    history.setTransactionType("GIFT CARD BLOCKED");
                    history.setTransactionAmount(null); // Block doesn't have amount
                    history.setNotes(blockRecord.getBlockReason());
                    historyList.add(history);
                }
            }
        } catch (Exception e) {
            log.error("getGiftCardHistoryRecords: Error fetching block records for card {}: {}", cardNumber, e.getMessage(), e);
        }

        try {
            // Get unblock records (UNBLOCKED)
            List<GcUnblockRecord> unblockRecords = gcBlockDomainService.getUnblockRecordsByCardNumber(cardNumber);
            if (unblockRecords != null) {
                for (GcUnblockRecord unblockRecord : unblockRecords) {
                    GiftCardInfo.GiftCardHistory history = new GiftCardInfo.GiftCardHistory();
                    history.setInvoiceNumber(null); // Unblock operations may not have invoice
                    history.setTransactionDate(unblockRecord.getUnblockTime());
                    history.setStoreCode(unblockRecord.getOutletCode());
                    history.setStoreName(getOutletName(unblockRecord.getOutletCode()));
                    history.setTransactionType("GIFT CARD UNBLOCKED");
                    history.setTransactionAmount(null); // Unblock doesn't have amount
                    history.setNotes(unblockRecord.getUnblockReason());
                    historyList.add(history);
                }
            }
        } catch (Exception e) {
            log.error("getGiftCardHistoryRecords: Error fetching unblock records for card {}: {}", cardNumber, e.getMessage(), e);
        }

        // Sort history by transaction date (newest first)
        historyList.sort((h1, h2) -> {
            if (h1.getTransactionDate() == null && h2.getTransactionDate() == null) return 0;
            if (h1.getTransactionDate() == null) return 1;
            if (h2.getTransactionDate() == null) return -1;
            return h2.getTransactionDate().compareTo(h1.getTransactionDate());
        });

        return historyList;
    }

    /**
     * Helper method to get outlet name by outlet code
     *
     * @param outletCode Outlet code
     * @return Outlet name or outlet code if name not found
     */
    private String getOutletName(String outletCode) {
        if (outletCode == null || outletCode.isEmpty()) {
            return "";
        }

        try {
            Outlet outlet = masterDataCache.getOutlet(outletCode);
            return outlet != null ? outlet.getOutletName() : outletCode;
        } catch (Exception e) {
            log.warn("getOutletName: Error fetching outlet name for code {}: {}", outletCode, e.getMessage());
            return outletCode; // Return code as fallback
        }
    }

    // ========== Gift Card API Validation Methods ==========

    /**
     * Validate common header fields for Gift Card APIs
     */
    private void validateCommonHeaders(String token, Integer transactionId, String clientTime) {
        if (token == null || token.trim().isEmpty()) {
            throw new GvBusinessException(GiftCardResponseCodesEnum.TOKEN_IS_NULL);
        }
        if (transactionId == null) {
            throw new GvBusinessException(GiftCardResponseCodesEnum.TRANSACTION_ID_IS_NULL);
        }
        if (clientTime == null || clientTime.trim().isEmpty()) {
            throw new GvBusinessException(GiftCardResponseCodesEnum.CLIENT_TIME_IS_NULL);
        }
        // Validate clientTime format
        if (!isValidDateTimeFormat(clientTime)) {
            throw new GvBusinessException(GiftCardResponseCodesEnum.CLIENT_TIME_FORMAT_INVALID);
        }
    }

    /**
     * Validate token only (for APIs that don't require transactionId and clientTime in body)
     */
    private void validateTokenOnly(String token) {
        if (token == null || token.trim().isEmpty()) {
            throw new GvBusinessException(GiftCardResponseCodesEnum.TOKEN_IS_NULL);
        }
    }

    /**
     * Validate date time format (YYYY-MM-DD'T'HH:MM:SS)
     */
    private boolean isValidDateTimeFormat(String dateTime) {
        if (dateTime == null || dateTime.trim().isEmpty()) {
            return false;
        }
        try {
            // Use SimpleDateFormat to validate
            SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd'T'HH:mm:ss");
            sdf.setLenient(false);
            sdf.parse(dateTime);
            return true;
        } catch (Exception e) {
            return false;
        }
    }

}
