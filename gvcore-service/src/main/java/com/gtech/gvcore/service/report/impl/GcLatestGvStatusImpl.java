package com.gtech.gvcore.service.report.impl;

import com.gtech.commons.utils.DateUtil;
import com.gtech.commons.utils.StringUtil;
import com.gtech.gvcore.common.enums.ReportExportTypeEnum;
import com.gtech.gvcore.common.request.orderreport.CreateReportRequest;
import com.gtech.gvcore.dao.mapper.GcReportBusinessMapper;
import com.gtech.gvcore.dao.model.Company;
import com.gtech.gvcore.dao.model.Merchant;
import com.gtech.gvcore.dao.model.Outlet;
import com.gtech.gvcore.giftcard.infrastructure.entity.GiftCardEntity;
import com.gtech.gvcore.giftcard.infrastructure.mapper.GiftCardMapper;
import com.gtech.gvcore.giftcard.masterdata.gcpg.entity.GcCpg;
import com.gtech.gvcore.service.report.BusinessReport;
import com.gtech.gvcore.service.report.PollReport;
import com.gtech.gvcore.service.report.base.ReportSupport;
import com.gtech.gvcore.service.report.extend.joindate.JoinDataMap;
import com.gtech.gvcore.service.report.extend.voucherstatus.ReportVoucherStatusConvertSupport;
import com.gtech.gvcore.service.report.impl.bean.GcLatestGvStatusBean;
import com.gtech.gvcore.service.report.impl.param.GcLatestStatusQueryData;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import tk.mybatis.mapper.weekend.WeekendSqls;

import java.math.BigDecimal;
import java.util.Collections;
import java.util.Date;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * @ClassName LatestGvStatusEnhancedImpl
 * @Description Latest Gift Card Status Enhanced Report Implementation
 * <AUTHOR>
 * @Date 2025-06-17
 * @Version V1.0
 **/
@Slf4j
@Service
public class GcLatestGvStatusImpl extends ReportSupport
        implements BusinessReport<GcLatestStatusQueryData, GcLatestGvStatusBean>, PollReport, ReportVoucherStatusConvertSupport {

    @Autowired
    private GcReportBusinessMapper reportBusinessMapper;

    @Autowired
    private GiftCardMapper giftCardMapper;

    @Override
    public ReportExportTypeEnum exportTypeEnum() {
        return ReportExportTypeEnum.GC_LATEST_GV_STATUS_REPORT;
    }

    @Override
    public GcLatestStatusQueryData builderQueryParam(CreateReportRequest reportParam) {
        GcLatestStatusQueryData queryData = new GcLatestStatusQueryData();

        // Set CPG codes
        queryData.setCpgCodeList(reportParam.getCpgCodes());

        // Set gift card number range
        queryData.setCardNumberStart(reportParam.getVoucherCodeNumStart() != null ? reportParam.getVoucherCodeNumStart().toString() : null);
        queryData.setCardNumberEnd(reportParam.getVoucherCodeNumEnd() != null ? reportParam.getVoucherCodeNumEnd().toString() : null);

        // Set gift card status list
        queryData.setCardStatusList(reportParam.getVoucherStatus());

        // Set gift card numbers from uploaded file (pre-parsed)
        queryData.setCardNumberList(reportParam.getVoucherCodeList());

        return queryData;
    }

    @Override
    public List<GcLatestGvStatusBean> getExportData(GcLatestStatusQueryData queryData) {
        // Get gift card data
        List<GiftCardEntity> giftCardList = getGiftCardList(queryData);

        if (CollectionUtils.isEmpty(giftCardList)) {
            return Collections.emptyList();
        }

        // Get related CPG data
        final JoinDataMap<GcCpg> cpgMap = super.getMapByCode(giftCardList, GiftCardEntity::getCpgCode, GcCpg.class);

        // Get merchant and outlet info from sales records
        final Map<String, String> cardToMerchantMap = getCardToMerchantMap(giftCardList);
        final Map<String, String> cardToOutletMap = getCardToOutletMap(giftCardList);

        // Get unique merchant and outlet codes
        final Set<String> merchantCodes = new HashSet<>(cardToMerchantMap.values());
        final Set<String> outletCodes = new HashSet<>(cardToOutletMap.values());

        // Get merchant and outlet entities
        final JoinDataMap<Merchant> merchantMap = super.getMapByCode(merchantCodes, code -> code, Merchant.class);
        final JoinDataMap<Outlet> outletMap = super.getMapByCode(outletCodes, code -> code, Outlet.class);

        // Get related company data
        final JoinDataMap<Company> companyMap = super.getMapByCode(merchantMap.values(), Merchant::getCompanyCode, Company.class);

        // Convert to report beans
        return giftCardList.stream().map(giftCard -> {
            GcLatestGvStatusBean bean = new GcLatestGvStatusBean();

            // Set basic gift card info
            bean.setVoucherNumber(giftCard.getCardNumber());
            bean.setVoucherStatus(giftCard.getStatus());

            // Format dates
            if (giftCard.getCreateTime() != null) {
                bean.setIssuanceDate(DateUtil.format(giftCard.getCreateTime(), "yyyy-MM-dd HH:mm:ss"));
            }
            if (giftCard.getExpiryTime() != null) {
                bean.setExpiryDate(DateUtil.format(giftCard.getExpiryTime(), "yyyy-MM-dd HH:mm:ss"));
            }

            // Get latest transaction type and last action date
            String transactionType = getLatestTransactionType(giftCard.getCardNumber());
            bean.setTransactionType(transactionType);

            // Get last action date from latest transaction
            String lastActionDate = getLastActionDate(giftCard.getCardNumber());
            bean.setLastActionDate(lastActionDate);

            // Set current balance and redeem amount
            if (giftCard.getBalance() != null) {
                bean.setCurrentBalance(super.toAmount(giftCard.getBalance()));
                // Calculate redeem amount = denomination - current balance
                if (giftCard.getDenomination() != null) {
                    BigDecimal redeemAmount = giftCard.getDenomination().subtract(giftCard.getBalance());
                    bean.setRedeemAmount(super.toAmount(redeemAmount));
                } else {
                    bean.setRedeemAmount(super.toAmount(BigDecimal.ZERO));
                }
            } else {
                bean.setCurrentBalance(super.toAmount(BigDecimal.ZERO));
                bean.setRedeemAmount(super.toAmount(BigDecimal.ZERO));
            }

            // Get CPG info
            Optional<GcCpg> optionalCpg = Optional.ofNullable(cpgMap.get(giftCard.getCpgCode()));
            if (optionalCpg.isPresent()) {
                GcCpg cpg = optionalCpg.get();
                bean.setVoucherProgramGroup(cpg.getCpgName());
            }

            // Get merchant info from sales records
            String merchantCode = cardToMerchantMap.get(giftCard.getCardNumber());
            if (merchantCode != null) {
                Optional<Merchant> optionalMerchant = Optional.ofNullable(merchantMap.get(merchantCode));
                if (optionalMerchant.isPresent()) {
                    Merchant merchant = optionalMerchant.get();
                    bean.setMerchant(merchant.getMerchantName());

                    // Get company info
                    Optional<Company> optionalCompany = Optional.ofNullable(companyMap.get(merchant.getCompanyCode()));
                    if (optionalCompany.isPresent()) {
                        Company company = optionalCompany.get();
                        bean.setSbuCompanyName(company.getCompanyName());
                    }
                }
            }

            // Get outlet info from sales records
            String outletCode = cardToOutletMap.get(giftCard.getCardNumber());
            if (outletCode != null) {
                Optional<Outlet> optionalOutlet = Optional.ofNullable(outletMap.get(outletCode));
                optionalOutlet.ifPresent(outlet -> bean.setOutletName(outlet.getOutletName()));
            }

            return bean;
        }).collect(Collectors.toList());
    }

    /**
     * Get gift card list based on query parameters
     */
    private List<GiftCardEntity> getGiftCardList(GcLatestStatusQueryData queryData) {
        // Build query condition using WeekendSqls
        WeekendSqls<GiftCardEntity> condition = WeekendSqls.custom();

        // Filter by CPG codes
        if (CollectionUtils.isNotEmpty(queryData.getCpgCodeList())) {
            condition.andIn(GiftCardEntity::getCpgCode, queryData.getCpgCodeList());
        }

        // Filter by card status
        if (CollectionUtils.isNotEmpty(queryData.getCardStatusList())) {
            condition.andIn(GiftCardEntity::getStatus, queryData.getCardStatusList());
        }

        // Filter by card number list (from uploaded file)
        if (CollectionUtils.isNotEmpty(queryData.getCardNumberList())) {
            condition.andIn(GiftCardEntity::getCardNumber, queryData.getCardNumberList());
        }

        // Filter by card number range
        if (StringUtil.isNotBlank(queryData.getCardNumberStart())) {
            condition.andGreaterThanOrEqualTo(GiftCardEntity::getCardNumber, queryData.getCardNumberStart());
        }
        if (StringUtil.isNotBlank(queryData.getCardNumberEnd())) {
            condition.andLessThanOrEqualTo(GiftCardEntity::getCardNumber, queryData.getCardNumberEnd());
        }

        // Use pagination for large datasets
        return giftCardMapper.selectByCondition(condition);
    }

    /**
     * Get latest transaction type by querying all transaction tables and finding the one with max create_time
     */
    private String getLatestTransactionType(String cardNumber) {
        // Query each table separately and find the latest transaction
        String latestTransactionType = "";
        Date latestCreateTime = null;

        // Check gc_activation table
        Date activationTime = reportBusinessMapper.getLatestGcActivationTime(cardNumber);
        if (activationTime != null) {
            latestCreateTime = activationTime;
            latestTransactionType = "GIFT CARD ACTIVATED";
        }

        // Check gc_sales table
        Date salesTime = reportBusinessMapper.getLatestGcSalesTime(cardNumber);
        if (salesTime != null && (latestCreateTime == null || salesTime.after(latestCreateTime))) {
            latestCreateTime = salesTime;
            latestTransactionType = "GIFT CARD SOLD";
        }

        // Check gc_redemption table
        Date redemptionTime = reportBusinessMapper.getLatestGcRedemptionTime(cardNumber);
        if (redemptionTime != null && (latestCreateTime == null || redemptionTime.after(latestCreateTime))) {
            latestCreateTime = redemptionTime;
            latestTransactionType = "GIFT CARD REDEEMED";
        }

        // Check gc_block table
        Date blockTime = reportBusinessMapper.getLatestGcBlockTime(cardNumber);
        if (blockTime != null && (latestCreateTime == null || blockTime.after(latestCreateTime))) {
            latestCreateTime = blockTime;
            latestTransactionType = "GIFT CARD BLOCKED";
        }

        // Check gc_unblock table
        Date unblockTime = reportBusinessMapper.getLatestGcUnblockTime(cardNumber);
        if (unblockTime != null && (latestCreateTime == null || unblockTime.after(latestCreateTime))) {
            latestCreateTime = unblockTime;
            latestTransactionType = "GIFT CARD UNBLOCKED";
        }

        // Check gc_cancel_sales table
        Date cancelSalesTime = reportBusinessMapper.getLatestGcCancelSalesTime(cardNumber);
        if (cancelSalesTime != null && (latestCreateTime == null || cancelSalesTime.after(latestCreateTime))) {
            latestCreateTime = cancelSalesTime;
            latestTransactionType = "GIFT CARD SALES CANCELLED";
        }

        // Check gc_cancel_activation table
        Date cancelActivationTime = reportBusinessMapper.getLatestGcCancelActivationTime(cardNumber);
        if (cancelActivationTime != null && (latestCreateTime == null || cancelActivationTime.after(latestCreateTime))) {
            latestCreateTime = cancelActivationTime;
            latestTransactionType = "GIFT CARD ACTIVATION CANCELLED";
        }

        // Check gc_extend_activation_period table
        Date extendActivationTime = reportBusinessMapper.getLatestGcExtendActivationTime(cardNumber);
        if (extendActivationTime != null && (latestCreateTime == null || extendActivationTime.after(latestCreateTime))) {
            latestCreateTime = extendActivationTime;
            latestTransactionType = "GIFT CARD ACTIVATION EXTENDED";
        }

        return latestTransactionType;
    }

    /**
     * Get last action date from the latest transaction
     */
    private String getLastActionDate(String cardNumber) {
        Date latestCreateTime = null;

        // Check all transaction tables and find the latest create_time
        Date activationTime = reportBusinessMapper.getLatestGcActivationTime(cardNumber);
        if (activationTime != null) {
            latestCreateTime = activationTime;
        }

        Date salesTime = reportBusinessMapper.getLatestGcSalesTime(cardNumber);
        if (salesTime != null && (latestCreateTime == null || salesTime.after(latestCreateTime))) {
            latestCreateTime = salesTime;
        }

        Date redemptionTime = reportBusinessMapper.getLatestGcRedemptionTime(cardNumber);
        if (redemptionTime != null && (latestCreateTime == null || redemptionTime.after(latestCreateTime))) {
            latestCreateTime = redemptionTime;
        }

        Date blockTime = reportBusinessMapper.getLatestGcBlockTime(cardNumber);
        if (blockTime != null && (latestCreateTime == null || blockTime.after(latestCreateTime))) {
            latestCreateTime = blockTime;
        }

        Date unblockTime = reportBusinessMapper.getLatestGcUnblockTime(cardNumber);
        if (unblockTime != null && (latestCreateTime == null || unblockTime.after(latestCreateTime))) {
            latestCreateTime = unblockTime;
        }

        Date cancelSalesTime = reportBusinessMapper.getLatestGcCancelSalesTime(cardNumber);
        if (cancelSalesTime != null && (latestCreateTime == null || cancelSalesTime.after(latestCreateTime))) {
            latestCreateTime = cancelSalesTime;
        }

        Date cancelActivationTime = reportBusinessMapper.getLatestGcCancelActivationTime(cardNumber);
        if (cancelActivationTime != null && (latestCreateTime == null || cancelActivationTime.after(latestCreateTime))) {
            latestCreateTime = cancelActivationTime;
        }

        Date extendActivationTime = reportBusinessMapper.getLatestGcExtendActivationTime(cardNumber);
        if (extendActivationTime != null && (latestCreateTime == null || extendActivationTime.after(latestCreateTime))) {
            latestCreateTime = extendActivationTime;
        }

        return latestCreateTime != null ? DateUtil.format(latestCreateTime, "yyyy-MM-dd HH:mm:ss") : "";
    }

    /**
     * Get card number to merchant code mapping from sales records
     */
    private Map<String, String> getCardToMerchantMap(List<GiftCardEntity> giftCardList) {
        if (CollectionUtils.isEmpty(giftCardList)) {
            return Collections.emptyMap();
        }

        List<String> cardNumbers = giftCardList.stream()
                .map(GiftCardEntity::getCardNumber)
                .collect(Collectors.toList());

        List<Map<String, Object>> results = reportBusinessMapper.getCardToMerchantMapFromGcSales(cardNumbers);
        return results.stream()
                .collect(Collectors.toMap(
                        map -> (String) map.get("key"),
                        map -> (String) map.get("value"),
                        (existing, replacement) -> existing
                ));
    }

    /**
     * Get card number to outlet code mapping from sales records
     */
    private Map<String, String> getCardToOutletMap(List<GiftCardEntity> giftCardList) {
        if (CollectionUtils.isEmpty(giftCardList)) {
            return Collections.emptyMap();
        }

        List<String> cardNumbers = giftCardList.stream()
                .map(GiftCardEntity::getCardNumber)
                .collect(Collectors.toList());

        List<Map<String, Object>> results = reportBusinessMapper.getCardToOutletMapFromGcSales(cardNumbers);
        return results.stream()
                .collect(Collectors.toMap(
                        map -> (String) map.get("key"),
                        map -> (String) map.get("value"),
                        (existing, replacement) -> existing
                ));
    }
}
