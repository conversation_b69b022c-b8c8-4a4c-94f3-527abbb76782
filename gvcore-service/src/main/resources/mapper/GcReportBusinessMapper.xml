<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.gtech.gvcore.dao.mapper.GcReportBusinessMapper">

    <select id="gcSalesReport" parameterType="com.gtech.gvcore.service.report.impl.param.SalesDetailedQueryData"
            resultType="com.gtech.gvcore.service.report.impl.bo.GcSalesBo">
        SELECT
        sales.card_number as voucher_code,
        sales.sales_code as transaction_code,
        DATE_FORMAT(sales.sales_time, '%Y-%m-%d %H:%i:%S') AS transaction_date,
        sales.merchant_code,
        sales.outlet_code,
        sales.invoice_number,
        sales.cpg_code,
        card.denomination,
        sales.notes,
        sales.approval_code
        FROM
        gc_sales sales
        LEFT JOIN gc_gift_card card ON sales.card_number = card.card_number
        WHERE
        card.management_status != 'DESTROY'

        <if test="transactionDateStart != null and transactionDateEnd != null ">
            AND (sales.sales_time BETWEEN #{transactionDateStart} AND #{transactionDateEnd})
        </if>
        <if test="issuerCode != null and issuerCode.length() != 0">AND card.issuer_code = #{issuerCode}</if>
        <if test="merchantCodeList != null and merchantCodeList.size() != 0">
            AND sales.merchant_code IN
            <foreach collection="merchantCodeList" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="outletCodeList != null and outletCodeList.size() != 0">
            AND sales.outlet_code IN
            <foreach collection="outletCodeList" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="cpgCodeList != null and cpgCodeList.size() != 0">
            AND sales.cpg_code IN
            <foreach collection="cpgCodeList" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="invoiceNumber != null and invoiceNumber.length() != 0">AND sales.invoice_number = #{invoiceNumber}
        </if>
        order by sales.sales_time, sales.id
    </select>

    <select id="selectGcRedemption" parameterType="com.gtech.gvcore.service.report.impl.param.RedemptionQueryData"
            resultType="com.gtech.gvcore.service.report.impl.bo.GcRedemptionBo">
        SELECT
        DATE_FORMAT(t.redemption_time, '%Y-%m-%d %H:%i:%S') AS transaction_date,
        t.redemption_code AS transaction_code,
        t.merchant_code,
        t.outlet_code,
        t.invoice_number,
        t.amount,
        t.denomination,
        t.cpg_code,
        t.card_number,
        t.notes,
        balance_after,
        balance_before
        FROM
        gc_redemption t
        WHERE
        t.`status` = 'REDEEMED'

        <if test="transactionDateStart != null and transactionDateEnd != null ">
            AND (t.redemption_time BETWEEN #{transactionDateStart} AND #{transactionDateEnd})
        </if>
        <if test="invoiceNumber != null and invoiceNumber.trim().length() != 0 ">AND t.invoice_number =
            #{invoiceNumber}
        </if>
        <if test="merchantCodeList != null and merchantCodeList.size() != 0 ">
            AND t.merchant_code IN
            <foreach collection="merchantCodeList" item="item" open="( " separator=", " close=") ">
                #{item}
            </foreach>
        </if>
        <if test="outletCodeList != null and outletCodeList.size() != 0 ">
            AND t.outlet_code IN
            <foreach collection="outletCodeList" item="item" open="( " separator=", " close=") ">
                #{item}
            </foreach>
        </if>
        <if test="cpgCodeList != null and cpgCodeList.size() != 0 ">
            AND t.cpg_code IN
            <foreach collection="cpgCodeList" item="item" open="( " separator=", " close=") ">
                #{item}
            </foreach>
        </if>
        <if test="voucherCodeNumStart != null ">AND t.card_number <![CDATA[ >= ]]> #{voucherCodeNumStart}</if>
        <if test="voucherCodeNumEnd != null ">AND t.card_number <![CDATA[ <= ]]> #{voucherCodeNumEnd}</if>
        order by t.redemption_time, t.id
    </select>
    <select id="selectCancelSales" parameterType="com.gtech.gvcore.service.report.impl.param.CancelSalesQueryData" resultType="com.gtech.gvcore.service.report.impl.bo.GcCancelSalesBo">
        select
        card_number,
        cancel_code AS transaction_code,
        merchant_code,
        outlet_code,
        cpg_code,
        denomination,
        DATE_FORMAT(cancel_time, '%Y-%m-%d %H:%i:%S'),
        invoice_number,
        t.owner_customer as customer_code
        from gc_cancel_sales
        <where>
            <if test="transactionDateStart != null and transactionDateEnd != null ">
                AND (cancel_time BETWEEN #{transactionDateStart} AND #{transactionDateEnd})
            </if>
            <if test="issuerCode != null and issuerCode.trim().length() != 0">AND issuer_code = #{issuerCode}</if>
            <if test="merchantCodeList != null and merchantCodeList.size() != 0">
                AND merchant_code IN
                <foreach collection="merchantCodeList" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="outletCodeList != null and outletCodeList.size() != 0">
                AND outlet_code IN
                <foreach collection="outletCodeList" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="cpgCodeList != null and cpgCodeList.size() != 0">
                AND cpg_code IN
                <foreach collection="cpgCodeList" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="invoiceNumber != null and invoiceNumber.trim().length() != 0">AND invoice_number =
                #{invoiceNumber}
            </if>
            <if test="voucherCodeNumStart != null">AND card_number <![CDATA[ >= ]]> #{voucherCodeNumStart}</if>
            <if test="voucherCodeNumEnd != null">AND card_number <![CDATA[ <= ]]> #{voucherCodeNumEnd}</if>

            <if test="customerCodeList != null and customerCodeList.size() != 0">
                AND owner_customer IN
                <foreach collection="customerCodeList" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>

        </where>
    </select>

    <select id="selectGcDeactivated" resultType="com.gtech.gvcore.service.report.impl.bo.DeactivatedBo">

        SELECT
        t.card_number as transaction_code
        , t.card_number as voucher_code
        , t.merchant_code
        , t.outlet_code
        , t.cpg_code
        , t.denomination
        , t.owner_customer as customer_code
        , t.invoice_number
        , t.block_reason
        , DATE_FORMAT(t.block_time, '%Y%m%d%H%i%S') AS transaction_date
        FROM gc_block t
        <where>
            <if test="transactionDateStart != null and transactionDateEnd != null ">
                AND (t.block_time BETWEEN #{transactionDateStart} AND #{transactionDateEnd})
            </if>
            <if test="issuerCodeList != null and issuerCodeList.size() != 0">
                AND t.issuer_code IN
                <foreach collection="issuerCodeList" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="merchantCodeList != null and merchantCodeList.size() != 0">
                AND t.merchant_code IN
                <foreach collection="merchantCodeList" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="outletCodeList != null and outletCodeList.size() != 0">
                AND t.outlet_code IN
                <foreach collection="outletCodeList" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="cpgCodeList != null and cpgCodeList.size() != 0">
                AND t.cpg_code IN
                <foreach collection="cpgCodeList" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="invoiceNumber != null and invoiceNumber.trim().length() != 0"> AND t.invoice_number = #{invoiceNumber} </if>
            <if test="voucherCodeNumStart != null"> AND t.card_number <![CDATA[ >= ]]> #{voucherCodeNumStart} </if>
            <if test="voucherCodeNumEnd != null"> AND t.card_number <![CDATA[ <= ]]> #{voucherCodeNumEnd} </if>

        </where>
    </select>

    <select id="selectGcReactivated" resultType="com.gtech.gvcore.service.report.impl.bo.GcReactivatedBo">

        SELECT
        t.card_number as transaction_code
        , t.card_number as voucher_code
        , t.merchant_code
        , t.outlet_code
        , t.cpg_code
        , t.denomination
        , t.owner_customer
        , t.invoice_number
        , t.owner_customer as customer_code
        , t.unblock_reason
        , DATE_FORMAT(t.unblock_time, '%Y%m%d%H%i%S') AS transaction_date
        FROM gc_unblock t
        <where>
            <if test="transactionDateStart != null and transactionDateEnd != null ">
                AND (t.unblock_time BETWEEN #{transactionDateStart} AND #{transactionDateEnd})
            </if>
            <if test="issuerCodeList != null and issuerCodeList.size() != 0">
                AND t.issuer_code IN
                <foreach collection="issuerCodeList" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="merchantCodeList != null and merchantCodeList.size() != 0">
                AND t.merchant_code IN
                <foreach collection="merchantCodeList" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="outletCodeList != null and outletCodeList.size() != 0">
                AND t.outlet_code IN
                <foreach collection="outletCodeList" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="cpgCodeList != null and cpgCodeList.size() != 0">
                AND t.cpg_code IN
                <foreach collection="cpgCodeList" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="invoiceNumber != null and invoiceNumber.trim().length() != 0"> AND t.invoice_number = #{invoiceNumber} </if>
            <if test="voucherCodeNumStart != null"> AND t.card_number <![CDATA[ >= ]]> #{voucherCodeNumStart} </if>
            <if test="voucherCodeNumEnd != null"> AND t.card_number <![CDATA[ <= ]]> #{voucherCodeNumEnd} </if>

        </where>
    </select>

    <select id="selectGcExpiry" resultType="com.gtech.gvcore.service.report.impl.bo.GcExpiryBo">

        SELECT t.card_number as voucher_code
        , t.sales_outlet as outlet_code
        , t.cpg_code
        , t.denomination
        , t.balance
        , t.owner_customer
        , t.status as card_status
        , DATE_FORMAT(t.expiry_time, '%Y%m%d%H%i%S') AS expiry_time
        , DATE_FORMAT(t.sales_time, '%Y%m%d%H%i%S') AS sales_time
        , DATE_FORMAT(t.activation_time, '%Y%m%d%H%i%S') AS activation_time
        FROM gc_gift_card t
        <where>
            <if test="expiryDateStart != null and expiryDateEnd != null ">
                AND (t.expiry_time BETWEEN #{expiryDateStart} AND #{expiryDateEnd})
            </if>
            <if test="salesTimeStart != null and salesTimeEnd != null ">
                AND (t.sales_time BETWEEN #{salesTimeStart} AND #{salesTimeEnd})
            </if>
            <if test="activationTimeStart != null and activationTimeEnd != null ">
                AND (t.activation_time BETWEEN #{activationTimeStart} AND #{activationTimeEnd})
            </if>
            <if test="issuerCodeList != null and issuerCodeList.size() != 0">
                AND t.issuer_code IN
                <foreach collection="issuerCodeList" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="outletCodeList != null and outletCodeList.size() != 0">
                AND t.sales_outlet IN
                <foreach collection="outletCodeList" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="cpgCodeList != null and cpgCodeList.size() != 0">
                AND t.cpg_code IN
                <foreach collection="cpgCodeList" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="customerCodeList != null and customerCodeList.size() != 0">
                AND t.owner_customer IN
                <foreach collection="customerCodeList" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="cardStatusList != null and cardStatusList.size() != 0">
                AND t.status IN
                <foreach collection="cardStatusList" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>


        </where>
    </select>

    <select id="selectGcExtendExpiry" resultType="com.gtech.gvcore.service.report.impl.bo.GcExtendExpiryBo">

        SELECT t.card_number
        , t.extension_code
        , t.outlet_code
        , t.extension_count
        , t.issuer_code
        , t.merchant_code
        , t.invoice_number
        , t.approval_code
        , t.notes
        , t.batch_number
        , t.source
        , DATE_FORMAT(t.extension_time, '%Y%m%d%H%i%S') AS extension_time
        , DATE_FORMAT(t.old_activation_deadline, '%Y%m%d%H%i%S') AS old_activation_deadline
        , DATE_FORMAT(t.new_activation_deadline, '%Y%m%d%H%i%S') AS new_activation_deadline
        , gc.cpg_code
        , gc.owner_customer
        , COALESCE(gc.balance, gc.denomination) AS amount
        , DATE_FORMAT(gc.sales_time, '%Y%m%d%H%i%S') AS sales_time
        , DATE_FORMAT(gc.activation_time, '%Y%m%d%H%i%S') AS activation_time
        FROM gc_extend_activation_period t
        LEFT JOIN gc_gift_card gc ON t.card_number = gc.card_number

        <where>
            <if test="transactionDateStart != null and transactionDateEnd != null ">
                AND (t.extension_time BETWEEN #{transactionDateStart} AND #{transactionDateEnd})
            </if>
            <if test="issuerCodeList != null and issuerCodeList.size() != 0">
                AND t.issuer_code IN
                <foreach collection="issuerCodeList" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="merchantCodeList != null and merchantCodeList.size() != 0">
                AND t.merchant_code IN
                <foreach collection="merchantCodeList" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="outletCodeList != null and outletCodeList.size() != 0">
                AND t.outlet_code IN
                <foreach collection="outletCodeList" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="cpgCodeList != null and cpgCodeList.size() != 0">
                AND gc.cpg_code IN
                <foreach collection="cpgCodeList" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="sourceList != null and sourceList.size() != 0">
                AND t.source IN
                <foreach collection="sourceList" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
        </where>
    </select>

</mapper>