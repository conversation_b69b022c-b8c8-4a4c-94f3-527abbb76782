package com.gtech.gvcore.common.config;

import org.apache.shardingsphere.api.sharding.standard.RangeShardingAlgorithm;
import org.apache.shardingsphere.api.sharding.standard.RangeShardingValue;

import java.util.Collection;
import java.util.LinkedHashSet;
import java.util.Set;

/**
 * <AUTHOR>
 * @Date 2023/2/8 17:10
 */
public class VoucherRangeShardingConfig implements RangeShardingAlgorithm<String> {
    /**
     * Sharding.
     *
     * @param availableTargetNames available data sources or tables's names
     * @param shardingValue        sharding value
     * @return sharding results for data sources or tables's names
     */
    @Override
    public Collection<String> doSharding(Collection<String> availableTargetNames, RangeShardingValue<String> shardingValue) {

        Set<String> result = new LinkedHashSet<>();
        // between 起始值

        String lower = shardingValue.getValueRange().lowerEndpoint();

        // between 结束值

        String upper = shardingValue.getValueRange().upperEndpoint();

        //不去除字母的情况下转为全数字
        upper = upper.replaceAll("[a-zA-Z]", "");
        lower = lower.replaceAll("[a-zA-Z]", "");

        // 循环范围计算分库逻辑

        for (long i = Long.valueOf(lower); i <= Long.valueOf(upper); i++) {

            for (String databaseName : availableTargetNames) {

                if (databaseName.endsWith(i % availableTargetNames.size() + "")) {

                    result.add(databaseName);

                }

            }

        }

        return result;
    }
}
