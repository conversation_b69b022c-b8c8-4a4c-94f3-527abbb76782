<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.gtech.gvcore.dao.mapper.IssueHandlingDetailsMapper">
  <resultMap id="BaseResultMap" type="com.gtech.gvcore.dao.model.IssueHandlingDetails">
    <!--
      WARNING - @mbg.generated
    -->
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="issue_handling_details_code" jdbcType="VARCHAR" property="issueHandlingDetailsCode" />
    <result column="issue_handling_code" jdbcType="VARCHAR" property="issueHandlingCode" />
    <result column="voucher_code" jdbcType="VARCHAR" property="voucherCode" />
    <result column="issue_type" jdbcType="VARCHAR" property="issueType" />
    <result column="denomination" jdbcType="DECIMAL" property="denomination" />
    <result column="voucher_owner_code" jdbcType="VARCHAR" property="voucherOwnerCode" />
    <result column="cpg_code" jdbcType="VARCHAR" property="cpgCode" />
    <result column="row_of_sheet" jdbcType="INTEGER" property="rowOfSheet" />
    <result column="invoice_no" jdbcType="VARCHAR" property="invoiceNo" />
    <result column="approval_code" jdbcType="VARCHAR" property="approvalCode" />
    <result column="voucher_effective_date" jdbcType="TIMESTAMP" property="voucherEffectiveDate" />
    <result column="new_voucher_code" jdbcType="VARCHAR" property="newVoucherCode" />
    <result column="old_activation_code" jdbcType="VARCHAR" property="oldActivationCode" />
    <result column="receiver_email" jdbcType="VARCHAR" property="receiverEmail" />
    <result column="remarks" jdbcType="VARCHAR" property="remarks" />
    <result column="process_status" jdbcType="INTEGER" property="processStatus" />
    <result column="result" jdbcType="VARCHAR" property="result" />
    <result column="permission_code" jdbcType="VARCHAR" property="permissionCode" />
    <result column="create_user" jdbcType="VARCHAR" property="createUser" />
    <result column="outlet_name" jdbcType="VARCHAR" property="outletName" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_user" jdbcType="VARCHAR" property="updateUser" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="amount" jdbcType="DECIMAL" property="amount" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--
      WARNING - @mbg.generated
    -->
    id, issue_handling_details_code, issue_handling_code, voucher_code, denomination,  issue_type,
    voucher_owner_code, cpg_code, row_of_sheet, invoice_no, approval_code, voucher_effective_date, 
    new_voucher_code, receiver_email, remarks, process_status, result, permission_code, 
    create_user, create_time, update_user, update_time,outlet_name , old_activation_code,amount
  </sql>
  <sql id="gv_issue_handling_details_query_condition">
    <trim prefix="WHERE" prefixOverrides="AND |OR ">
      <if test="issueHandlingDetailsCode != null and issueHandlingDetailsCode.trim().length() != 0">
        AND (issue_handling_details_code = #{issueHandlingDetailsCode})
      </if>
      <if test="issueHandlingCode != null and issueHandlingCode.trim().length() != 0">
        AND (issue_handling_code = #{issueHandlingCode})
      </if>
      <if test="voucherCode != null and voucherCode.trim().length() != 0">
        AND (voucher_code = #{voucherCode})
      </if>
      <if test="denomination != null">
        AND (denomination = #{denomination})
      </if>
      <if test="issueType != null and issueType.trim().length() != 0">
        AND (issue_type = #{issueType})
      </if>
      <if test="voucherOwnerCode != null and voucherOwnerCode.trim().length() != 0">
        AND (voucher_owner_code = #{voucherOwnerCode})
      </if>
      <if test="cpgCode != null and cpgCode.trim().length() != 0">
        AND (cpg_code = #{cpgCode})
      </if>
      <if test="rowOfSheet != null">
        AND (row_of_sheet = #{rowOfSheet})
      </if>
      <if test="invoiceNo != null and invoiceNo.trim().length() != 0">
        AND (invoice_no = #{invoiceNo})
      </if>
      <if test="approvalCode != null and approvalCode.trim().length() != 0">
        AND (approval_code = #{approvalCode})
      </if>
      <if test="voucherEffectiveDate != null">
        AND (voucher_effective_date = #{voucherEffectiveDate})
      </if>
      <if test="newVoucherCode != null and newVoucherCode.trim().length() != 0">
        AND (new_voucher_code = #{newVoucherCode})
      </if>
      <if test="receiverEmail != null and receiverEmail.trim().length() != 0">
        AND (receiver_email = #{receiverEmail})
      </if>
      <if test="remarks != null and remarks.trim().length() != 0">
        AND (remarks = #{remarks})
      </if>
      <if test="processStatus != null">
        AND (process_status = #{processStatus})
      </if>
      <if test="result != null and result.trim().length() != 0">
        AND (result = #{result})
      </if>
      <if test="permissionCode != null and permissionCode.trim().length() != 0">
        AND (permission_code = #{permissionCode})
      </if>
      <if test="createUser != null and createUser.trim().length() != 0">
        AND (create_user = #{createUser})
      </if>
      <if test="createTime != null">
        AND (create_time = #{createTime})
      </if>
      <if test="updateUser != null and updateUser.trim().length() != 0">
        AND (update_user = #{updateUser})
      </if>
      <if test="updateTime != null">
        AND (update_time = #{updateTime})
      </if>
    </trim>
  </sql>
  
  <select id="queryByIssueHandlingCode" parameterType="com.gtech.gvcore.dao.dto.IssueHandlingDetailsDto" resultMap="BaseResultMap">
    SELECT <include refid="Base_Column_List" /> 
    FROM gv_issue_handling_details 
    WHERE issue_handling_code = #{issueHandlingCode} 
    	<if test="processStatus != null">
	      AND process_status = #{processStatus} 
    	</if>
      AND id &gt;= #{startId} 
    ORDER BY id LIMIT #{pageSize}
  </select>
  
  <select id="queryDigitalByIssueHandlingCode" parameterType="com.gtech.gvcore.dao.dto.IssueHandlingDetailsDto" resultMap="BaseResultMap">
    SELECT <include refid="Base_Column_List" /> 
    FROM gv_issue_handling_details 
    WHERE issue_handling_code = #{issueHandlingCode} 
      AND process_status = 2
      AND receiver_email is not null
      AND voucher_code not like '100%'
    ORDER BY id
  </select>
  
  
  <update id="updateByProcess" parameterType="com.gtech.gvcore.dao.dto.IssueHandlingDetailsDto">
  	UPDATE gv_issue_handling_details 
  	   SET process_status =
		   <foreach collection="detailList" item="item" open="CASE id " close=" END,">
		   	WHEN #{item.id} THEN #{item.processStatus}
		   </foreach>
  	   	   result = 
  	   	   <foreach collection="detailList" item="item" open="CASE id " close=" END,">
		   	WHEN #{item.id} THEN #{item.result}
		   </foreach>
		   update_user = #{updateUser},
		   update_time = #{updateTime} 
  	WHERE id IN
  	<foreach collection="detailList" item="item" open="(" separator="," close=")">
  		#{item.id}
  	</foreach>
  </update>

</mapper>