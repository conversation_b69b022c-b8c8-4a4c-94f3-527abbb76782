package com.gtech.gvcore.service.report.impl;

import com.gtech.commons.utils.DateUtil;
import com.gtech.gvcore.common.enums.ReportExportTypeEnum;
import com.gtech.gvcore.common.request.orderreport.CreateReportRequest;
import com.gtech.gvcore.dao.mapper.CustomerOrderMapper;
import com.gtech.gvcore.dao.mapper.TransactionDataMapper;
import com.gtech.gvcore.dao.model.*;
import com.gtech.gvcore.service.IssueHandlingService;
import com.gtech.gvcore.service.report.BusinessReport;
import com.gtech.gvcore.service.report.SingleReport;
import com.gtech.gvcore.service.report.extend.joindate.JoinDataMap;
import com.gtech.gvcore.service.report.impl.bean.GcSalesSummaryBean;
import com.gtech.gvcore.service.report.impl.bo.GcSalesBo;
import com.gtech.gvcore.service.report.impl.param.GcSalesQueryData;
import com.gtech.gvcore.service.report.impl.support.GcSalesBaseImpl;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

/**
 * @Author: gao.yuhua
 * @Date: 2022/5/11 14:01
 * @Description: 0.24 - 1.02 59.58s,
 */
@Service
public class GcSalesSummaryImpl extends GcSalesBaseImpl<GcSalesSummaryBean>
        implements BusinessReport<GcSalesQueryData, GcSalesSummaryBean>, SingleReport {

    @Autowired
    protected IssueHandlingService issueHandlingService;
    @Autowired
    protected CustomerOrderMapper customerOrderMapper;
    @Autowired
    TransactionDataMapper transactionDataMapper;

    @Override
    public ReportExportTypeEnum exportTypeEnum() {
        return ReportExportTypeEnum.GC_SALES_SUMMARY_REPORT;
    }

    @Override
    protected void addParam(GcSalesQueryData param, CreateReportRequest reportParam) {

    }

    @Override
    public List<GcSalesSummaryBean> getExportData(List<GcSalesBo> boList) {

        final Map<String, GcSalesSummaryBo> resultMap = new HashMap<>();

        boList.forEach(e -> {

            final String key = StringUtils.join(e.getSalesCode(), e.getMerchantCode(), e.getOutletCode(), e.getInvoiceNumber(), e.getCpgCode());

            final GcSalesSummaryBo salesSummaryBo = resultMap.computeIfAbsent(key, k -> GcSalesSummaryBo.newInstance(e));

            salesSummaryBo.addSalesCount().addSalesAmount(e.getDenomination());
        });

        final List<GcSalesSummaryBo> resultList = new ArrayList<>(resultMap.values());

        final JoinDataMap<Outlet> outletMap = super.getMapByCode(resultList, GcSalesSummaryBo::getOutletCode, Outlet.class);
        final JoinDataMap<Cpg> cpgMap = super.getMapByCode(resultList, GcSalesSummaryBo::getCpgCode, Cpg.class);
        final JoinDataMap<Merchant> merchantMap = super.getMapByCode(resultList, GcSalesSummaryBo::getMerchantCode, Merchant.class);
        final JoinDataMap<Company> companyMap = super.getMapByCode(merchantMap.values(), Merchant::getCompanyCode, Company.class);

        return resultList.stream()
                .map(e -> {

                    final Merchant merchant = merchantMap.findValue(e.getMerchantCode());
                    final Outlet outlet = outletMap.findValue(e.getOutletCode());
                    final Cpg cpg = cpgMap.findValue(e.getCpgCode());
                    final Company company = companyMap.findValue(merchant.getCompanyCode());

                    return new GcSalesSummaryBean()
                            .setDate(e.getDate())
                            .setInvoiceNumber(e.getInvoiceNumber())
                            .setSubCompanyName(company.getCompanyName())
                            .setMerchantName(merchant.getMerchantName())
                            .setMerchantOutletName(outlet.getOutletName())
                            .setSalesCount(e.getSalesCount().toString())
                            .setSalesAmount(super.toAmount(e.getSalesAmount()))
                            .setCpgCode(cpg.getCpgCode())
                            .setNotes(e.getNotes());
                }).collect(Collectors.toList());
    }


    @Getter
    @Setter
    @Accessors(chain = true)
    public static class GcSalesSummaryBo {

        private String date;

        private String saleCode;

        private String merchantCode;

        private String outletCode;

        private String invoiceNumber;

        private String cpgCode;

        private Integer salesCount = 0;

        private BigDecimal salesAmount = BigDecimal.ZERO;

        private String notes;

        public static GcSalesSummaryBo newInstance(GcSalesBo bo) {

            return newInstance(DateUtil.format(bo.getTransactionDate(), DateUtil.FORMAT_YYYYMMDDHHMISS), bo.getSalesCode(), bo.getMerchantCode(), bo.getOutletCode(), bo.getInvoiceNumber(), bo.getCpgCode(), bo.getNotes());
        }

        public static GcSalesSummaryBo newInstance(String date, String saleCode, String merchantCode, String outletCode, String invoiceNumber, String cpgCode, String notes) {

            return new GcSalesSummaryBo()
                    .setDate(date)
                    .setSaleCode(saleCode)
                    .setMerchantCode(merchantCode)
                    .setOutletCode(outletCode)
                    .setInvoiceNumber(invoiceNumber)
                    .setCpgCode(cpgCode)
                    .setNotes(notes);
        }

        public GcSalesSummaryBo addSalesCount() {
            this.salesCount += 1;
            return this;
        }

        public void addSalesAmount(BigDecimal amount) {
            this.salesAmount = this.salesAmount.add(amount);
        }

    }

}
