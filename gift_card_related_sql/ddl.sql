ALTER TABLE `gv_issue_handling_details`
    ADD COLUMN `amount` decimal(10, 2) NULL COMMENT 'amount';

ALTER TABLE `gc_sales`
    ADD COLUMN `denomination` decimal(20, 3) NULL DEFAULT NULL ;
-- 菜单
-- issue handling父菜单
INSERT INTO `idm_menu` ( `domain_code`, `app_code`, `menu_parent`, `menu_code`, `resource_code`, `menu_name`, `menu_desc`, `menu_icon`, `display_order`, `page_url`, `status`, `create_user`, `create_time`, `update_user`, `update_time`, `logic_delete`, `menu_name_lang`, `menu_lang`, `menu_type`) VALUES ( 'SYSTEM_DEFAULT', 'GV', '0', 'M000700', 'm_gc_issueHandling', 'GC Issue Handling', NULL, NULL, 7, NULL, 1, NULL, '2022-03-31 10:26:46', NULL, '2022-03-31 10:27:04', 0, NULL, '[{\"language\": \"zh-CN\", \"menuName\": \"\"}]', '2');
-- Block/Temporary Deactivated 子菜单
INSERT INTO `idm_menu` ( `domain_code`, `app_code`, `menu_parent`, `menu_code`, `resource_code`, `menu_name`, `menu_desc`, `menu_icon`, `display_order`, `page_url`, `status`, `create_user`, `create_time`, `update_user`, `update_time`, `logic_delete`, `menu_name_lang`, `menu_lang`, `menu_type`) VALUES ('SYSTEM_DEFAULT', 'GV', 'M000700', 'M000701', 'm_gc_issue_block', 'Block/Temporary Deactivated', NULL, NULL, 10, '/gc/order/issue/handling/bulk_deactivate', 1, NULL, '2022-05-12 13:01:22', NULL, '2022-05-12 13:09:35', 0, NULL, '[{\"language\": \"zh-CN\", \"menuName\": \"Block/Temporary Deactivated\"}]', '2');
-- Unblock Reactivated 子菜单
INSERT INTO `idm_menu` ( `domain_code`, `app_code`, `menu_parent`, `menu_code`, `resource_code`, `menu_name`, `menu_desc`, `menu_icon`, `display_order`, `page_url`, `status`, `create_user`, `create_time`, `update_user`, `update_time`, `logic_delete`, `menu_name_lang`, `menu_lang`, `menu_type`) VALUES ('SYSTEM_DEFAULT', 'GV', 'M000700', 'M000702', 'm_gc_issue_unblock', 'Unblock Reactivated', NULL, NULL, 20, '/gc/order/issue/handling/bulk_reactivate', 1, NULL, '2022-05-12 13:01:22', NULL, '2022-05-12 13:09:35', 0, NULL, '[{\"language\": \"zh-CN\", \"menuName\": \"Unblock Reactivated\"}]', '2');
-- Extend Expiry Date 子菜单
INSERT INTO `idm_menu` ( `domain_code`, `app_code`, `menu_parent`, `menu_code`, `resource_code`, `menu_name`, `menu_desc`, `menu_icon`, `display_order`, `page_url`, `status`, `create_user`, `create_time`, `update_user`, `update_time`, `logic_delete`, `menu_name_lang`, `menu_lang`, `menu_type`) VALUES ('SYSTEM_DEFAULT', 'GV', 'M000700', 'M000703', 'm_gc_issue_extend_expiry', 'Extend Expiry Date', NULL, NULL, 30, '/gc/order/issue/handling/bulk_change_expiry', 1, NULL, '2022-05-12 13:01:22', NULL, '2022-05-12 13:09:35', 0, NULL, '[{\"language\": \"zh-CN\", \"menuName\": \"Extend Expiry Date\"}]', '2');
-- Regenerate Activation Code 子菜单
INSERT INTO `idm_menu` ( `domain_code`, `app_code`, `menu_parent`, `menu_code`, `resource_code`, `menu_name`, `menu_desc`, `menu_icon`, `display_order`, `page_url`, `status`, `create_user`, `create_time`, `update_user`, `update_time`, `logic_delete`, `menu_name_lang`, `menu_lang`, `menu_type`) VALUES ('SYSTEM_DEFAULT', 'GV', 'M000700', 'M000704', 'm_gc_issue_regenerate_code', 'Regenerate Activation Code', NULL, NULL, 40, '/gc/order/issue/handling/bulk_regenerate_activation_code', 1, NULL, '2022-05-12 13:01:22', NULL, '2022-05-12 13:09:35', 0, NULL, '[{\"language\": \"zh-CN\", \"menuName\": \"Regenerate Activation Code\"}]', '2');
-- Bulk Redeem 子菜单
INSERT INTO `idm_menu` ( `domain_code`, `app_code`, `menu_parent`, `menu_code`, `resource_code`, `menu_name`, `menu_desc`, `menu_icon`, `display_order`, `page_url`, `status`, `create_user`, `create_time`, `update_user`, `update_time`, `logic_delete`, `menu_name_lang`, `menu_lang`, `menu_type`) VALUES ('SYSTEM_DEFAULT', 'GV', 'M000700', 'M000705', 'm_gc_issue_redeem', 'Bulk Redeem', NULL, NULL, 50, '/gc/order/issue/handling/bulk_redeem', 1, NULL, '2022-05-12 13:01:22', NULL, '2022-05-12 13:09:35', 0, NULL, '[{\"language\": \"zh-CN\", \"menuName\": \"Bulk Redeem\"}]', '2');
-- Bulk Cancel Redeem 子菜单
INSERT INTO `idm_menu` ( `domain_code`, `app_code`, `menu_parent`, `menu_code`, `resource_code`, `menu_name`, `menu_desc`, `menu_icon`, `display_order`, `page_url`, `status`, `create_user`, `create_time`, `update_user`, `update_time`, `logic_delete`, `menu_name_lang`, `menu_lang`, `menu_type`) VALUES ('SYSTEM_DEFAULT', 'GV', 'M000700', 'M000706', 'm_gc_issue_cancel_redeem', 'Bulk Cancel Redeem', NULL, NULL, 60, '/gc/order/issue/handling/bulk_cancel_redeem', 1, NULL, '2022-05-12 13:01:22', NULL, '2022-05-12 13:09:35', 0, NULL, '[{\"language\": \"zh-CN\", \"menuName\": \"Bulk Cancel Redeem\"}]', '2');
-- Cancel Sales 子菜单
INSERT INTO `idm_menu` ( `domain_code`, `app_code`, `menu_parent`, `menu_code`, `resource_code`, `menu_name`, `menu_desc`, `menu_icon`, `display_order`, `page_url`, `status`, `create_user`, `create_time`, `update_user`, `update_time`, `logic_delete`, `menu_name_lang`, `menu_lang`, `menu_type`) VALUES ('SYSTEM_DEFAULT', 'GV', 'M000700', 'M000707', 'm_gc_issue_cancel_sales', 'Bulk Cancel Sales', NULL, NULL, 70, '/gc/order/issue/handling/bulk_cancel_sales', 1, NULL, '2022-05-12 13:01:22', NULL, '2022-05-12 13:09:35', 0, NULL, '[{\"language\": \"zh-CN\", \"menuName\": \"Bulk Cancel Sales\"}]', '2');

-- 资源
-- issue handling父菜单资源
INSERT INTO `idm_resource` ( `domain_code`, `app_code`, `resource_parent`, `resource_code`, `resource_name`, `resource_type`, `resource_class`, `access_type`, `status`, `create_user`, `create_time`, `update_user`, `update_time`, `logic_delete`, `position_code`, `resource_url`, `log_switch`, `log_business_code`, `log_desc`, `log_business`) VALUES ( 'SYSTEM_DEFAULT', 'GV', 'root', 'm_gc_issueHandling', 'GC Issue Handling', 2, 1, 3, 1, NULL, '2022-03-31 10:14:36', NULL, '2023-03-10 13:56:03', 0, NULL, NULL, 0, NULL, NULL, NULL);
-- Block/Temporary Deactivated 子菜单资源
INSERT INTO `idm_resource` ( `domain_code`, `app_code`, `resource_parent`, `resource_code`, `resource_name`, `resource_type`, `resource_class`, `access_type`, `status`, `create_user`, `create_time`, `update_user`, `update_time`, `logic_delete`, `position_code`, `resource_url`, `log_switch`, `log_business_code`, `log_desc`, `log_business`) VALUES ( 'SYSTEM_DEFAULT', 'GV', 'm_gc_issueHandling', 'm_gc_issue_block', 'Block/Temporary Deactivated', 2, 1, 3, 1, NULL, '2022-04-14 13:59:34', NULL, '2023-03-10 13:56:04', 0, NULL, NULL, 0, NULL, NULL, NULL);
-- Unblock Reactivated 子菜单资源
INSERT INTO `idm_resource` ( `domain_code`, `app_code`, `resource_parent`, `resource_code`, `resource_name`, `resource_type`, `resource_class`, `access_type`, `status`, `create_user`, `create_time`, `update_user`, `update_time`, `logic_delete`, `position_code`, `resource_url`, `log_switch`, `log_business_code`, `log_desc`, `log_business`) VALUES ( 'SYSTEM_DEFAULT', 'GV', 'm_gc_issueHandling', 'm_gc_issue_unblock', 'Unblock Reactivated', 2, 1, 3, 1, NULL, '2022-04-14 13:59:34', NULL, '2023-03-10 13:56:04', 0, NULL, NULL, 0, NULL, NULL, NULL);
-- Extend Expiry Date 子菜单资源
INSERT INTO `idm_resource` ( `domain_code`, `app_code`, `resource_parent`, `resource_code`, `resource_name`, `resource_type`, `resource_class`, `access_type`, `status`, `create_user`, `create_time`, `update_user`, `update_time`, `logic_delete`, `position_code`, `resource_url`, `log_switch`, `log_business_code`, `log_desc`, `log_business`) VALUES ( 'SYSTEM_DEFAULT', 'GV', 'm_gc_issueHandling', 'm_gc_issue_extend_expiry', 'Extend Expiry Date', 2, 1, 3, 1, NULL, '2022-04-14 13:59:34', NULL, '2023-03-10 13:56:04', 0, NULL, NULL, 0, NULL, NULL, NULL);
-- Regenerate Activation Code 子菜单资源
INSERT INTO `idm_resource` ( `domain_code`, `app_code`, `resource_parent`, `resource_code`, `resource_name`, `resource_type`, `resource_class`, `access_type`, `status`, `create_user`, `create_time`, `update_user`, `update_time`, `logic_delete`, `position_code`, `resource_url`, `log_switch`, `log_business_code`, `log_desc`, `log_business`) VALUES ( 'SYSTEM_DEFAULT', 'GV', 'm_gc_issueHandling', 'm_gc_issue_regenerate_code', 'Regenerate Activation Code', 2, 1, 3, 1, NULL, '2022-04-14 13:59:34', NULL, '2023-03-10 13:56:04', 0, NULL, NULL, 0, NULL, NULL, NULL);
-- Bulk Redeem 子菜单资源
INSERT INTO `idm_resource` ( `domain_code`, `app_code`, `resource_parent`, `resource_code`, `resource_name`, `resource_type`, `resource_class`, `access_type`, `status`, `create_user`, `create_time`, `update_user`, `update_time`, `logic_delete`, `position_code`, `resource_url`, `log_switch`, `log_business_code`, `log_desc`, `log_business`) VALUES ( 'SYSTEM_DEFAULT', 'GV', 'm_gc_issueHandling', 'm_gc_issue_redeem', 'Bulk Redeem', 2, 1, 3, 1, NULL, '2022-04-14 13:59:34', NULL, '2023-03-10 13:56:04', 0, NULL, NULL, 0, NULL, NULL, NULL);
-- Bulk Cancel Redeem 子菜单资源
INSERT INTO `idm_resource` ( `domain_code`, `app_code`, `resource_parent`, `resource_code`, `resource_name`, `resource_type`, `resource_class`, `access_type`, `status`, `create_user`, `create_time`, `update_user`, `update_time`, `logic_delete`, `position_code`, `resource_url`, `log_switch`, `log_business_code`, `log_desc`, `log_business`) VALUES ( 'SYSTEM_DEFAULT', 'GV', 'm_gc_issueHandling', 'm_gc_issue_cancel_redeem', 'Bulk Cancel Redeem', 2, 1, 3, 1, NULL, '2022-04-14 13:59:34', NULL, '2023-03-10 13:56:04', 0, NULL, NULL, 0, NULL, NULL, NULL);
-- Cancel Sales 子菜单资源
INSERT INTO `idm_resource` ( `domain_code`, `app_code`, `resource_parent`, `resource_code`, `resource_name`, `resource_type`, `resource_class`, `access_type`, `status`, `create_user`, `create_time`, `update_user`, `update_time`, `logic_delete`, `position_code`, `resource_url`, `log_switch`, `log_business_code`, `log_desc`, `log_business`) VALUES ( 'SYSTEM_DEFAULT', 'GV', 'm_gc_issueHandling', 'm_gc_issue_cancel_sales', 'Bulk Cancel Sales', 2, 1, 3, 1, NULL, '2022-04-14 13:59:34', NULL, '2023-03-10 13:56:04', 0, NULL, NULL, 0, NULL, NULL, NULL);

-- 权限资源
INSERT INTO `idm_resource` (`domain_code`, `app_code`, `resource_parent`, `resource_code`, `resource_name`, `resource_type`, `resource_class`, `access_type`, `status`, `create_user`, `create_time`, `update_user`, `update_time`, `logic_delete`, `position_code`, `resource_url`, `log_switch`, `log_business_code`, `log_desc`, `log_business`) VALUES ( 'SYSTEM_DEFAULT', 'GV', 'm_gc_issue_block', 'queryGcIssueHandlingBlocked', 'queryGcIssueHandlingBlocked', 3, 1, 3, 1, NULL, '2022-05-05 08:59:00', NULL, '2022-05-06 08:52:46', 0, NULL, 'queryIssueHandlingList', 0, NULL, NULL, NULL);
INSERT INTO `idm_resource` (`domain_code`, `app_code`, `resource_parent`, `resource_code`, `resource_name`, `resource_type`, `resource_class`, `access_type`, `status`, `create_user`, `create_time`, `update_user`, `update_time`, `logic_delete`, `position_code`, `resource_url`, `log_switch`, `log_business_code`, `log_desc`, `log_business`) VALUES ( 'SYSTEM_DEFAULT', 'GV', 'm_gc_issue_block', 'getGcIssueHandlingBlocked', 'getGcIssueHandlingBlocked', 3, 1, 3, 1, NULL, '2022-05-05 08:59:01', NULL, '2022-05-06 08:52:46', 0, NULL, 'queryIssueHandling', 0, NULL, NULL, NULL);
INSERT INTO `idm_resource` (`domain_code`, `app_code`, `resource_parent`, `resource_code`, `resource_name`, `resource_type`, `resource_class`, `access_type`, `status`, `create_user`, `create_time`, `update_user`, `update_time`, `logic_delete`, `position_code`, `resource_url`, `log_switch`, `log_business_code`, `log_desc`, `log_business`) VALUES ( 'SYSTEM_DEFAULT', 'GV', 'm_gc_issue_block', 'updateGcIssueHandlingBlocked', 'Create/Edit Gc Issue Blocked', 4, 1, 3, 1, NULL, '2022-05-05 08:59:01', NULL, '2022-05-09 10:44:30', 0, NULL, 'updateIssueHandling', 0, NULL, NULL, NULL);
INSERT INTO `idm_resource` (`domain_code`, `app_code`, `resource_parent`, `resource_code`, `resource_name`, `resource_type`, `resource_class`, `access_type`, `status`, `create_user`, `create_time`, `update_user`, `update_time`, `logic_delete`, `position_code`, `resource_url`, `log_switch`, `log_business_code`, `log_desc`, `log_business`) VALUES ( 'SYSTEM_DEFAULT', 'GV', 'm_gc_issue_block', 'submitGcIssueHandlingBlocked', 'Submit Gc Issue Blocked', 4, 1, 3, 1, NULL, '2022-05-05 08:59:01', NULL, '2022-05-09 10:44:30', 0, NULL, 'updateIssueHandling', 0, NULL, NULL, NULL);
INSERT INTO `idm_resource` (`domain_code`, `app_code`, `resource_parent`, `resource_code`, `resource_name`, `resource_type`, `resource_class`, `access_type`, `status`, `create_user`, `create_time`, `update_user`, `update_time`, `logic_delete`, `position_code`, `resource_url`, `log_switch`, `log_business_code`, `log_desc`, `log_business`) VALUES ( 'SYSTEM_DEFAULT', 'GV', 'm_gc_issue_block', 'approveGcIssueHandlingBlocked', 'Approve Gc Issue Blocked', 4, 1, 3, 1, NULL, '2022-05-05 08:59:01', NULL, '2022-05-09 10:44:57', 0, NULL, 'approveIssueHandling', 0, NULL, NULL, NULL);
INSERT INTO `idm_resource` (`domain_code`, `app_code`, `resource_parent`, `resource_code`, `resource_name`, `resource_type`, `resource_class`, `access_type`, `status`, `create_user`, `create_time`, `update_user`, `update_time`, `logic_delete`, `position_code`, `resource_url`, `log_switch`, `log_business_code`, `log_desc`, `log_business`) VALUES ( 'SYSTEM_DEFAULT', 'GV', 'm_gc_issue_block', 'cancelGcIssueHandlingBlocked', 'Cancel Gc Issue Blocked', 4, 1, 3, 1, NULL, '2022-05-05 08:59:01', NULL, '2022-05-09 10:45:07', 0, NULL, 'cancelIssueHandling', 0, NULL, NULL, NULL);

INSERT INTO `idm_resource` (`domain_code`, `app_code`, `resource_parent`, `resource_code`, `resource_name`, `resource_type`, `resource_class`, `access_type`, `status`, `create_user`, `create_time`, `update_user`, `update_time`, `logic_delete`, `position_code`, `resource_url`, `log_switch`, `log_business_code`, `log_desc`, `log_business`) VALUES ( 'SYSTEM_DEFAULT', 'GV', 'm_gc_issue_unblock', 'queryGcIssueHandlingUnBlocked', 'queryGcIssueHandlingUnBlocked', 3, 1, 3, 1, NULL, '2022-05-05 08:59:00', NULL, '2022-05-06 08:52:46', 0, NULL, 'queryIssueHandlingList', 0, NULL, NULL, NULL);
INSERT INTO `idm_resource` (`domain_code`, `app_code`, `resource_parent`, `resource_code`, `resource_name`, `resource_type`, `resource_class`, `access_type`, `status`, `create_user`, `create_time`, `update_user`, `update_time`, `logic_delete`, `position_code`, `resource_url`, `log_switch`, `log_business_code`, `log_desc`, `log_business`) VALUES ( 'SYSTEM_DEFAULT', 'GV', 'm_gc_issue_unblock', 'getGcIssueHandlingUnBlocked', 'getGcIssueHandlingUnBlocked', 3, 1, 3, 1, NULL, '2022-05-05 08:59:01', NULL, '2022-05-06 08:52:46', 0, NULL, 'queryIssueHandling', 0, NULL, NULL, NULL);
INSERT INTO `idm_resource` (`domain_code`, `app_code`, `resource_parent`, `resource_code`, `resource_name`, `resource_type`, `resource_class`, `access_type`, `status`, `create_user`, `create_time`, `update_user`, `update_time`, `logic_delete`, `position_code`, `resource_url`, `log_switch`, `log_business_code`, `log_desc`, `log_business`) VALUES ( 'SYSTEM_DEFAULT', 'GV', 'm_gc_issue_unblock', 'updateGcIssueHandlingUnBlocked', 'Create/Edit Gc Issue UnBlocked', 4, 1, 3, 1, NULL, '2022-05-05 08:59:01', NULL, '2022-05-09 10:44:30', 0, NULL, 'updateIssueHandling', 0, NULL, NULL, NULL);
INSERT INTO `idm_resource` (`domain_code`, `app_code`, `resource_parent`, `resource_code`, `resource_name`, `resource_type`, `resource_class`, `access_type`, `status`, `create_user`, `create_time`, `update_user`, `update_time`, `logic_delete`, `position_code`, `resource_url`, `log_switch`, `log_business_code`, `log_desc`, `log_business`) VALUES ( 'SYSTEM_DEFAULT', 'GV', 'm_gc_issue_unblock', 'submitGcIssueHandlingUnBlocked', 'Submit Gc Issue UnBlocked', 4, 1, 3, 1, NULL, '2022-05-05 08:59:01', NULL, '2022-05-09 10:44:30', 0, NULL, 'updateIssueHandling', 0, NULL, NULL, NULL);
INSERT INTO `idm_resource` (`domain_code`, `app_code`, `resource_parent`, `resource_code`, `resource_name`, `resource_type`, `resource_class`, `access_type`, `status`, `create_user`, `create_time`, `update_user`, `update_time`, `logic_delete`, `position_code`, `resource_url`, `log_switch`, `log_business_code`, `log_desc`, `log_business`) VALUES ( 'SYSTEM_DEFAULT', 'GV', 'm_gc_issue_unblock', 'approveGcIssueHandlingUnBlocked', 'Approve Gc Issue UnBlocked', 4, 1, 3, 1, NULL, '2022-05-05 08:59:01', NULL, '2022-05-09 10:44:57', 0, NULL, 'approveIssueHandling', 0, NULL, NULL, NULL);
INSERT INTO `idm_resource` (`domain_code`, `app_code`, `resource_parent`, `resource_code`, `resource_name`, `resource_type`, `resource_class`, `access_type`, `status`, `create_user`, `create_time`, `update_user`, `update_time`, `logic_delete`, `position_code`, `resource_url`, `log_switch`, `log_business_code`, `log_desc`, `log_business`) VALUES ( 'SYSTEM_DEFAULT', 'GV', 'm_gc_issue_unblock', 'cancelGcIssueHandlingUnBlocked', 'Cancel Gc Issue UnBlocked', 4, 1, 3, 1, NULL, '2022-05-05 08:59:01', NULL, '2022-05-09 10:45:07', 0, NULL, 'cancelIssueHandling', 0, NULL, NULL, NULL);

INSERT INTO `idm_resource` (`domain_code`, `app_code`, `resource_parent`, `resource_code`, `resource_name`, `resource_type`, `resource_class`, `access_type`, `status`, `create_user`, `create_time`, `update_user`, `update_time`, `logic_delete`, `position_code`, `resource_url`, `log_switch`, `log_business_code`, `log_desc`, `log_business`) VALUES ( 'SYSTEM_DEFAULT', 'GV', 'm_gc_issue_extend_expiry', 'queryGcIssueHandlingExtendExpiry', 'queryGcIssueHandlingExtendExpiry', 3, 1, 3, 1, NULL, '2022-05-05 08:59:00', NULL, '2022-05-06 08:52:46', 0, NULL, 'queryIssueHandlingList', 0, NULL, NULL, NULL);
INSERT INTO `idm_resource` (`domain_code`, `app_code`, `resource_parent`, `resource_code`, `resource_name`, `resource_type`, `resource_class`, `access_type`, `status`, `create_user`, `create_time`, `update_user`, `update_time`, `logic_delete`, `position_code`, `resource_url`, `log_switch`, `log_business_code`, `log_desc`, `log_business`) VALUES ( 'SYSTEM_DEFAULT', 'GV', 'm_gc_issue_extend_expiry', 'getGcIssueHandlingExtendExpiry', 'getGcIssueHandlingExtendExpiry', 3, 1, 3, 1, NULL, '2022-05-05 08:59:01', NULL, '2022-05-06 08:52:46', 0, NULL, 'queryIssueHandling', 0, NULL, NULL, NULL);
INSERT INTO `idm_resource` (`domain_code`, `app_code`, `resource_parent`, `resource_code`, `resource_name`, `resource_type`, `resource_class`, `access_type`, `status`, `create_user`, `create_time`, `update_user`, `update_time`, `logic_delete`, `position_code`, `resource_url`, `log_switch`, `log_business_code`, `log_desc`, `log_business`) VALUES ( 'SYSTEM_DEFAULT', 'GV', 'm_gc_issue_extend_expiry', 'updateGcIssueHandlingExtendExpiry', 'Create/Edit Gc Issue Extend Expiry', 4, 1, 3, 1, NULL, '2022-05-05 08:59:01', NULL, '2022-05-09 10:44:30', 0, NULL, 'updateIssueHandling', 0, NULL, NULL, NULL);
INSERT INTO `idm_resource` (`domain_code`, `app_code`, `resource_parent`, `resource_code`, `resource_name`, `resource_type`, `resource_class`, `access_type`, `status`, `create_user`, `create_time`, `update_user`, `update_time`, `logic_delete`, `position_code`, `resource_url`, `log_switch`, `log_business_code`, `log_desc`, `log_business`) VALUES ( 'SYSTEM_DEFAULT', 'GV', 'm_gc_issue_extend_expiry', 'submitGcIssueHandlingExtendExpiry', 'Submit Gc Issue Extend Expiry', 4, 1, 3, 1, NULL, '2022-05-05 08:59:01', NULL, '2022-05-09 10:44:30', 0, NULL, 'updateIssueHandling', 0, NULL, NULL, NULL);
INSERT INTO `idm_resource` (`domain_code`, `app_code`, `resource_parent`, `resource_code`, `resource_name`, `resource_type`, `resource_class`, `access_type`, `status`, `create_user`, `create_time`, `update_user`, `update_time`, `logic_delete`, `position_code`, `resource_url`, `log_switch`, `log_business_code`, `log_desc`, `log_business`) VALUES ( 'SYSTEM_DEFAULT', 'GV', 'm_gc_issue_extend_expiry', 'approveGcIssueHandlingExtendExpiry', 'Approve Gc Issue Extend Expiry', 4, 1, 3, 1, NULL, '2022-05-05 08:59:01', NULL, '2022-05-09 10:44:57', 0, NULL, 'approveIssueHandling', 0, NULL, NULL, NULL);
INSERT INTO `idm_resource` (`domain_code`, `app_code`, `resource_parent`, `resource_code`, `resource_name`, `resource_type`, `resource_class`, `access_type`, `status`, `create_user`, `create_time`, `update_user`, `update_time`, `logic_delete`, `position_code`, `resource_url`, `log_switch`, `log_business_code`, `log_desc`, `log_business`) VALUES ( 'SYSTEM_DEFAULT', 'GV', 'm_gc_issue_extend_expiry', 'cancelGcIssueHandlingExtendExpiry', 'Cancel Gc Issue Extend Expiry', 4, 1, 3, 1, NULL, '2022-05-05 08:59:01', NULL, '2022-05-09 10:45:07', 0, NULL, 'cancelIssueHandling', 0, NULL, NULL, NULL);

INSERT INTO `idm_resource` (`domain_code`, `app_code`, `resource_parent`, `resource_code`, `resource_name`, `resource_type`, `resource_class`, `access_type`, `status`, `create_user`, `create_time`, `update_user`, `update_time`, `logic_delete`, `position_code`, `resource_url`, `log_switch`, `log_business_code`, `log_desc`, `log_business`) VALUES ( 'SYSTEM_DEFAULT', 'GV', 'm_gc_issue_regenerate_code', 'queryGcIssueHandlingRegenerate', 'queryGcIssueHandlingRegenerate', 3, 1, 3, 1, NULL, '2022-05-05 08:59:00', NULL, '2022-05-06 08:52:46', 0, NULL, 'queryIssueHandlingList', 0, NULL, NULL, NULL);
INSERT INTO `idm_resource` (`domain_code`, `app_code`, `resource_parent`, `resource_code`, `resource_name`, `resource_type`, `resource_class`, `access_type`, `status`, `create_user`, `create_time`, `update_user`, `update_time`, `logic_delete`, `position_code`, `resource_url`, `log_switch`, `log_business_code`, `log_desc`, `log_business`) VALUES ( 'SYSTEM_DEFAULT', 'GV', 'm_gc_issue_regenerate_code', 'getGcIssueHandlingRegenerate', 'getGcIssueHandlingRegenerate', 3, 1, 3, 1, NULL, '2022-05-05 08:59:01', NULL, '2022-05-06 08:52:46', 0, NULL, 'queryIssueHandling', 0, NULL, NULL, NULL);
INSERT INTO `idm_resource` (`domain_code`, `app_code`, `resource_parent`, `resource_code`, `resource_name`, `resource_type`, `resource_class`, `access_type`, `status`, `create_user`, `create_time`, `update_user`, `update_time`, `logic_delete`, `position_code`, `resource_url`, `log_switch`, `log_business_code`, `log_desc`, `log_business`) VALUES ( 'SYSTEM_DEFAULT', 'GV', 'm_gc_issue_regenerate_code', 'updateGcIssueHandlingRegenerate', 'Create/Edit Gc Issue Regenerate Code', 4, 1, 3, 1, NULL, '2022-05-05 08:59:01', NULL, '2022-05-09 10:44:30', 0, NULL, 'updateIssueHandling', 0, NULL, NULL, NULL);
INSERT INTO `idm_resource` (`domain_code`, `app_code`, `resource_parent`, `resource_code`, `resource_name`, `resource_type`, `resource_class`, `access_type`, `status`, `create_user`, `create_time`, `update_user`, `update_time`, `logic_delete`, `position_code`, `resource_url`, `log_switch`, `log_business_code`, `log_desc`, `log_business`) VALUES ( 'SYSTEM_DEFAULT', 'GV', 'm_gc_issue_regenerate_code', 'submitGcIssueHandlingRegenerate', 'Submit Gc Issue Regenerate Code', 4, 1, 3, 1, NULL, '2022-05-05 08:59:01', NULL, '2022-05-09 10:44:30', 0, NULL, 'updateIssueHandling', 0, NULL, NULL, NULL);
INSERT INTO `idm_resource` (`domain_code`, `app_code`, `resource_parent`, `resource_code`, `resource_name`, `resource_type`, `resource_class`, `access_type`, `status`, `create_user`, `create_time`, `update_user`, `update_time`, `logic_delete`, `position_code`, `resource_url`, `log_switch`, `log_business_code`, `log_desc`, `log_business`) VALUES ( 'SYSTEM_DEFAULT', 'GV', 'm_gc_issue_regenerate_code', 'approveGcIssueHandlingRegenerate', 'Approve Gc Issue Regenerate Code', 4, 1, 3, 1, NULL, '2022-05-05 08:59:01', NULL, '2022-05-09 10:44:57', 0, NULL, 'approveIssueHandling', 0, NULL, NULL, NULL);
INSERT INTO `idm_resource` (`domain_code`, `app_code`, `resource_parent`, `resource_code`, `resource_name`, `resource_type`, `resource_class`, `access_type`, `status`, `create_user`, `create_time`, `update_user`, `update_time`, `logic_delete`, `position_code`, `resource_url`, `log_switch`, `log_business_code`, `log_desc`, `log_business`) VALUES ( 'SYSTEM_DEFAULT', 'GV', 'm_gc_issue_regenerate_code', 'cancelGcIssueHandlingRegenerate', 'Cancel Gc Issue Regenerate Code', 4, 1, 3, 1, NULL, '2022-05-05 08:59:01', NULL, '2022-05-09 10:45:07', 0, NULL, 'cancelIssueHandling', 0, NULL, NULL, NULL);

INSERT INTO `idm_resource` (`domain_code`, `app_code`, `resource_parent`, `resource_code`, `resource_name`, `resource_type`, `resource_class`, `access_type`, `status`, `create_user`, `create_time`, `update_user`, `update_time`, `logic_delete`, `position_code`, `resource_url`, `log_switch`, `log_business_code`, `log_desc`, `log_business`) VALUES ( 'SYSTEM_DEFAULT', 'GV', 'm_gc_issue_redeem', 'queryGcIssueHandlingRedeem', 'queryGcIssueHandlingRedeem', 3, 1, 3, 1, NULL, '2022-05-05 08:59:00', NULL, '2022-05-06 08:52:46', 0, NULL, 'queryIssueHandlingList', 0, NULL, NULL, NULL);
INSERT INTO `idm_resource` (`domain_code`, `app_code`, `resource_parent`, `resource_code`, `resource_name`, `resource_type`, `resource_class`, `access_type`, `status`, `create_user`, `create_time`, `update_user`, `update_time`, `logic_delete`, `position_code`, `resource_url`, `log_switch`, `log_business_code`, `log_desc`, `log_business`) VALUES ( 'SYSTEM_DEFAULT', 'GV', 'm_gc_issue_redeem', 'getGcIssueHandlingRedeem', 'getGcIssueHandlingRedeem', 3, 1, 3, 1, NULL, '2022-05-05 08:59:01', NULL, '2022-05-06 08:52:46', 0, NULL, 'queryIssueHandling', 0, NULL, NULL, NULL);
INSERT INTO `idm_resource` (`domain_code`, `app_code`, `resource_parent`, `resource_code`, `resource_name`, `resource_type`, `resource_class`, `access_type`, `status`, `create_user`, `create_time`, `update_user`, `update_time`, `logic_delete`, `position_code`, `resource_url`, `log_switch`, `log_business_code`, `log_desc`, `log_business`) VALUES ( 'SYSTEM_DEFAULT', 'GV', 'm_gc_issue_redeem', 'updateGcIssueHandlingRedeem', 'Create/Edit Gc Issue Redemption', 4, 1, 3, 1, NULL, '2022-05-05 08:59:01', NULL, '2022-05-09 10:44:30', 0, NULL, 'updateIssueHandling', 0, NULL, NULL, NULL);
INSERT INTO `idm_resource` (`domain_code`, `app_code`, `resource_parent`, `resource_code`, `resource_name`, `resource_type`, `resource_class`, `access_type`, `status`, `create_user`, `create_time`, `update_user`, `update_time`, `logic_delete`, `position_code`, `resource_url`, `log_switch`, `log_business_code`, `log_desc`, `log_business`) VALUES ( 'SYSTEM_DEFAULT', 'GV', 'm_gc_issue_redeem', 'submitGcIssueHandlingRedeem', 'Submit Gc Issue Redemption', 4, 1, 3, 1, NULL, '2022-05-05 08:59:01', NULL, '2022-05-09 10:44:30', 0, NULL, 'updateIssueHandling', 0, NULL, NULL, NULL);
INSERT INTO `idm_resource` (`domain_code`, `app_code`, `resource_parent`, `resource_code`, `resource_name`, `resource_type`, `resource_class`, `access_type`, `status`, `create_user`, `create_time`, `update_user`, `update_time`, `logic_delete`, `position_code`, `resource_url`, `log_switch`, `log_business_code`, `log_desc`, `log_business`) VALUES ( 'SYSTEM_DEFAULT', 'GV', 'm_gc_issue_redeem', 'approveGcIssueHandlingRedeem', 'Approve Gc Issue Redemption', 4, 1, 3, 1, NULL, '2022-05-05 08:59:01', NULL, '2022-05-09 10:44:57', 0, NULL, 'approveIssueHandling', 0, NULL, NULL, NULL);
INSERT INTO `idm_resource` (`domain_code`, `app_code`, `resource_parent`, `resource_code`, `resource_name`, `resource_type`, `resource_class`, `access_type`, `status`, `create_user`, `create_time`, `update_user`, `update_time`, `logic_delete`, `position_code`, `resource_url`, `log_switch`, `log_business_code`, `log_desc`, `log_business`) VALUES ( 'SYSTEM_DEFAULT', 'GV', 'm_gc_issue_redeem', 'cancelGcIssueHandlingRedeem', 'Cancel Gc Issue Redemption', 4, 1, 3, 1, NULL, '2022-05-05 08:59:01', NULL, '2022-05-09 10:45:07', 0, NULL, 'cancelIssueHandling', 0, NULL, NULL, NULL);

INSERT INTO `idm_resource` (`domain_code`, `app_code`, `resource_parent`, `resource_code`, `resource_name`, `resource_type`, `resource_class`, `access_type`, `status`, `create_user`, `create_time`, `update_user`, `update_time`, `logic_delete`, `position_code`, `resource_url`, `log_switch`, `log_business_code`, `log_desc`, `log_business`) VALUES ( 'SYSTEM_DEFAULT', 'GV', 'm_gc_issue_cancel_redeem', 'queryGcIssueHandlingCancelRedeem', 'queryGcIssueHandlingCancelRedeem', 3, 1, 3, 1, NULL, '2022-05-05 08:59:00', NULL, '2022-05-06 08:52:46', 0, NULL, 'queryIssueHandlingList', 0, NULL, NULL, NULL);
INSERT INTO `idm_resource` (`domain_code`, `app_code`, `resource_parent`, `resource_code`, `resource_name`, `resource_type`, `resource_class`, `access_type`, `status`, `create_user`, `create_time`, `update_user`, `update_time`, `logic_delete`, `position_code`, `resource_url`, `log_switch`, `log_business_code`, `log_desc`, `log_business`) VALUES ( 'SYSTEM_DEFAULT', 'GV', 'm_gc_issue_cancel_redeem', 'getGcIssueHandlingCancelRedeem', 'getGcIssueHandlingCancelRedeem', 3, 1, 3, 1, NULL, '2022-05-05 08:59:01', NULL, '2022-05-06 08:52:46', 0, NULL, 'queryIssueHandling', 0, NULL, NULL, NULL);
INSERT INTO `idm_resource` (`domain_code`, `app_code`, `resource_parent`, `resource_code`, `resource_name`, `resource_type`, `resource_class`, `access_type`, `status`, `create_user`, `create_time`, `update_user`, `update_time`, `logic_delete`, `position_code`, `resource_url`, `log_switch`, `log_business_code`, `log_desc`, `log_business`) VALUES ( 'SYSTEM_DEFAULT', 'GV', 'm_gc_issue_cancel_redeem', 'updateGcIssueHandlingCancelRedeem', 'Create/Edit Gc Issue Cancel Redemption', 4, 1, 3, 1, NULL, '2022-05-05 08:59:01', NULL, '2022-05-09 10:44:30', 0, NULL, 'updateIssueHandling', 0, NULL, NULL, NULL);
INSERT INTO `idm_resource` (`domain_code`, `app_code`, `resource_parent`, `resource_code`, `resource_name`, `resource_type`, `resource_class`, `access_type`, `status`, `create_user`, `create_time`, `update_user`, `update_time`, `logic_delete`, `position_code`, `resource_url`, `log_switch`, `log_business_code`, `log_desc`, `log_business`) VALUES ( 'SYSTEM_DEFAULT', 'GV', 'm_gc_issue_cancel_redeem', 'submitGcIssueHandlingCancelRedeem', 'Submit Gc Issue Cancel Redemption', 4, 1, 3, 1, NULL, '2022-05-05 08:59:01', NULL, '2022-05-09 10:44:30', 0, NULL, 'updateIssueHandling', 0, NULL, NULL, NULL);
INSERT INTO `idm_resource` (`domain_code`, `app_code`, `resource_parent`, `resource_code`, `resource_name`, `resource_type`, `resource_class`, `access_type`, `status`, `create_user`, `create_time`, `update_user`, `update_time`, `logic_delete`, `position_code`, `resource_url`, `log_switch`, `log_business_code`, `log_desc`, `log_business`) VALUES ( 'SYSTEM_DEFAULT', 'GV', 'm_gc_issue_cancel_redeem', 'approveGcIssueHandlingCancelRedeem', 'Approve Gc Issue Cancel Redemption', 4, 1, 3, 1, NULL, '2022-05-05 08:59:01', NULL, '2022-05-09 10:44:57', 0, NULL, 'approveIssueHandling', 0, NULL, NULL, NULL);
INSERT INTO `idm_resource` (`domain_code`, `app_code`, `resource_parent`, `resource_code`, `resource_name`, `resource_type`, `resource_class`, `access_type`, `status`, `create_user`, `create_time`, `update_user`, `update_time`, `logic_delete`, `position_code`, `resource_url`, `log_switch`, `log_business_code`, `log_desc`, `log_business`) VALUES ( 'SYSTEM_DEFAULT', 'GV', 'm_gc_issue_cancel_redeem', 'cancelGcIssueHandlingCancelRedeem', 'Cancel Gc Issue Cancel Redemption', 4, 1, 3, 1, NULL, '2022-05-05 08:59:01', NULL, '2022-05-09 10:45:07', 0, NULL, 'cancelIssueHandling', 0, NULL, NULL, NULL);

INSERT INTO `idm_resource` (`domain_code`, `app_code`, `resource_parent`, `resource_code`, `resource_name`, `resource_type`, `resource_class`, `access_type`, `status`, `create_user`, `create_time`, `update_user`, `update_time`, `logic_delete`, `position_code`, `resource_url`, `log_switch`, `log_business_code`, `log_desc`, `log_business`) VALUES ( 'SYSTEM_DEFAULT', 'GV', 'm_gc_issue_cancel_sales', 'queryGcIssueHandlingCancelSales', 'queryGcIssueHandlingCancelSales', 3, 1, 3, 1, NULL, '2022-05-05 08:59:00', NULL, '2022-05-06 08:52:46', 0, NULL, 'queryIssueHandlingList', 0, NULL, NULL, NULL);
INSERT INTO `idm_resource` (`domain_code`, `app_code`, `resource_parent`, `resource_code`, `resource_name`, `resource_type`, `resource_class`, `access_type`, `status`, `create_user`, `create_time`, `update_user`, `update_time`, `logic_delete`, `position_code`, `resource_url`, `log_switch`, `log_business_code`, `log_desc`, `log_business`) VALUES ( 'SYSTEM_DEFAULT', 'GV', 'm_gc_issue_cancel_sales', 'getGcIssueHandlingCancelSales', 'getGcIssueHandlingCancelSales', 3, 1, 3, 1, NULL, '2022-05-05 08:59:01', NULL, '2022-05-06 08:52:46', 0, NULL, 'queryIssueHandling', 0, NULL, NULL, NULL);
INSERT INTO `idm_resource` (`domain_code`, `app_code`, `resource_parent`, `resource_code`, `resource_name`, `resource_type`, `resource_class`, `access_type`, `status`, `create_user`, `create_time`, `update_user`, `update_time`, `logic_delete`, `position_code`, `resource_url`, `log_switch`, `log_business_code`, `log_desc`, `log_business`) VALUES ( 'SYSTEM_DEFAULT', 'GV', 'm_gc_issue_cancel_sales', 'updateGcIssueHandlingCancelSales', 'Create/Edit Gc Issue Cancel Sales', 4, 1, 3, 1, NULL, '2022-05-05 08:59:01', NULL, '2022-05-09 10:44:30', 0, NULL, 'updateIssueHandling', 0, NULL, NULL, NULL);
INSERT INTO `idm_resource` (`domain_code`, `app_code`, `resource_parent`, `resource_code`, `resource_name`, `resource_type`, `resource_class`, `access_type`, `status`, `create_user`, `create_time`, `update_user`, `update_time`, `logic_delete`, `position_code`, `resource_url`, `log_switch`, `log_business_code`, `log_desc`, `log_business`) VALUES ( 'SYSTEM_DEFAULT', 'GV', 'm_gc_issue_cancel_sales', 'submitGcIssueHandlingCancelSales', 'Submit Gc Issue Cancel Sales', 4, 1, 3, 1, NULL, '2022-05-05 08:59:01', NULL, '2022-05-09 10:44:30', 0, NULL, 'updateIssueHandling', 0, NULL, NULL, NULL);
INSERT INTO `idm_resource` (`domain_code`, `app_code`, `resource_parent`, `resource_code`, `resource_name`, `resource_type`, `resource_class`, `access_type`, `status`, `create_user`, `create_time`, `update_user`, `update_time`, `logic_delete`, `position_code`, `resource_url`, `log_switch`, `log_business_code`, `log_desc`, `log_business`) VALUES ( 'SYSTEM_DEFAULT', 'GV', 'm_gc_issue_cancel_sales', 'approveGcIssueHandlingCancelSales', 'Approve Gc Issue Cancel Sales', 4, 1, 3, 1, NULL, '2022-05-05 08:59:01', NULL, '2022-05-09 10:44:57', 0, NULL, 'approveIssueHandling', 0, NULL, NULL, NULL);
INSERT INTO `idm_resource` (`domain_code`, `app_code`, `resource_parent`, `resource_code`, `resource_name`, `resource_type`, `resource_class`, `access_type`, `status`, `create_user`, `create_time`, `update_user`, `update_time`, `logic_delete`, `position_code`, `resource_url`, `log_switch`, `log_business_code`, `log_desc`, `log_business`) VALUES ( 'SYSTEM_DEFAULT', 'GV', 'm_gc_issue_cancel_sales', 'cancelGcIssueHandlingCancelSales', 'Cancel Gc Issue Cancel Sales', 4, 1, 3, 1, NULL, '2022-05-05 08:59:01', NULL, '2022-05-09 10:45:07', 0, NULL, 'cancelIssueHandling', 0, NULL, NULL, NULL);


-- 页面
-- Block/Temporary Deactivated
INSERT INTO `idm_page` ( `domain_code`, `app_code`, `menu_code`, `page_code`, `resource_code`, `page_name`, `page_desc`, `page_url`, `status`, `create_user`, `create_time`, `update_user`, `update_time`, `logic_delete`) VALUES ( 'SYSTEM_DEFAULT', 'GV', 'M000701', '1130', 'RS1130', 'GC/CancelSales/bulk_deactivate', NULL, '/gc/order/issue/handling/bulk_deactivate', 1, NULL, '2022-05-12 16:48:04', NULL, '2022-05-12 16:48:04', 0);
INSERT INTO `idm_page` ( `domain_code`, `app_code`, `menu_code`, `page_code`, `resource_code`, `page_name`, `page_desc`, `page_url`, `status`, `create_user`, `create_time`, `update_user`, `update_time`, `logic_delete`) VALUES ( 'SYSTEM_DEFAULT', 'GV', 'M000701', '1131', 'RS1131', 'GC/CancelSales/bulk_deactivate/create', NULL, '/gc/order/issue/handling/bulk_deactivate/create/:code?', 1, NULL, '2022-05-12 16:48:04', NULL, '2022-05-12 16:48:04', 0);
INSERT INTO `idm_page` ( `domain_code`, `app_code`, `menu_code`, `page_code`, `resource_code`, `page_name`, `page_desc`, `page_url`, `status`, `create_user`, `create_time`, `update_user`, `update_time`, `logic_delete`) VALUES ( 'SYSTEM_DEFAULT', 'GV', 'M000701', '1132', 'RS1132', 'GC/CancelSales/bulk_deactivate/edit', NULL, '/gc/order/issue/handling/bulk_deactivate/edit/:code?', 1, NULL, '2022-05-12 16:48:04', NULL, '2022-05-12 16:48:04', 0);
INSERT INTO `idm_page` ( `domain_code`, `app_code`, `menu_code`, `page_code`, `resource_code`, `page_name`, `page_desc`, `page_url`, `status`, `create_user`, `create_time`, `update_user`, `update_time`, `logic_delete`) VALUES ( 'SYSTEM_DEFAULT', 'GV', 'M000701', '1133', 'RS1133', 'GC/CancelSales/bulk_deactivate/view', NULL, '/gc/order/issue/handling/bulk_deactivate/view/:code?', 1, NULL, '2022-05-12 16:48:04', NULL, '2022-05-12 16:48:04', 0);
INSERT INTO `idm_page` ( `domain_code`, `app_code`, `menu_code`, `page_code`, `resource_code`, `page_name`, `page_desc`, `page_url`, `status`, `create_user`, `create_time`, `update_user`, `update_time`, `logic_delete`) VALUES ( 'SYSTEM_DEFAULT', 'GV', 'M000701', '1147', 'RS1147', 'GC/CancelSales/bulk_deactivate/submit', NULL, '/gc/order/issue/handling/bulk_deactivate/submit/:code?', 1, NULL, '2022-06-01 14:59:17', NULL, '2022-06-01 14:59:17', 0);
INSERT INTO `idm_page` ( `domain_code`, `app_code`, `menu_code`, `page_code`, `resource_code`, `page_name`, `page_desc`, `page_url`, `status`, `create_user`, `create_time`, `update_user`, `update_time`, `logic_delete`) VALUES ( 'SYSTEM_DEFAULT', 'GV', 'M000701', '1157', 'RS1157', 'GC/CancelSales/bulk_deactivate/approve', NULL, '/gc/order/issue/handling/bulk_deactivate/approve/:code?', 1, NULL, '2022-07-11 10:20:09', NULL, '2022-07-11 10:20:09', 0);
-- Unblock Reactivated
INSERT INTO `idm_page` ( `domain_code`, `app_code`, `menu_code`, `page_code`, `resource_code`, `page_name`, `page_desc`, `page_url`, `status`, `create_user`, `create_time`, `update_user`, `update_time`, `logic_delete`) VALUES ( 'SYSTEM_DEFAULT', 'GV', 'M000702', '1134', 'RS1134', 'GC/CancelSales/bulk_reactivate', NULL, '/gc/order/issue/handling/bulk_reactivate', 1, NULL, '2022-05-12 16:48:04', NULL, '2022-05-12 16:48:04', 0);
INSERT INTO `idm_page` ( `domain_code`, `app_code`, `menu_code`, `page_code`, `resource_code`, `page_name`, `page_desc`, `page_url`, `status`, `create_user`, `create_time`, `update_user`, `update_time`, `logic_delete`) VALUES ( 'SYSTEM_DEFAULT', 'GV', 'M000702', '1135', 'RS1135', 'GC/CancelSales/bulk_reactivate/create', NULL, '/gc/order/issue/handling/bulk_reactivate/create/:code?', 1, NULL, '2022-05-12 16:48:05', NULL, '2022-05-12 16:48:05', 0);
INSERT INTO `idm_page` ( `domain_code`, `app_code`, `menu_code`, `page_code`, `resource_code`, `page_name`, `page_desc`, `page_url`, `status`, `create_user`, `create_time`, `update_user`, `update_time`, `logic_delete`) VALUES ( 'SYSTEM_DEFAULT', 'GV', 'M000702', '1136', 'RS1136', 'GC/CancelSales/bulk_reactivate/edit', NULL, '/gc/order/issue/handling/bulk_reactivate/edit/:code?', 1, NULL, '2022-05-12 16:48:05', NULL, '2022-05-12 16:48:05', 0);
INSERT INTO `idm_page` ( `domain_code`, `app_code`, `menu_code`, `page_code`, `resource_code`, `page_name`, `page_desc`, `page_url`, `status`, `create_user`, `create_time`, `update_user`, `update_time`, `logic_delete`) VALUES ( 'SYSTEM_DEFAULT', 'GV', 'M000702', '1137', 'RS1137', 'GC/CancelSales/bulk_reactivate/view', NULL, '/gc/order/issue/handling/bulk_reactivate/view/:code?', 1, NULL, '2022-05-12 16:48:05', NULL, '2022-05-12 16:48:05', 0);
INSERT INTO `idm_page` ( `domain_code`, `app_code`, `menu_code`, `page_code`, `resource_code`, `page_name`, `page_desc`, `page_url`, `status`, `create_user`, `create_time`, `update_user`, `update_time`, `logic_delete`) VALUES ( 'SYSTEM_DEFAULT', 'GV', 'M000702', '1148', 'RS1148', 'GC/CancelSales/bulk_reactivate/submit', NULL, '/gc/order/issue/handling/bulk_reactivate/submit/:code?', 1, NULL, '2022-06-01 14:59:17', NULL, '2022-06-01 14:59:17', 0);
INSERT INTO `idm_page` ( `domain_code`, `app_code`, `menu_code`, `page_code`, `resource_code`, `page_name`, `page_desc`, `page_url`, `status`, `create_user`, `create_time`, `update_user`, `update_time`, `logic_delete`) VALUES ( 'SYSTEM_DEFAULT', 'GV', 'M000702', '1158', 'RS1158', 'GC/CancelSales/bulk_reactivate/approve', NULL, '/gc/order/issue/handling/bulk_reactivate/approve/:code?', 1, NULL, '2022-07-11 10:20:09', NULL, '2022-07-11 10:20:09', 0);
-- Extend Expiry Date
INSERT INTO `idm_page` ( `domain_code`, `app_code`, `menu_code`, `page_code`, `resource_code`, `page_name`, `page_desc`, `page_url`, `status`, `create_user`, `create_time`, `update_user`, `update_time`, `logic_delete`) VALUES ( 'SYSTEM_DEFAULT', 'GV', 'M000703', '1134', 'RS1134', 'GC/CancelSales/bulk_change_expiry', NULL, '/gc/order/issue/handling/bulk_change_expiry', 1, NULL, '2022-05-12 16:48:04', NULL, '2022-05-12 16:48:04', 0);
INSERT INTO `idm_page` ( `domain_code`, `app_code`, `menu_code`, `page_code`, `resource_code`, `page_name`, `page_desc`, `page_url`, `status`, `create_user`, `create_time`, `update_user`, `update_time`, `logic_delete`) VALUES ( 'SYSTEM_DEFAULT', 'GV', 'M000703', '1135', 'RS1135', 'GC/CancelSales/bulk_change_expiry/create', NULL, '/gc/order/issue/handling/bulk_change_expiry/create/:code?', 1, NULL, '2022-05-12 16:48:05', NULL, '2022-05-12 16:48:05', 0);
INSERT INTO `idm_page` ( `domain_code`, `app_code`, `menu_code`, `page_code`, `resource_code`, `page_name`, `page_desc`, `page_url`, `status`, `create_user`, `create_time`, `update_user`, `update_time`, `logic_delete`) VALUES ( 'SYSTEM_DEFAULT', 'GV', 'M000703', '1136', 'RS1136', 'GC/CancelSales/bulk_change_expiry/edit', NULL, '/gc/order/issue/handling/bulk_change_expiry/edit/:code?', 1, NULL, '2022-05-12 16:48:05', NULL, '2022-05-12 16:48:05', 0);
INSERT INTO `idm_page` ( `domain_code`, `app_code`, `menu_code`, `page_code`, `resource_code`, `page_name`, `page_desc`, `page_url`, `status`, `create_user`, `create_time`, `update_user`, `update_time`, `logic_delete`) VALUES ( 'SYSTEM_DEFAULT', 'GV', 'M000703', '1137', 'RS1137', 'GC/CancelSales/bulk_change_expiry/view', NULL, '/gc/order/issue/handling/bulk_change_expiry/view/:code?', 1, NULL, '2022-05-12 16:48:05', NULL, '2022-05-12 16:48:05', 0);
INSERT INTO `idm_page` ( `domain_code`, `app_code`, `menu_code`, `page_code`, `resource_code`, `page_name`, `page_desc`, `page_url`, `status`, `create_user`, `create_time`, `update_user`, `update_time`, `logic_delete`) VALUES ( 'SYSTEM_DEFAULT', 'GV', 'M000703', '1148', 'RS1148', 'GC/CancelSales/bulk_change_expiry/submit', NULL, '/gc/order/issue/handling/bulk_change_expiry/submit/:code?', 1, NULL, '2022-06-01 14:59:17', NULL, '2022-06-01 14:59:17', 0);
INSERT INTO `idm_page` ( `domain_code`, `app_code`, `menu_code`, `page_code`, `resource_code`, `page_name`, `page_desc`, `page_url`, `status`, `create_user`, `create_time`, `update_user`, `update_time`, `logic_delete`) VALUES ( 'SYSTEM_DEFAULT', 'GV', 'M000703', '1158', 'RS1158', 'GC/CancelSales/bulk_change_expiry/approve', NULL, '/gc/order/issue/handling/bulk_change_expiry/approve/:code?', 1, NULL, '2022-07-11 10:20:09', NULL, '2022-07-11 10:20:09', 0);
-- Regenerate Activation Code
INSERT INTO `idm_page` ( `domain_code`, `app_code`, `menu_code`, `page_code`, `resource_code`, `page_name`, `page_desc`, `page_url`, `status`, `create_user`, `create_time`, `update_user`, `update_time`, `logic_delete`) VALUES ( 'SYSTEM_DEFAULT', 'GV', 'M000704', '1134', 'RS1134', 'GC/CancelSales/bulk_regenerate_activation_code', NULL, '/gc/order/issue/handling/bulk_regenerate_activation_code', 1, NULL, '2022-05-12 16:48:04', NULL, '2022-05-12 16:48:04', 0);
INSERT INTO `idm_page` ( `domain_code`, `app_code`, `menu_code`, `page_code`, `resource_code`, `page_name`, `page_desc`, `page_url`, `status`, `create_user`, `create_time`, `update_user`, `update_time`, `logic_delete`) VALUES ( 'SYSTEM_DEFAULT', 'GV', 'M000704', '1135', 'RS1135', 'GC/CancelSales/bulk_regenerate_activation_code/create', NULL, '/gc/order/issue/handling/bulk_regenerate_activation_code/create/:code?', 1, NULL, '2022-05-12 16:48:05', NULL, '2022-05-12 16:48:05', 0);
INSERT INTO `idm_page` ( `domain_code`, `app_code`, `menu_code`, `page_code`, `resource_code`, `page_name`, `page_desc`, `page_url`, `status`, `create_user`, `create_time`, `update_user`, `update_time`, `logic_delete`) VALUES ( 'SYSTEM_DEFAULT', 'GV', 'M000704', '1136', 'RS1136', 'GC/CancelSales/bulk_regenerate_activation_code/edit', NULL, '/gc/order/issue/handling/bulk_regenerate_activation_code/edit/:code?', 1, NULL, '2022-05-12 16:48:05', NULL, '2022-05-12 16:48:05', 0);
INSERT INTO `idm_page` ( `domain_code`, `app_code`, `menu_code`, `page_code`, `resource_code`, `page_name`, `page_desc`, `page_url`, `status`, `create_user`, `create_time`, `update_user`, `update_time`, `logic_delete`) VALUES ( 'SYSTEM_DEFAULT', 'GV', 'M000704', '1137', 'RS1137', 'GC/CancelSales/bulk_regenerate_activation_code/view', NULL, '/gc/order/issue/handling/bulk_regenerate_activation_code/view/:code?', 1, NULL, '2022-05-12 16:48:05', NULL, '2022-05-12 16:48:05', 0);
INSERT INTO `idm_page` ( `domain_code`, `app_code`, `menu_code`, `page_code`, `resource_code`, `page_name`, `page_desc`, `page_url`, `status`, `create_user`, `create_time`, `update_user`, `update_time`, `logic_delete`) VALUES ( 'SYSTEM_DEFAULT', 'GV', 'M000704', '1148', 'RS1148', 'GC/CancelSales/bulk_regenerate_activation_code/submit', NULL, '/gc/order/issue/handling/bulk_regenerate_activation_code/submit/:code?', 1, NULL, '2022-06-01 14:59:17', NULL, '2022-06-01 14:59:17', 0);
INSERT INTO `idm_page` ( `domain_code`, `app_code`, `menu_code`, `page_code`, `resource_code`, `page_name`, `page_desc`, `page_url`, `status`, `create_user`, `create_time`, `update_user`, `update_time`, `logic_delete`) VALUES ( 'SYSTEM_DEFAULT', 'GV', 'M000704', '1158', 'RS1158', 'GC/CancelSales/bulk_regenerate_activation_code/approve', NULL, '/gc/order/issue/handling/bulk_regenerate_activation_code/approve/:code?', 1, NULL, '2022-07-11 10:20:09', NULL, '2022-07-11 10:20:09', 0);
-- Bulk Redeem
INSERT INTO `idm_page` ( `domain_code`, `app_code`, `menu_code`, `page_code`, `resource_code`, `page_name`, `page_desc`, `page_url`, `status`, `create_user`, `create_time`, `update_user`, `update_time`, `logic_delete`) VALUES ( 'SYSTEM_DEFAULT', 'GV', 'M000705', '1134', 'RS1134', 'GC/CancelSales/bulk_redeem', NULL, '/gc/order/issue/handling/bulk_redeem', 1, NULL, '2022-05-12 16:48:04', NULL, '2022-05-12 16:48:04', 0);
INSERT INTO `idm_page` ( `domain_code`, `app_code`, `menu_code`, `page_code`, `resource_code`, `page_name`, `page_desc`, `page_url`, `status`, `create_user`, `create_time`, `update_user`, `update_time`, `logic_delete`) VALUES ( 'SYSTEM_DEFAULT', 'GV', 'M000705', '1135', 'RS1135', 'GC/CancelSales/bulk_redeem/create', NULL, '/gc/order/issue/handling/bulk_redeem/create/:code?', 1, NULL, '2022-05-12 16:48:05', NULL, '2022-05-12 16:48:05', 0);
INSERT INTO `idm_page` ( `domain_code`, `app_code`, `menu_code`, `page_code`, `resource_code`, `page_name`, `page_desc`, `page_url`, `status`, `create_user`, `create_time`, `update_user`, `update_time`, `logic_delete`) VALUES ( 'SYSTEM_DEFAULT', 'GV', 'M000705', '1136', 'RS1136', 'GC/CancelSales/bulk_redeem/edit', NULL, '/gc/order/issue/handling/bulk_redeem/edit/:code?', 1, NULL, '2022-05-12 16:48:05', NULL, '2022-05-12 16:48:05', 0);
INSERT INTO `idm_page` ( `domain_code`, `app_code`, `menu_code`, `page_code`, `resource_code`, `page_name`, `page_desc`, `page_url`, `status`, `create_user`, `create_time`, `update_user`, `update_time`, `logic_delete`) VALUES ( 'SYSTEM_DEFAULT', 'GV', 'M000705', '1137', 'RS1137', 'GC/CancelSales/bulk_redeem/view', NULL, '/gc/order/issue/handling/bulk_redeem/view/:code?', 1, NULL, '2022-05-12 16:48:05', NULL, '2022-05-12 16:48:05', 0);
INSERT INTO `idm_page` ( `domain_code`, `app_code`, `menu_code`, `page_code`, `resource_code`, `page_name`, `page_desc`, `page_url`, `status`, `create_user`, `create_time`, `update_user`, `update_time`, `logic_delete`) VALUES ( 'SYSTEM_DEFAULT', 'GV', 'M000705', '1148', 'RS1148', 'GC/CancelSales/bulk_redeem/submit', NULL, '/gc/order/issue/handling/bulk_redeem/submit/:code?', 1, NULL, '2022-06-01 14:59:17', NULL, '2022-06-01 14:59:17', 0);
INSERT INTO `idm_page` ( `domain_code`, `app_code`, `menu_code`, `page_code`, `resource_code`, `page_name`, `page_desc`, `page_url`, `status`, `create_user`, `create_time`, `update_user`, `update_time`, `logic_delete`) VALUES ( 'SYSTEM_DEFAULT', 'GV', 'M000705', '1158', 'RS1158', 'GC/CancelSales/bulk_redeem/approve', NULL, '/gc/order/issue/handling/bulk_redeem/approve/:code?', 1, NULL, '2022-07-11 10:20:09', NULL, '2022-07-11 10:20:09', 0);
-- Bulk Cancel Redeem
INSERT INTO `idm_page` ( `domain_code`, `app_code`, `menu_code`, `page_code`, `resource_code`, `page_name`, `page_desc`, `page_url`, `status`, `create_user`, `create_time`, `update_user`, `update_time`, `logic_delete`) VALUES ( 'SYSTEM_DEFAULT', 'GV', 'M000706', '1134', 'RS1134', 'GC/CancelSales/bulk_cancel_redeem', NULL, '/gc/order/issue/handling/bulk_cancel_redeem', 1, NULL, '2022-05-12 16:48:04', NULL, '2022-05-12 16:48:04', 0);
INSERT INTO `idm_page` ( `domain_code`, `app_code`, `menu_code`, `page_code`, `resource_code`, `page_name`, `page_desc`, `page_url`, `status`, `create_user`, `create_time`, `update_user`, `update_time`, `logic_delete`) VALUES ( 'SYSTEM_DEFAULT', 'GV', 'M000706', '1135', 'RS1135', 'GC/CancelSales/bulk_cancel_redeem/create', NULL, '/gc/order/issue/handling/bulk_cancel_redeem/create/:code?', 1, NULL, '2022-05-12 16:48:05', NULL, '2022-05-12 16:48:05', 0);
INSERT INTO `idm_page` ( `domain_code`, `app_code`, `menu_code`, `page_code`, `resource_code`, `page_name`, `page_desc`, `page_url`, `status`, `create_user`, `create_time`, `update_user`, `update_time`, `logic_delete`) VALUES ( 'SYSTEM_DEFAULT', 'GV', 'M000706', '1136', 'RS1136', 'GC/CancelSales/bulk_cancel_redeem/edit', NULL, '/gc/order/issue/handling/bulk_cancel_redeem/edit/:code?', 1, NULL, '2022-05-12 16:48:05', NULL, '2022-05-12 16:48:05', 0);
INSERT INTO `idm_page` ( `domain_code`, `app_code`, `menu_code`, `page_code`, `resource_code`, `page_name`, `page_desc`, `page_url`, `status`, `create_user`, `create_time`, `update_user`, `update_time`, `logic_delete`) VALUES ( 'SYSTEM_DEFAULT', 'GV', 'M000706', '1137', 'RS1137', 'GC/CancelSales/bulk_cancel_redeem/view', NULL, '/gc/order/issue/handling/bulk_cancel_redeem/view/:code?', 1, NULL, '2022-05-12 16:48:05', NULL, '2022-05-12 16:48:05', 0);
INSERT INTO `idm_page` ( `domain_code`, `app_code`, `menu_code`, `page_code`, `resource_code`, `page_name`, `page_desc`, `page_url`, `status`, `create_user`, `create_time`, `update_user`, `update_time`, `logic_delete`) VALUES ( 'SYSTEM_DEFAULT', 'GV', 'M000706', '1148', 'RS1148', 'GC/CancelSales/bulk_cancel_redeem/submit', NULL, '/gc/order/issue/handling/bulk_cancel_redeem/submit/:code?', 1, NULL, '2022-06-01 14:59:17', NULL, '2022-06-01 14:59:17', 0);
INSERT INTO `idm_page` ( `domain_code`, `app_code`, `menu_code`, `page_code`, `resource_code`, `page_name`, `page_desc`, `page_url`, `status`, `create_user`, `create_time`, `update_user`, `update_time`, `logic_delete`) VALUES ( 'SYSTEM_DEFAULT', 'GV', 'M000706', '1158', 'RS1158', 'GC/CancelSales/bulk_cancel_redeem/approve', NULL, '/gc/order/issue/handling/bulk_cancel_redeem/approve/:code?', 1, NULL, '2022-07-11 10:20:09', NULL, '2022-07-11 10:20:09', 0);
-- Cancel Sales
INSERT INTO `idm_page` ( `domain_code`, `app_code`, `menu_code`, `page_code`, `resource_code`, `page_name`, `page_desc`, `page_url`, `status`, `create_user`, `create_time`, `update_user`, `update_time`, `logic_delete`) VALUES ( 'SYSTEM_DEFAULT', 'GV', 'M000707', '1134', 'RS1134', 'GC/CancelSales/bulk_cancel_sales', NULL, '/gc/order/issue/handling/bulk_cancel_sales', 1, NULL, '2022-05-12 16:48:04', NULL, '2022-05-12 16:48:04', 0);
INSERT INTO `idm_page` ( `domain_code`, `app_code`, `menu_code`, `page_code`, `resource_code`, `page_name`, `page_desc`, `page_url`, `status`, `create_user`, `create_time`, `update_user`, `update_time`, `logic_delete`) VALUES ( 'SYSTEM_DEFAULT', 'GV', 'M000707', '1135', 'RS1135', 'GC/CancelSales/bulk_cancel_sales/create', NULL, '/gc/order/issue/handling/bulk_cancel_sales/create/:code?', 1, NULL, '2022-05-12 16:48:05', NULL, '2022-05-12 16:48:05', 0);
INSERT INTO `idm_page` ( `domain_code`, `app_code`, `menu_code`, `page_code`, `resource_code`, `page_name`, `page_desc`, `page_url`, `status`, `create_user`, `create_time`, `update_user`, `update_time`, `logic_delete`) VALUES ( 'SYSTEM_DEFAULT', 'GV', 'M000707', '1136', 'RS1136', 'GC/CancelSales/bulk_cancel_sales/edit', NULL, '/gc/order/issue/handling/bulk_cancel_sales/edit/:code?', 1, NULL, '2022-05-12 16:48:05', NULL, '2022-05-12 16:48:05', 0);
INSERT INTO `idm_page` ( `domain_code`, `app_code`, `menu_code`, `page_code`, `resource_code`, `page_name`, `page_desc`, `page_url`, `status`, `create_user`, `create_time`, `update_user`, `update_time`, `logic_delete`) VALUES ( 'SYSTEM_DEFAULT', 'GV', 'M000707', '1137', 'RS1137', 'GC/CancelSales/bulk_cancel_sales/view', NULL, '/gc/order/issue/handling/bulk_cancel_sales/view/:code?', 1, NULL, '2022-05-12 16:48:05', NULL, '2022-05-12 16:48:05', 0);
INSERT INTO `idm_page` ( `domain_code`, `app_code`, `menu_code`, `page_code`, `resource_code`, `page_name`, `page_desc`, `page_url`, `status`, `create_user`, `create_time`, `update_user`, `update_time`, `logic_delete`) VALUES ( 'SYSTEM_DEFAULT', 'GV', 'M000707', '1148', 'RS1148', 'GC/CancelSales/bulk_cancel_sales/submit', NULL, '/gc/order/issue/handling/bulk_cancel_sales/submit/:code?', 1, NULL, '2022-06-01 14:59:17', NULL, '2022-06-01 14:59:17', 0);
INSERT INTO `idm_page` ( `domain_code`, `app_code`, `menu_code`, `page_code`, `resource_code`, `page_name`, `page_desc`, `page_url`, `status`, `create_user`, `create_time`, `update_user`, `update_time`, `logic_delete`) VALUES ( 'SYSTEM_DEFAULT', 'GV', 'M000707', '1158', 'RS1158', 'GC/CancelSales/bulk_cancel_sales/approve', NULL, '/gc/order/issue/handling/bulk_cancel_sales/approve/:code?', 1, NULL, '2022-07-11 10:20:09', NULL, '2022-07-11 10:20:09', 0);

-- 角色菜单资源绑定
INSERT INTO `idm_role_resource_mapping` ( `domain_code`, `tenant_code`, `role_code`, `app_code`, `resource_code`, `create_user`, `create_time`, `update_user`, `update_time`, `logic_delete`, `resource_type`) VALUES ( 'SYSTEM_DEFAULT', 'SYSTEM_DEFAULT', 'admin', 'GV', 'm_gc_issueHandling', NULL, '2024-01-25 16:09:51', NULL, '2024-01-25 16:09:51', 0, NULL);
INSERT INTO `idm_role_resource_mapping` ( `domain_code`, `tenant_code`, `role_code`, `app_code`, `resource_code`, `create_user`, `create_time`, `update_user`, `update_time`, `logic_delete`, `resource_type`) VALUES ( 'SYSTEM_DEFAULT', 'SYSTEM_DEFAULT', 'admin', 'GV', 'm_gc_issue_block', NULL, '2024-01-25 16:09:51', NULL, '2024-01-25 16:09:51', 0, NULL);
INSERT INTO `idm_role_resource_mapping` ( `domain_code`, `tenant_code`, `role_code`, `app_code`, `resource_code`, `create_user`, `create_time`, `update_user`, `update_time`, `logic_delete`, `resource_type`) VALUES ( 'SYSTEM_DEFAULT', 'SYSTEM_DEFAULT', 'admin', 'GV', 'm_gc_issue_unblock', NULL, '2024-01-25 16:09:51', NULL, '2024-01-25 16:09:51', 0, NULL);
INSERT INTO `idm_role_resource_mapping` ( `domain_code`, `tenant_code`, `role_code`, `app_code`, `resource_code`, `create_user`, `create_time`, `update_user`, `update_time`, `logic_delete`, `resource_type`) VALUES ( 'SYSTEM_DEFAULT', 'SYSTEM_DEFAULT', 'admin', 'GV', 'm_gc_issue_extend_expiry', NULL, '2024-01-25 16:09:51', NULL, '2024-01-25 16:09:51', 0, NULL);
INSERT INTO `idm_role_resource_mapping` ( `domain_code`, `tenant_code`, `role_code`, `app_code`, `resource_code`, `create_user`, `create_time`, `update_user`, `update_time`, `logic_delete`, `resource_type`) VALUES ( 'SYSTEM_DEFAULT', 'SYSTEM_DEFAULT', 'admin', 'GV', 'm_gc_issue_regenerate_code', NULL, '2024-01-25 16:09:51', NULL, '2024-01-25 16:09:51', 0, NULL);
INSERT INTO `idm_role_resource_mapping` ( `domain_code`, `tenant_code`, `role_code`, `app_code`, `resource_code`, `create_user`, `create_time`, `update_user`, `update_time`, `logic_delete`, `resource_type`) VALUES ( 'SYSTEM_DEFAULT', 'SYSTEM_DEFAULT', 'admin', 'GV', 'm_gc_issue_redeem', NULL, '2024-01-25 16:09:51', NULL, '2024-01-25 16:09:51', 0, NULL);
INSERT INTO `idm_role_resource_mapping` ( `domain_code`, `tenant_code`, `role_code`, `app_code`, `resource_code`, `create_user`, `create_time`, `update_user`, `update_time`, `logic_delete`, `resource_type`) VALUES ( 'SYSTEM_DEFAULT', 'SYSTEM_DEFAULT', 'admin', 'GV', 'm_gc_issue_cancel_redeem', NULL, '2024-01-25 16:09:51', NULL, '2024-01-25 16:09:51', 0, NULL);
INSERT INTO `idm_role_resource_mapping` ( `domain_code`, `tenant_code`, `role_code`, `app_code`, `resource_code`, `create_user`, `create_time`, `update_user`, `update_time`, `logic_delete`, `resource_type`) VALUES ( 'SYSTEM_DEFAULT', 'SYSTEM_DEFAULT', 'admin', 'GV', 'm_gc_issue_cancel_sales', NULL, '2024-01-25 16:09:51', NULL, '2024-01-25 16:09:51', 0, NULL);

-- 角色权限资源绑定
INSERT INTO `idm_role_resource_mapping` ( `domain_code`, `tenant_code`, `role_code`, `app_code`, `resource_code`, `create_user`, `create_time`, `update_user`, `update_time`, `logic_delete`, `resource_type`) VALUES ( 'SYSTEM_DEFAULT', 'SYSTEM_DEFAULT', 'admin', 'GV', 'queryGcIssueHandlingBlocked', NULL, '2024-01-25 16:09:51', NULL, '2024-01-25 16:09:51', 0, NULL);
INSERT INTO `idm_role_resource_mapping` ( `domain_code`, `tenant_code`, `role_code`, `app_code`, `resource_code`, `create_user`, `create_time`, `update_user`, `update_time`, `logic_delete`, `resource_type`) VALUES ( 'SYSTEM_DEFAULT', 'SYSTEM_DEFAULT', 'admin', 'GV', 'getGcIssueHandlingBlocked', NULL, '2024-01-25 16:09:51', NULL, '2024-01-25 16:09:51', 0, NULL);
INSERT INTO `idm_role_resource_mapping` ( `domain_code`, `tenant_code`, `role_code`, `app_code`, `resource_code`, `create_user`, `create_time`, `update_user`, `update_time`, `logic_delete`, `resource_type`) VALUES ( 'SYSTEM_DEFAULT', 'SYSTEM_DEFAULT', 'admin', 'GV', 'updateGcIssueHandlingBlocked', NULL, '2024-01-25 16:09:51', NULL, '2024-01-25 16:09:51', 0, NULL);
INSERT INTO `idm_role_resource_mapping` ( `domain_code`, `tenant_code`, `role_code`, `app_code`, `resource_code`, `create_user`, `create_time`, `update_user`, `update_time`, `logic_delete`, `resource_type`) VALUES ( 'SYSTEM_DEFAULT', 'SYSTEM_DEFAULT', 'admin', 'GV', 'submitGcIssueHandlingBlocked', NULL, '2024-01-25 16:09:51', NULL, '2024-01-25 16:09:51', 0, NULL);
INSERT INTO `idm_role_resource_mapping` ( `domain_code`, `tenant_code`, `role_code`, `app_code`, `resource_code`, `create_user`, `create_time`, `update_user`, `update_time`, `logic_delete`, `resource_type`) VALUES ( 'SYSTEM_DEFAULT', 'SYSTEM_DEFAULT', 'admin', 'GV', 'approveGcIssueHandlingBlocked', NULL, '2024-01-25 16:09:51', NULL, '2024-01-25 16:09:51', 0, NULL);
INSERT INTO `idm_role_resource_mapping` ( `domain_code`, `tenant_code`, `role_code`, `app_code`, `resource_code`, `create_user`, `create_time`, `update_user`, `update_time`, `logic_delete`, `resource_type`) VALUES ( 'SYSTEM_DEFAULT', 'SYSTEM_DEFAULT', 'admin', 'GV', 'cancelGcIssueHandlingBlocked', NULL, '2024-01-25 16:09:51', NULL, '2024-01-25 16:09:51', 0, NULL);

INSERT INTO `idm_role_resource_mapping` ( `domain_code`, `tenant_code`, `role_code`, `app_code`, `resource_code`, `create_user`, `create_time`, `update_user`, `update_time`, `logic_delete`, `resource_type`) VALUES ( 'SYSTEM_DEFAULT', 'SYSTEM_DEFAULT', 'admin', 'GV', 'queryGcIssueHandlingExtendExpiry', NULL, '2024-01-25 16:09:51', NULL, '2024-01-25 16:09:51', 0, NULL);
INSERT INTO `idm_role_resource_mapping` ( `domain_code`, `tenant_code`, `role_code`, `app_code`, `resource_code`, `create_user`, `create_time`, `update_user`, `update_time`, `logic_delete`, `resource_type`) VALUES ( 'SYSTEM_DEFAULT', 'SYSTEM_DEFAULT', 'admin', 'GV', 'getGcIssueHandlingExtendExpiry', NULL, '2024-01-25 16:09:51', NULL, '2024-01-25 16:09:51', 0, NULL);
INSERT INTO `idm_role_resource_mapping` ( `domain_code`, `tenant_code`, `role_code`, `app_code`, `resource_code`, `create_user`, `create_time`, `update_user`, `update_time`, `logic_delete`, `resource_type`) VALUES ( 'SYSTEM_DEFAULT', 'SYSTEM_DEFAULT', 'admin', 'GV', 'updateGcIssueHandlingExtendExpiry', NULL, '2024-01-25 16:09:51', NULL, '2024-01-25 16:09:51', 0, NULL);
INSERT INTO `idm_role_resource_mapping` ( `domain_code`, `tenant_code`, `role_code`, `app_code`, `resource_code`, `create_user`, `create_time`, `update_user`, `update_time`, `logic_delete`, `resource_type`) VALUES ( 'SYSTEM_DEFAULT', 'SYSTEM_DEFAULT', 'admin', 'GV', 'submitGcIssueHandlingExtendExpiry', NULL, '2024-01-25 16:09:51', NULL, '2024-01-25 16:09:51', 0, NULL);
INSERT INTO `idm_role_resource_mapping` ( `domain_code`, `tenant_code`, `role_code`, `app_code`, `resource_code`, `create_user`, `create_time`, `update_user`, `update_time`, `logic_delete`, `resource_type`) VALUES ( 'SYSTEM_DEFAULT', 'SYSTEM_DEFAULT', 'admin', 'GV', 'approveGcIssueHandlingExtendExpiry', NULL, '2024-01-25 16:09:51', NULL, '2024-01-25 16:09:51', 0, NULL);
INSERT INTO `idm_role_resource_mapping` ( `domain_code`, `tenant_code`, `role_code`, `app_code`, `resource_code`, `create_user`, `create_time`, `update_user`, `update_time`, `logic_delete`, `resource_type`) VALUES ( 'SYSTEM_DEFAULT', 'SYSTEM_DEFAULT', 'admin', 'GV', 'cancelGcIssueHandlingExtendExpiry', NULL, '2024-01-25 16:09:51', NULL, '2024-01-25 16:09:51', 0, NULL);

INSERT INTO `idm_role_resource_mapping` ( `domain_code`, `tenant_code`, `role_code`, `app_code`, `resource_code`, `create_user`, `create_time`, `update_user`, `update_time`, `logic_delete`, `resource_type`) VALUES ( 'SYSTEM_DEFAULT', 'SYSTEM_DEFAULT', 'admin', 'GV', 'queryGcIssueHandlingUnBlocked', NULL, '2024-01-25 16:09:51', NULL, '2024-01-25 16:09:51', 0, NULL);
INSERT INTO `idm_role_resource_mapping` ( `domain_code`, `tenant_code`, `role_code`, `app_code`, `resource_code`, `create_user`, `create_time`, `update_user`, `update_time`, `logic_delete`, `resource_type`) VALUES ( 'SYSTEM_DEFAULT', 'SYSTEM_DEFAULT', 'admin', 'GV', 'getGcIssueHandlingUnBlocked', NULL, '2024-01-25 16:09:51', NULL, '2024-01-25 16:09:51', 0, NULL);
INSERT INTO `idm_role_resource_mapping` ( `domain_code`, `tenant_code`, `role_code`, `app_code`, `resource_code`, `create_user`, `create_time`, `update_user`, `update_time`, `logic_delete`, `resource_type`) VALUES ( 'SYSTEM_DEFAULT', 'SYSTEM_DEFAULT', 'admin', 'GV', 'updateGcIssueHandlingUnBlocked', NULL, '2024-01-25 16:09:51', NULL, '2024-01-25 16:09:51', 0, NULL);
INSERT INTO `idm_role_resource_mapping` ( `domain_code`, `tenant_code`, `role_code`, `app_code`, `resource_code`, `create_user`, `create_time`, `update_user`, `update_time`, `logic_delete`, `resource_type`) VALUES ( 'SYSTEM_DEFAULT', 'SYSTEM_DEFAULT', 'admin', 'GV', 'submitGcIssueHandlingUnBlocked', NULL, '2024-01-25 16:09:51', NULL, '2024-01-25 16:09:51', 0, NULL);
INSERT INTO `idm_role_resource_mapping` ( `domain_code`, `tenant_code`, `role_code`, `app_code`, `resource_code`, `create_user`, `create_time`, `update_user`, `update_time`, `logic_delete`, `resource_type`) VALUES ( 'SYSTEM_DEFAULT', 'SYSTEM_DEFAULT', 'admin', 'GV', 'approveGcIssueHandlingUnBlocked', NULL, '2024-01-25 16:09:51', NULL, '2024-01-25 16:09:51', 0, NULL);
INSERT INTO `idm_role_resource_mapping` ( `domain_code`, `tenant_code`, `role_code`, `app_code`, `resource_code`, `create_user`, `create_time`, `update_user`, `update_time`, `logic_delete`, `resource_type`) VALUES ( 'SYSTEM_DEFAULT', 'SYSTEM_DEFAULT', 'admin', 'GV', 'cancelGcIssueHandlingUnBlocked', NULL, '2024-01-25 16:09:51', NULL, '2024-01-25 16:09:51', 0, NULL);

INSERT INTO `idm_role_resource_mapping` ( `domain_code`, `tenant_code`, `role_code`, `app_code`, `resource_code`, `create_user`, `create_time`, `update_user`, `update_time`, `logic_delete`, `resource_type`) VALUES ( 'SYSTEM_DEFAULT', 'SYSTEM_DEFAULT', 'admin', 'GV', 'queryGcIssueHandlingBlocked', NULL, '2024-01-25 16:09:51', NULL, '2024-01-25 16:09:51', 0, NULL);
INSERT INTO `idm_role_resource_mapping` ( `domain_code`, `tenant_code`, `role_code`, `app_code`, `resource_code`, `create_user`, `create_time`, `update_user`, `update_time`, `logic_delete`, `resource_type`) VALUES ( 'SYSTEM_DEFAULT', 'SYSTEM_DEFAULT', 'admin', 'GV', 'getGcIssueHandlingBlocked', NULL, '2024-01-25 16:09:51', NULL, '2024-01-25 16:09:51', 0, NULL);
INSERT INTO `idm_role_resource_mapping` ( `domain_code`, `tenant_code`, `role_code`, `app_code`, `resource_code`, `create_user`, `create_time`, `update_user`, `update_time`, `logic_delete`, `resource_type`) VALUES ( 'SYSTEM_DEFAULT', 'SYSTEM_DEFAULT', 'admin', 'GV', 'updateGcIssueHandlingBlocked', NULL, '2024-01-25 16:09:51', NULL, '2024-01-25 16:09:51', 0, NULL);
INSERT INTO `idm_role_resource_mapping` ( `domain_code`, `tenant_code`, `role_code`, `app_code`, `resource_code`, `create_user`, `create_time`, `update_user`, `update_time`, `logic_delete`, `resource_type`) VALUES ( 'SYSTEM_DEFAULT', 'SYSTEM_DEFAULT', 'admin', 'GV', 'submitGcIssueHandlingBlocked', NULL, '2024-01-25 16:09:51', NULL, '2024-01-25 16:09:51', 0, NULL);
INSERT INTO `idm_role_resource_mapping` ( `domain_code`, `tenant_code`, `role_code`, `app_code`, `resource_code`, `create_user`, `create_time`, `update_user`, `update_time`, `logic_delete`, `resource_type`) VALUES ( 'SYSTEM_DEFAULT', 'SYSTEM_DEFAULT', 'admin', 'GV', 'approveGcIssueHandlingBlocked', NULL, '2024-01-25 16:09:51', NULL, '2024-01-25 16:09:51', 0, NULL);
INSERT INTO `idm_role_resource_mapping` ( `domain_code`, `tenant_code`, `role_code`, `app_code`, `resource_code`, `create_user`, `create_time`, `update_user`, `update_time`, `logic_delete`, `resource_type`) VALUES ( 'SYSTEM_DEFAULT', 'SYSTEM_DEFAULT', 'admin', 'GV', 'cancelGcIssueHandlingBlocked', NULL, '2024-01-25 16:09:51', NULL, '2024-01-25 16:09:51', 0, NULL);

INSERT INTO `idm_role_resource_mapping` ( `domain_code`, `tenant_code`, `role_code`, `app_code`, `resource_code`, `create_user`, `create_time`, `update_user`, `update_time`, `logic_delete`, `resource_type`) VALUES ( 'SYSTEM_DEFAULT', 'SYSTEM_DEFAULT', 'admin', 'GV', 'queryGcIssueHandlingRegenerate', NULL, '2024-01-25 16:09:51', NULL, '2024-01-25 16:09:51', 0, NULL);
INSERT INTO `idm_role_resource_mapping` ( `domain_code`, `tenant_code`, `role_code`, `app_code`, `resource_code`, `create_user`, `create_time`, `update_user`, `update_time`, `logic_delete`, `resource_type`) VALUES ( 'SYSTEM_DEFAULT', 'SYSTEM_DEFAULT', 'admin', 'GV', 'getGcIssueHandlingRegenerate', NULL, '2024-01-25 16:09:51', NULL, '2024-01-25 16:09:51', 0, NULL);
INSERT INTO `idm_role_resource_mapping` ( `domain_code`, `tenant_code`, `role_code`, `app_code`, `resource_code`, `create_user`, `create_time`, `update_user`, `update_time`, `logic_delete`, `resource_type`) VALUES ( 'SYSTEM_DEFAULT', 'SYSTEM_DEFAULT', 'admin', 'GV', 'updateGcIssueHandlingRegenerate', NULL, '2024-01-25 16:09:51', NULL, '2024-01-25 16:09:51', 0, NULL);
INSERT INTO `idm_role_resource_mapping` ( `domain_code`, `tenant_code`, `role_code`, `app_code`, `resource_code`, `create_user`, `create_time`, `update_user`, `update_time`, `logic_delete`, `resource_type`) VALUES ( 'SYSTEM_DEFAULT', 'SYSTEM_DEFAULT', 'admin', 'GV', 'submitGcIssueHandlingRegenerate', NULL, '2024-01-25 16:09:51', NULL, '2024-01-25 16:09:51', 0, NULL);
INSERT INTO `idm_role_resource_mapping` ( `domain_code`, `tenant_code`, `role_code`, `app_code`, `resource_code`, `create_user`, `create_time`, `update_user`, `update_time`, `logic_delete`, `resource_type`) VALUES ( 'SYSTEM_DEFAULT', 'SYSTEM_DEFAULT', 'admin', 'GV', 'approveGcIssueHandlingRegenerate', NULL, '2024-01-25 16:09:51', NULL, '2024-01-25 16:09:51', 0, NULL);
INSERT INTO `idm_role_resource_mapping` ( `domain_code`, `tenant_code`, `role_code`, `app_code`, `resource_code`, `create_user`, `create_time`, `update_user`, `update_time`, `logic_delete`, `resource_type`) VALUES ( 'SYSTEM_DEFAULT', 'SYSTEM_DEFAULT', 'admin', 'GV', 'cancelGcIssueHandlingRegenerate', NULL, '2024-01-25 16:09:51', NULL, '2024-01-25 16:09:51', 0, NULL);

INSERT INTO `idm_role_resource_mapping` ( `domain_code`, `tenant_code`, `role_code`, `app_code`, `resource_code`, `create_user`, `create_time`, `update_user`, `update_time`, `logic_delete`, `resource_type`) VALUES ( 'SYSTEM_DEFAULT', 'SYSTEM_DEFAULT', 'admin', 'GV', 'queryGcIssueHandlingRedeem', NULL, '2024-01-25 16:09:51', NULL, '2024-01-25 16:09:51', 0, NULL);
INSERT INTO `idm_role_resource_mapping` ( `domain_code`, `tenant_code`, `role_code`, `app_code`, `resource_code`, `create_user`, `create_time`, `update_user`, `update_time`, `logic_delete`, `resource_type`) VALUES ( 'SYSTEM_DEFAULT', 'SYSTEM_DEFAULT', 'admin', 'GV', 'getGcIssueHandlingRedeem', NULL, '2024-01-25 16:09:51', NULL, '2024-01-25 16:09:51', 0, NULL);
INSERT INTO `idm_role_resource_mapping` ( `domain_code`, `tenant_code`, `role_code`, `app_code`, `resource_code`, `create_user`, `create_time`, `update_user`, `update_time`, `logic_delete`, `resource_type`) VALUES ( 'SYSTEM_DEFAULT', 'SYSTEM_DEFAULT', 'admin', 'GV', 'updateGcIssueHandlingRedeem', NULL, '2024-01-25 16:09:51', NULL, '2024-01-25 16:09:51', 0, NULL);
INSERT INTO `idm_role_resource_mapping` ( `domain_code`, `tenant_code`, `role_code`, `app_code`, `resource_code`, `create_user`, `create_time`, `update_user`, `update_time`, `logic_delete`, `resource_type`) VALUES ( 'SYSTEM_DEFAULT', 'SYSTEM_DEFAULT', 'admin', 'GV', 'submitGcIssueHandlingRedeem', NULL, '2024-01-25 16:09:51', NULL, '2024-01-25 16:09:51', 0, NULL);
INSERT INTO `idm_role_resource_mapping` ( `domain_code`, `tenant_code`, `role_code`, `app_code`, `resource_code`, `create_user`, `create_time`, `update_user`, `update_time`, `logic_delete`, `resource_type`) VALUES ( 'SYSTEM_DEFAULT', 'SYSTEM_DEFAULT', 'admin', 'GV', 'approveGcIssueHandlingRedeem', NULL, '2024-01-25 16:09:51', NULL, '2024-01-25 16:09:51', 0, NULL);
INSERT INTO `idm_role_resource_mapping` ( `domain_code`, `tenant_code`, `role_code`, `app_code`, `resource_code`, `create_user`, `create_time`, `update_user`, `update_time`, `logic_delete`, `resource_type`) VALUES ( 'SYSTEM_DEFAULT', 'SYSTEM_DEFAULT', 'admin', 'GV', 'cancelGcIssueHandlingRedeem', NULL, '2024-01-25 16:09:51', NULL, '2024-01-25 16:09:51', 0, NULL);

INSERT INTO `idm_role_resource_mapping` ( `domain_code`, `tenant_code`, `role_code`, `app_code`, `resource_code`, `create_user`, `create_time`, `update_user`, `update_time`, `logic_delete`, `resource_type`) VALUES ( 'SYSTEM_DEFAULT', 'SYSTEM_DEFAULT', 'admin', 'GV', 'queryGcIssueHandlingCancelRedeem', NULL, '2024-01-25 16:09:51', NULL, '2024-01-25 16:09:51', 0, NULL);
INSERT INTO `idm_role_resource_mapping` ( `domain_code`, `tenant_code`, `role_code`, `app_code`, `resource_code`, `create_user`, `create_time`, `update_user`, `update_time`, `logic_delete`, `resource_type`) VALUES ( 'SYSTEM_DEFAULT', 'SYSTEM_DEFAULT', 'admin', 'GV', 'getGcIssueHandlingCancelRedeem', NULL, '2024-01-25 16:09:51', NULL, '2024-01-25 16:09:51', 0, NULL);
INSERT INTO `idm_role_resource_mapping` ( `domain_code`, `tenant_code`, `role_code`, `app_code`, `resource_code`, `create_user`, `create_time`, `update_user`, `update_time`, `logic_delete`, `resource_type`) VALUES ( 'SYSTEM_DEFAULT', 'SYSTEM_DEFAULT', 'admin', 'GV', 'updateGcIssueHandlingCancelRedeem', NULL, '2024-01-25 16:09:51', NULL, '2024-01-25 16:09:51', 0, NULL);
INSERT INTO `idm_role_resource_mapping` ( `domain_code`, `tenant_code`, `role_code`, `app_code`, `resource_code`, `create_user`, `create_time`, `update_user`, `update_time`, `logic_delete`, `resource_type`) VALUES ( 'SYSTEM_DEFAULT', 'SYSTEM_DEFAULT', 'admin', 'GV', 'submitGcIssueHandlingCancelRedeem', NULL, '2024-01-25 16:09:51', NULL, '2024-01-25 16:09:51', 0, NULL);
INSERT INTO `idm_role_resource_mapping` ( `domain_code`, `tenant_code`, `role_code`, `app_code`, `resource_code`, `create_user`, `create_time`, `update_user`, `update_time`, `logic_delete`, `resource_type`) VALUES ( 'SYSTEM_DEFAULT', 'SYSTEM_DEFAULT', 'admin', 'GV', 'approveGcIssueHandlingCancelRedeem', NULL, '2024-01-25 16:09:51', NULL, '2024-01-25 16:09:51', 0, NULL);
INSERT INTO `idm_role_resource_mapping` ( `domain_code`, `tenant_code`, `role_code`, `app_code`, `resource_code`, `create_user`, `create_time`, `update_user`, `update_time`, `logic_delete`, `resource_type`) VALUES ( 'SYSTEM_DEFAULT', 'SYSTEM_DEFAULT', 'admin', 'GV', 'cancelGcIssueHandlingCancelRedeem', NULL, '2024-01-25 16:09:51', NULL, '2024-01-25 16:09:51', 0, NULL);

INSERT INTO `idm_role_resource_mapping` ( `domain_code`, `tenant_code`, `role_code`, `app_code`, `resource_code`, `create_user`, `create_time`, `update_user`, `update_time`, `logic_delete`, `resource_type`) VALUES ( 'SYSTEM_DEFAULT', 'SYSTEM_DEFAULT', 'admin', 'GV', 'queryGcIssueHandlingCancelSales', NULL, '2024-01-25 16:09:51', NULL, '2024-01-25 16:09:51', 0, NULL);
INSERT INTO `idm_role_resource_mapping` ( `domain_code`, `tenant_code`, `role_code`, `app_code`, `resource_code`, `create_user`, `create_time`, `update_user`, `update_time`, `logic_delete`, `resource_type`) VALUES ( 'SYSTEM_DEFAULT', 'SYSTEM_DEFAULT', 'admin', 'GV', 'getGcIssueHandlingCancelSales', NULL, '2024-01-25 16:09:51', NULL, '2024-01-25 16:09:51', 0, NULL);
INSERT INTO `idm_role_resource_mapping` ( `domain_code`, `tenant_code`, `role_code`, `app_code`, `resource_code`, `create_user`, `create_time`, `update_user`, `update_time`, `logic_delete`, `resource_type`) VALUES ( 'SYSTEM_DEFAULT', 'SYSTEM_DEFAULT', 'admin', 'GV', 'updateGcIssueHandlingCancelSales', NULL, '2024-01-25 16:09:51', NULL, '2024-01-25 16:09:51', 0, NULL);
INSERT INTO `idm_role_resource_mapping` ( `domain_code`, `tenant_code`, `role_code`, `app_code`, `resource_code`, `create_user`, `create_time`, `update_user`, `update_time`, `logic_delete`, `resource_type`) VALUES ( 'SYSTEM_DEFAULT', 'SYSTEM_DEFAULT', 'admin', 'GV', 'submitGcIssueHandlingCancelSales', NULL, '2024-01-25 16:09:51', NULL, '2024-01-25 16:09:51', 0, NULL);
INSERT INTO `idm_role_resource_mapping` ( `domain_code`, `tenant_code`, `role_code`, `app_code`, `resource_code`, `create_user`, `create_time`, `update_user`, `update_time`, `logic_delete`, `resource_type`) VALUES ( 'SYSTEM_DEFAULT', 'SYSTEM_DEFAULT', 'admin', 'GV', 'approveGcIssueHandlingCancelSales', NULL, '2024-01-25 16:09:51', NULL, '2024-01-25 16:09:51', 0, NULL);
INSERT INTO `idm_role_resource_mapping` ( `domain_code`, `tenant_code`, `role_code`, `app_code`, `resource_code`, `create_user`, `create_time`, `update_user`, `update_time`, `logic_delete`, `resource_type`) VALUES ( 'SYSTEM_DEFAULT', 'SYSTEM_DEFAULT', 'admin', 'GV', 'cancelGcIssueHandlingCancelSales', NULL, '2024-01-25 16:09:51', NULL, '2024-01-25 16:09:51', 0, NULL);



-------------------------------- 报表资源配置 --------------------------------


-- 报表菜单
INSERT INTO `idm_menu` ( `domain_code`, `app_code`, `menu_parent`, `menu_code`, `resource_code`, `menu_name`, `menu_desc`, `menu_icon`, `display_order`, `page_url`, `status`, `create_user`, `create_time`, `update_user`, `update_time`, `logic_delete`, `menu_name_lang`, `menu_lang`, `menu_type`) VALUES ( 'SYSTEM_DEFAULT', 'GV', '0', 'M000800', 'm_gc_report', 'Gc Report', NULL, NULL, 6, NULL, 1, NULL, '2022-03-28 17:19:14', NULL, '2022-03-28 17:22:39', 0, NULL, '[{\"language\": \"zh-CN\", \"menuName\": \"\"}]', '2');
INSERT INTO `idm_menu` ( `domain_code`, `app_code`, `menu_parent`, `menu_code`, `resource_code`, `menu_name`, `menu_desc`, `menu_icon`, `display_order`, `page_url`, `status`, `create_user`, `create_time`, `update_user`, `update_time`, `logic_delete`, `menu_name_lang`, `menu_lang`, `menu_type`) VALUES ( 'SYSTEM_DEFAULT', 'GV', 'M000800', 'M000801', 'm_gc_report_sales', 'Gift Card Sales Report (Summary & Detailed)', NULL, NULL, 10, '/gc/report/export/sales-report', 1, NULL, '2022-03-28 17:20:40', NULL, '2022-07-07 17:40:24', 0, NULL, '[{\"language\": \"zh-CN\", \"menuName\": \"Gift Card Sales Report (Summary & Detailed)\"}]', '2');
INSERT INTO `idm_menu` ( `domain_code`, `app_code`, `menu_parent`, `menu_code`, `resource_code`, `menu_name`, `menu_desc`, `menu_icon`, `display_order`, `page_url`, `status`, `create_user`, `create_time`, `update_user`, `update_time`, `logic_delete`, `menu_name_lang`, `menu_lang`, `menu_type`) VALUES ( 'SYSTEM_DEFAULT', 'GV', 'M000800', 'M000802', 'm_gc_report_redemption', 'Gift Card Redemption Report (Summary & Detailed)', NULL, NULL, 20, '/gc/report/export/redemption-report', 1, NULL, '2022-03-28 17:21:48', NULL, '2022-04-17 14:46:15', 0, NULL, '[{\"language\": \"zh-CN\", \"menuName\": \"\"}]', '2');
INSERT INTO `idm_menu` ( `domain_code`, `app_code`, `menu_parent`, `menu_code`, `resource_code`, `menu_name`, `menu_desc`, `menu_icon`, `display_order`, `page_url`, `status`, `create_user`, `create_time`, `update_user`, `update_time`, `logic_delete`, `menu_name_lang`, `menu_lang`, `menu_type`) VALUES ( 'SYSTEM_DEFAULT', 'GV', 'M000800', 'ME00803', 'm_gc_report_life_cycle', 'Gift Card Life Cycle Report (Summary & Detailed)', NULL, NULL, 30, '/gc/report/export/card-life-cycle-report', 1, NULL, '2022-04-17 14:36:06', NULL, '2022-04-17 14:36:06', 0, NULL, '[{\"language\": \"zh-CN\", \"menuName\": \"Inventory Report\"}]', '2');
INSERT INTO `idm_menu` ( `domain_code`, `app_code`, `menu_parent`, `menu_code`, `resource_code`, `menu_name`, `menu_desc`, `menu_icon`, `display_order`, `page_url`, `status`, `create_user`, `create_time`, `update_user`, `update_time`, `logic_delete`, `menu_name_lang`, `menu_lang`, `menu_type`) VALUES ( 'SYSTEM_DEFAULT', 'GV', 'M000800', 'ME00804', 'm_gc_report_bulk_order', 'Gift Card Bulk Order Report (Summary & Detailed)', NULL, NULL, 40, '/gc/report/export/bulk-order-report', 1, NULL, '2022-04-17 14:36:06', NULL, '2022-12-29 10:26:51', 0, NULL, '[{\"language\": \"zh-CN\", \"menuName\": \"Voucher Movement Report \"}]', '2');
INSERT INTO `idm_menu` ( `domain_code`, `app_code`, `menu_parent`, `menu_code`, `resource_code`, `menu_name`, `menu_desc`, `menu_icon`, `display_order`, `page_url`, `status`, `create_user`, `create_time`, `update_user`, `update_time`, `logic_delete`, `menu_name_lang`, `menu_lang`, `menu_type`) VALUES ( 'SYSTEM_DEFAULT', 'GV', 'M000800', 'ME00805', 'm_gc_report_expiry', 'Gift Card Expiry Report (Summary & Detailed)', NULL, NULL, 50, '/gc/report/export/expiry-gv-report', 1, NULL, '2022-04-17 14:36:06', NULL, '2022-04-17 14:36:06', 0, NULL, '[{\"language\": \"zh-CN\", \"menuName\": \"Liability Report\"}]', '2');
INSERT INTO `idm_menu` ( `domain_code`, `app_code`, `menu_parent`, `menu_code`, `resource_code`, `menu_name`, `menu_desc`, `menu_icon`, `display_order`, `page_url`, `status`, `create_user`, `create_time`, `update_user`, `update_time`, `logic_delete`, `menu_name_lang`, `menu_lang`, `menu_type`) VALUES ( 'SYSTEM_DEFAULT', 'GV', 'M000800', 'ME00806', 'm_gc_report_cancel_sales', 'Gift Card Cancel Sales Report (Summary & Detailed)', NULL, NULL, 60, '/gc/report/export/cancel-sales-report', 1, NULL, '2022-04-17 14:36:06', NULL, '2022-04-17 14:36:06', 0, NULL, '[{\"language\": \"zh-CN\", \"menuName\": \"Redemption Report\"}]', '2');
INSERT INTO `idm_menu` ( `domain_code`, `app_code`, `menu_parent`, `menu_code`, `resource_code`, `menu_name`, `menu_desc`, `menu_icon`, `display_order`, `page_url`, `status`, `create_user`, `create_time`, `update_user`, `update_time`, `logic_delete`, `menu_name_lang`, `menu_lang`, `menu_type`) VALUES ( 'SYSTEM_DEFAULT', 'GV', 'M000800', 'ME00807', 'm_gc_report_deactivated', 'Gift Card Deactivated Report (Summary & Detailed)', NULL, NULL, 70, '/gc/report/export/blocked-deactivated-report', 1, NULL, '2022-04-17 14:36:06', NULL, '2022-04-17 14:46:01', 0, NULL, '[{\"language\": \"zh-CN\", \"menuName\": \"Booklet Inventory Report\"}]', '2');
INSERT INTO `idm_menu` ( `domain_code`, `app_code`, `menu_parent`, `menu_code`, `resource_code`, `menu_name`, `menu_desc`, `menu_icon`, `display_order`, `page_url`, `status`, `create_user`, `create_time`, `update_user`, `update_time`, `logic_delete`, `menu_name_lang`, `menu_lang`, `menu_type`) VALUES ( 'SYSTEM_DEFAULT', 'GV', 'M000800', 'ME00808', 'm_gc_report_reactivated', 'Gift Card Reactivated Report (Summary & Detailed)', NULL, NULL, 80, '/gc/report/export/reactivated-report', 1, NULL, '2022-04-17 14:36:06', NULL, '2022-04-17 14:36:06', 0, NULL, '[{\"language\": \"zh-CN\", \"menuName\": \"Transactions Detailed Report\"}]', '2');
INSERT INTO `idm_menu` ( `domain_code`, `app_code`, `menu_parent`, `menu_code`, `resource_code`, `menu_name`, `menu_desc`, `menu_icon`, `display_order`, `page_url`, `status`, `create_user`, `create_time`, `update_user`, `update_time`, `logic_delete`, `menu_name_lang`, `menu_lang`, `menu_type`) VALUES ( 'SYSTEM_DEFAULT', 'GV', 'M000800', 'ME00809', 'm_gc_report_balance_correction', 'Gift Card Balance Correction Report (Summary & Detailed)', NULL, NULL, 90, '/gc/report/export/balance-correction-report', 1, NULL, '2022-04-17 14:36:06', NULL, '2022-04-17 14:47:07', 0, NULL, '[{\"language\": \"zh-CN\", \"menuName\": \"Cancel Redeemed Report\"}]', '2');
INSERT INTO `idm_menu` ( `domain_code`, `app_code`, `menu_parent`, `menu_code`, `resource_code`, `menu_name`, `menu_desc`, `menu_icon`, `display_order`, `page_url`, `status`, `create_user`, `create_time`, `update_user`, `update_time`, `logic_delete`, `menu_name_lang`, `menu_lang`, `menu_type`) VALUES ( 'SYSTEM_DEFAULT', 'GV', 'M000800', 'ME008010', 'm_gc_report_regenerate_code', 'Gift Card Regenerate Activation Code Report (Summary & Detailed)', NULL, NULL, 100, '/gc/report/export/regenerate-activation-code-report', 1, NULL, '2022-04-17 14:36:06', NULL, '2022-04-17 14:47:11', 0, NULL, '[{\"language\": \"zh-CN\", \"menuName\": \"Cancel Sales Report\"}]', '2');
INSERT INTO `idm_menu` ( `domain_code`, `app_code`, `menu_parent`, `menu_code`, `resource_code`, `menu_name`, `menu_desc`, `menu_icon`, `display_order`, `page_url`, `status`, `create_user`, `create_time`, `update_user`, `update_time`, `logic_delete`, `menu_name_lang`, `menu_lang`, `menu_type`) VALUES ( 'SYSTEM_DEFAULT', 'GV', 'M000800', 'ME008011', 'm_gc_report_liability', 'Gift Card Liability Report (Summary & Detailed)', NULL, NULL, 110, '/gc/report/export/liability-report', 1, NULL, '2022-04-17 14:36:06', NULL, '2022-12-22 15:23:01', 0, NULL, '[{\"language\": \"zh-CN\", \"menuName\": \"Deactivated Report\"}]', '2');
INSERT INTO `idm_menu` ( `domain_code`, `app_code`, `menu_parent`, `menu_code`, `resource_code`, `menu_name`, `menu_desc`, `menu_icon`, `display_order`, `page_url`, `status`, `create_user`, `create_time`, `update_user`, `update_time`, `logic_delete`, `menu_name_lang`, `menu_lang`, `menu_type`) VALUES ( 'SYSTEM_DEFAULT', 'GV', 'M000800', 'ME008012', 'm_gc_report_audit_trail', 'Gift Card Audit Trail Report', NULL, NULL, 120, '/gc/report/export/audit-trail-report', 1, NULL, '2022-04-17 14:36:06', NULL, '2022-04-17 14:36:06', 0, NULL, '[{\"language\": \"zh-CN\", \"menuName\": \"Sales Report\"}]', '2');
INSERT INTO `idm_menu` ( `domain_code`, `app_code`, `menu_parent`, `menu_code`, `resource_code`, `menu_name`, `menu_desc`, `menu_icon`, `display_order`, `page_url`, `status`, `create_user`, `create_time`, `update_user`, `update_time`, `logic_delete`, `menu_name_lang`, `menu_lang`, `menu_type`) VALUES ( 'SYSTEM_DEFAULT', 'GV', 'M000800', 'ME008013', 'm_gc_report_transaction_detail', 'Gift Card Transaction Detail Report', NULL, NULL, 121, '/gc/report/export/transactions-detailed-report', 1, NULL, '2022-04-17 14:36:06', NULL, '2022-12-22 16:45:58', 0, NULL, '[{\"language\": \"zh-CN\", \"menuName\": \"Card Life Cycle Report\"}]', '2');
INSERT INTO `idm_menu` ( `domain_code`, `app_code`, `menu_parent`, `menu_code`, `resource_code`, `menu_name`, `menu_desc`, `menu_icon`, `display_order`, `page_url`, `status`, `create_user`, `create_time`, `update_user`, `update_time`, `logic_delete`, `menu_name_lang`, `menu_lang`, `menu_type`) VALUES ( 'SYSTEM_DEFAULT', 'GV', 'M000800', 'ME008014', 'm_gc_report_expiry_extension', 'Gift Card Expiry Extension Report', NULL, NULL, 122, '/gc/report/export/expiry-extension-report', 1, NULL, '2022-04-17 14:36:06', NULL, '2022-04-17 14:46:07', 0, NULL, '[{\"language\": \"zh-CN\", \"menuName\": \"Bulk Order Report\"}]', '2');
INSERT INTO `idm_menu` ( `domain_code`, `app_code`, `menu_parent`, `menu_code`, `resource_code`, `menu_name`, `menu_desc`, `menu_icon`, `display_order`, `page_url`, `status`, `create_user`, `create_time`, `update_user`, `update_time`, `logic_delete`, `menu_name_lang`, `menu_lang`, `menu_type`) VALUES ( 'SYSTEM_DEFAULT', 'GV', 'M000800', 'ME008015', 'm_gc_report_latest_status', 'Gift Card Latest GC Status Report', NULL, NULL, 123, '/gc/report/export/latest-gv-status-report', 1, NULL, '2022-04-17 14:36:06', NULL, '2022-04-17 14:36:06', 0, NULL, '[{\"language\": \"zh-CN\", \"menuName\": \"Goods in Transit Report\"}]', '2');

-- 报表资源
INSERT INTO `idm_resource` ( `domain_code`, `app_code`, `resource_parent`, `resource_code`, `resource_name`, `resource_type`, `resource_class`, `access_type`, `status`, `create_user`, `create_time`, `update_user`, `update_time`, `logic_delete`, `position_code`, `resource_url`, `log_switch`, `log_business_code`, `log_desc`, `log_business`) VALUES ('SYSTEM_DEFAULT', 'GV', 'root', 'm_gc_report', 'Gc Report', 2, 1, 3, 1, NULL, '2022-03-28 17:16:08', NULL, '2023-03-10 13:56:03', 0, NULL, NULL, 0, NULL, NULL, NULL);
INSERT INTO `idm_resource` ( `domain_code`, `app_code`, `resource_parent`, `resource_code`, `resource_name`, `resource_type`, `resource_class`, `access_type`, `status`, `create_user`, `create_time`, `update_user`, `update_time`, `logic_delete`, `position_code`, `resource_url`, `log_switch`, `log_business_code`, `log_desc`, `log_business`) VALUES ('SYSTEM_DEFAULT', 'GV', 'm_gc_report', 'm_gc_report_sales', 'Gift Card Sales Report', 2, 1, 3, 1, NULL, '2022-03-28 17:16:53', NULL, '2023-03-10 13:56:03', 0, NULL, NULL, 0, NULL, NULL, NULL);
INSERT INTO `idm_resource` ( `domain_code`, `app_code`, `resource_parent`, `resource_code`, `resource_name`, `resource_type`, `resource_class`, `access_type`, `status`, `create_user`, `create_time`, `update_user`, `update_time`, `logic_delete`, `position_code`, `resource_url`, `log_switch`, `log_business_code`, `log_desc`, `log_business`) VALUES ('SYSTEM_DEFAULT', 'GV', 'm_gc_report', 'm_gc_report_redemption', 'Gift Card Redemption Report ', 2, 1, 3, 1, NULL, '2022-03-28 17:17:58', NULL, '2023-03-10 13:56:03', 0, NULL, NULL, 0, NULL, NULL, NULL);
INSERT INTO `idm_resource` ( `domain_code`, `app_code`, `resource_parent`, `resource_code`, `resource_name`, `resource_type`, `resource_class`, `access_type`, `status`, `create_user`, `create_time`, `update_user`, `update_time`, `logic_delete`, `position_code`, `resource_url`, `log_switch`, `log_business_code`, `log_desc`, `log_business`) VALUES ('SYSTEM_DEFAULT', 'GV', 'm_gc_report', 'm_gc_report_life_cycle', 'Gift Card Life Cycle Report', 2, 1, 3, 1, NULL, '2022-03-30 13:44:24', NULL, '2023-03-10 13:56:03', 0, NULL, NULL, 0, NULL, NULL, NULL);
INSERT INTO `idm_resource` ( `domain_code`, `app_code`, `resource_parent`, `resource_code`, `resource_name`, `resource_type`, `resource_class`, `access_type`, `status`, `create_user`, `create_time`, `update_user`, `update_time`, `logic_delete`, `position_code`, `resource_url`, `log_switch`, `log_business_code`, `log_desc`, `log_business`) VALUES ('SYSTEM_DEFAULT', 'GV', 'm_gc_report', 'm_gc_report_bulk_order', 'Gift Card Bulk Order Report', 2, 1, 3, 1, NULL, '2022-04-17 14:36:06', NULL, '2022-04-25 14:42:03', 0, NULL, NULL, 0, NULL, NULL, NULL);
INSERT INTO `idm_resource` ( `domain_code`, `app_code`, `resource_parent`, `resource_code`, `resource_name`, `resource_type`, `resource_class`, `access_type`, `status`, `create_user`, `create_time`, `update_user`, `update_time`, `logic_delete`, `position_code`, `resource_url`, `log_switch`, `log_business_code`, `log_desc`, `log_business`) VALUES ('SYSTEM_DEFAULT', 'GV', 'm_gc_report', 'm_gc_report_expiry', 'Gift Card Expiry Report', 2, 1, 3, 1, NULL, '2022-04-17 14:36:06', NULL, '2022-04-25 14:42:03', 0, NULL, NULL, 0, NULL, NULL, NULL);
INSERT INTO `idm_resource` ( `domain_code`, `app_code`, `resource_parent`, `resource_code`, `resource_name`, `resource_type`, `resource_class`, `access_type`, `status`, `create_user`, `create_time`, `update_user`, `update_time`, `logic_delete`, `position_code`, `resource_url`, `log_switch`, `log_business_code`, `log_desc`, `log_business`) VALUES ('SYSTEM_DEFAULT', 'GV', 'm_gc_report', 'm_gc_report_cancel_sales', 'Gift Card Cancel Sales Report', 2, 1, 3, 1, NULL, '2022-04-17 14:36:06', NULL, '2022-04-25 14:42:03', 0, NULL, NULL, 0, NULL, NULL, NULL);
INSERT INTO `idm_resource` ( `domain_code`, `app_code`, `resource_parent`, `resource_code`, `resource_name`, `resource_type`, `resource_class`, `access_type`, `status`, `create_user`, `create_time`, `update_user`, `update_time`, `logic_delete`, `position_code`, `resource_url`, `log_switch`, `log_business_code`, `log_desc`, `log_business`) VALUES ('SYSTEM_DEFAULT', 'GV', 'm_gc_report', 'm_gc_report_deactivated', 'Gift Card Deactivated Report', 2, 1, 3, 1, NULL, '2022-04-17 14:36:06', NULL, '2022-04-25 14:42:03', 0, NULL, NULL, 0, NULL, NULL, NULL);
INSERT INTO `idm_resource` ( `domain_code`, `app_code`, `resource_parent`, `resource_code`, `resource_name`, `resource_type`, `resource_class`, `access_type`, `status`, `create_user`, `create_time`, `update_user`, `update_time`, `logic_delete`, `position_code`, `resource_url`, `log_switch`, `log_business_code`, `log_desc`, `log_business`) VALUES ('SYSTEM_DEFAULT', 'GV', 'm_gc_report', 'm_gc_report_reactivated', 'Gift Card Reactivated Report', 2, 1, 3, 1, NULL, '2022-04-17 14:36:06', NULL, '2022-04-25 14:42:02', 0, NULL, NULL, 0, NULL, NULL, NULL);
INSERT INTO `idm_resource` ( `domain_code`, `app_code`, `resource_parent`, `resource_code`, `resource_name`, `resource_type`, `resource_class`, `access_type`, `status`, `create_user`, `create_time`, `update_user`, `update_time`, `logic_delete`, `position_code`, `resource_url`, `log_switch`, `log_business_code`, `log_desc`, `log_business`) VALUES ('SYSTEM_DEFAULT', 'GV', 'm_gc_report', 'm_gc_report_balance_correction', 'Gift Card Balance Correction Report', 2, 1, 3, 1, NULL, '2022-04-17 14:36:06', NULL, '2022-04-25 14:42:02', 0, NULL, NULL, 0, NULL, NULL, NULL);
INSERT INTO `idm_resource` ( `domain_code`, `app_code`, `resource_parent`, `resource_code`, `resource_name`, `resource_type`, `resource_class`, `access_type`, `status`, `create_user`, `create_time`, `update_user`, `update_time`, `logic_delete`, `position_code`, `resource_url`, `log_switch`, `log_business_code`, `log_desc`, `log_business`) VALUES ('SYSTEM_DEFAULT', 'GV', 'm_gc_report', 'm_gc_report_regenerate_code', 'Gift Card Regenerate Activation Code Report', 2, 1, 3, 1, NULL, '2022-04-17 14:36:06', NULL, '2022-04-25 14:42:02', 0, NULL, NULL, 0, NULL, NULL, NULL);
INSERT INTO `idm_resource` ( `domain_code`, `app_code`, `resource_parent`, `resource_code`, `resource_name`, `resource_type`, `resource_class`, `access_type`, `status`, `create_user`, `create_time`, `update_user`, `update_time`, `logic_delete`, `position_code`, `resource_url`, `log_switch`, `log_business_code`, `log_desc`, `log_business`) VALUES ('SYSTEM_DEFAULT', 'GV', 'm_gc_report', 'm_gc_report_liability', 'Gift Card Liability Report', 2, 1, 3, 1, NULL, '2022-04-17 14:36:06', NULL, '2022-04-25 14:42:02', 0, NULL, NULL, 0, NULL, NULL, NULL);
INSERT INTO `idm_resource` ( `domain_code`, `app_code`, `resource_parent`, `resource_code`, `resource_name`, `resource_type`, `resource_class`, `access_type`, `status`, `create_user`, `create_time`, `update_user`, `update_time`, `logic_delete`, `position_code`, `resource_url`, `log_switch`, `log_business_code`, `log_desc`, `log_business`) VALUES ('SYSTEM_DEFAULT', 'GV', 'm_gc_report', 'm_gc_report_audit_trail', 'Gift Card Audit Trail Report', 2, 1, 3, 1, NULL, '2022-04-17 14:36:06', NULL, '2022-04-25 14:42:02', 0, NULL, NULL, 0, NULL, NULL, NULL);
INSERT INTO `idm_resource` ( `domain_code`, `app_code`, `resource_parent`, `resource_code`, `resource_name`, `resource_type`, `resource_class`, `access_type`, `status`, `create_user`, `create_time`, `update_user`, `update_time`, `logic_delete`, `position_code`, `resource_url`, `log_switch`, `log_business_code`, `log_desc`, `log_business`) VALUES ('SYSTEM_DEFAULT', 'GV', 'm_gc_report', 'm_gc_report_transaction_detail', 'Gift Card Transaction Detail Report', 2, 1, 3, 1, NULL, '2022-04-17 14:36:06', NULL, '2022-04-25 14:42:02', 0, NULL, NULL, 0, NULL, NULL, NULL);
INSERT INTO `idm_resource` ( `domain_code`, `app_code`, `resource_parent`, `resource_code`, `resource_name`, `resource_type`, `resource_class`, `access_type`, `status`, `create_user`, `create_time`, `update_user`, `update_time`, `logic_delete`, `position_code`, `resource_url`, `log_switch`, `log_business_code`, `log_desc`, `log_business`) VALUES ('SYSTEM_DEFAULT', 'GV', 'm_gc_report', 'm_gc_report_expiry_extension', 'Gift Card Expiry Extension Report', 2, 1, 3, 1, NULL, '2022-04-17 14:36:06', NULL, '2022-04-25 14:42:02', 0, NULL, NULL, 0, NULL, NULL, NULL);
INSERT INTO `idm_resource` ( `domain_code`, `app_code`, `resource_parent`, `resource_code`, `resource_name`, `resource_type`, `resource_class`, `access_type`, `status`, `create_user`, `create_time`, `update_user`, `update_time`, `logic_delete`, `position_code`, `resource_url`, `log_switch`, `log_business_code`, `log_desc`, `log_business`) VALUES ('SYSTEM_DEFAULT', 'GV', 'm_gc_report', 'm_gc_report_latest_status', 'Gift Card Latest GC Status Report', 2, 1, 3, 1, NULL, '2022-04-17 14:36:06', NULL, '2022-04-25 14:42:02', 0, NULL, NULL, 0, NULL, NULL, NULL);


-- 页面
INSERT INTO `idm_page` ( `domain_code`, `app_code`, `menu_code`, `page_code`, `resource_code`, `page_name`, `page_desc`, `page_url`, `status`, `create_user`, `create_time`, `update_user`, `update_time`, `logic_delete`) VALUES ( 'SYSTEM_DEFAULT', 'GV', 'M000801',  '2070', 'RS2070', '/gc/report/export/sales-report', NULL, '/gc/report/export/sales-report', 1, NULL, '2022-04-27 11:43:24', NULL, '2022-04-28 11:53:52', 0);
INSERT INTO `idm_page` ( `domain_code`, `app_code`, `menu_code`, `page_code`, `resource_code`, `page_name`, `page_desc`, `page_url`, `status`, `create_user`, `create_time`, `update_user`, `update_time`, `logic_delete`) VALUES ( 'SYSTEM_DEFAULT', 'GV', 'M000802',  '2071', 'RS2071', '/gc/report/export/redemption-report', NULL, '/gc/report/export/redemption-report', 1, NULL, '2022-04-27 11:43:24', NULL, '2022-04-28 11:53:52', 0);
INSERT INTO `idm_page` ( `domain_code`, `app_code`, `menu_code`, `page_code`, `resource_code`, `page_name`, `page_desc`, `page_url`, `status`, `create_user`, `create_time`, `update_user`, `update_time`, `logic_delete`) VALUES ( 'SYSTEM_DEFAULT', 'GV', 'ME00803',  '2072', 'RS2072', '/gc/report/export/card-life-cycle-report', NULL, '/gc/report/export/card-life-cycle-report', 1, NULL, '2022-04-27 11:43:24', NULL, '2022-04-28 11:53:52', 0);
INSERT INTO `idm_page` ( `domain_code`, `app_code`, `menu_code`, `page_code`, `resource_code`, `page_name`, `page_desc`, `page_url`, `status`, `create_user`, `create_time`, `update_user`, `update_time`, `logic_delete`) VALUES ( 'SYSTEM_DEFAULT', 'GV', 'ME00804',  '2073', 'RS2073', '/gc/report/export/bulk-order-report', NULL, '/gc/report/export/bulk-order-report', 1, NULL, '2022-04-27 11:43:24', NULL, '2022-04-28 11:53:52', 0);
INSERT INTO `idm_page` ( `domain_code`, `app_code`, `menu_code`, `page_code`, `resource_code`, `page_name`, `page_desc`, `page_url`, `status`, `create_user`, `create_time`, `update_user`, `update_time`, `logic_delete`) VALUES ( 'SYSTEM_DEFAULT', 'GV', 'ME00805',  '2074', 'RS2074', '/gc/report/export/expiry-gv-report', NULL, '/gc/report/export/expiry-gv-report', 1, NULL, '2022-04-27 11:43:24', NULL, '2022-04-28 11:53:52', 0);
INSERT INTO `idm_page` ( `domain_code`, `app_code`, `menu_code`, `page_code`, `resource_code`, `page_name`, `page_desc`, `page_url`, `status`, `create_user`, `create_time`, `update_user`, `update_time`, `logic_delete`) VALUES ( 'SYSTEM_DEFAULT', 'GV', 'ME00806',  '2075', 'RS2075', '/gc/report/export/cancel-sales-report', NULL, '/gc/report/export/cancel-sales-report', 1, NULL, '2022-04-27 11:43:24', NULL, '2022-04-28 11:53:52', 0);
INSERT INTO `idm_page` ( `domain_code`, `app_code`, `menu_code`, `page_code`, `resource_code`, `page_name`, `page_desc`, `page_url`, `status`, `create_user`, `create_time`, `update_user`, `update_time`, `logic_delete`) VALUES ( 'SYSTEM_DEFAULT', 'GV', 'ME00807',  '2076', 'RS2076', '/gc/report/export/blocked-deactivated-report', NULL, '/gc/report/export/blocked-deactivated-report', 1, NULL, '2022-04-27 11:43:24', NULL, '2022-04-28 11:53:52', 0);
INSERT INTO `idm_page` ( `domain_code`, `app_code`, `menu_code`, `page_code`, `resource_code`, `page_name`, `page_desc`, `page_url`, `status`, `create_user`, `create_time`, `update_user`, `update_time`, `logic_delete`) VALUES ( 'SYSTEM_DEFAULT', 'GV', 'ME00808',  '2077', 'RS2077', '/gc/report/export/reactivated-report', NULL, '/gc/report/export/reactivated-report', 1, NULL, '2022-04-27 11:43:24', NULL, '2022-04-28 11:53:52', 0);
INSERT INTO `idm_page` ( `domain_code`, `app_code`, `menu_code`, `page_code`, `resource_code`, `page_name`, `page_desc`, `page_url`, `status`, `create_user`, `create_time`, `update_user`, `update_time`, `logic_delete`) VALUES ( 'SYSTEM_DEFAULT', 'GV', 'ME00809',  '2078', 'RS2078', '/gc/report/export/balance-correction-report', NULL, '/gc/report/export/balance-correction-report', 1, NULL, '2022-04-27 11:43:24', NULL, '2022-04-28 11:53:52', 0);
INSERT INTO `idm_page` ( `domain_code`, `app_code`, `menu_code`, `page_code`, `resource_code`, `page_name`, `page_desc`, `page_url`, `status`, `create_user`, `create_time`, `update_user`, `update_time`, `logic_delete`) VALUES ( 'SYSTEM_DEFAULT', 'GV', 'ME008010', '2079', 'RS2079', '/gc/report/export/regenerate-activation-code-report', NULL, '/gc/report/export/regenerate-activation-code-report', 1, NULL, '2022-04-27 11:43:24', NULL, '2022-04-28 11:53:52', 0);
INSERT INTO `idm_page` ( `domain_code`, `app_code`, `menu_code`, `page_code`, `resource_code`, `page_name`, `page_desc`, `page_url`, `status`, `create_user`, `create_time`, `update_user`, `update_time`, `logic_delete`) VALUES ( 'SYSTEM_DEFAULT', 'GV', 'ME008011', '2080', 'RS2080', '/gc/report/export/liability-report', NULL, '/gc/report/export/liability-report', 1, NULL, '2022-04-27 11:43:24', NULL, '2022-04-28 11:53:53', 0);
INSERT INTO `idm_page` ( `domain_code`, `app_code`, `menu_code`, `page_code`, `resource_code`, `page_name`, `page_desc`, `page_url`, `status`, `create_user`, `create_time`, `update_user`, `update_time`, `logic_delete`) VALUES ( 'SYSTEM_DEFAULT', 'GV', 'ME008012', '2081', 'RS2081', '/gc/report/export/audit-trail-report', NULL, '/gc/report/export/audit-trail-report', 1, NULL, '2022-04-27 11:43:24', NULL, '2022-04-28 11:53:53', 0);
INSERT INTO `idm_page` ( `domain_code`, `app_code`, `menu_code`, `page_code`, `resource_code`, `page_name`, `page_desc`, `page_url`, `status`, `create_user`, `create_time`, `update_user`, `update_time`, `logic_delete`) VALUES ( 'SYSTEM_DEFAULT', 'GV', 'ME008013', '2082', 'RS2082', '/gc/report/export/transactions-detailed-report', NULL, '/gc/report/export/transactions-detailed-report', 1, NULL, '2022-04-27 11:43:24', NULL, '2022-04-28 11:53:53', 0);
INSERT INTO `idm_page` ( `domain_code`, `app_code`, `menu_code`, `page_code`, `resource_code`, `page_name`, `page_desc`, `page_url`, `status`, `create_user`, `create_time`, `update_user`, `update_time`, `logic_delete`) VALUES ( 'SYSTEM_DEFAULT', 'GV', 'ME008014', '2083', 'RS2083', '/gc/report/export/expiry-extension-report', NULL, '/gc/report/export/expiry-extension-report', 1, NULL, '2022-04-27 11:43:24', NULL, '2022-04-28 11:53:53', 0);
INSERT INTO `idm_page` ( `domain_code`, `app_code`, `menu_code`, `page_code`, `resource_code`, `page_name`, `page_desc`, `page_url`, `status`, `create_user`, `create_time`, `update_user`, `update_time`, `logic_delete`) VALUES ( 'SYSTEM_DEFAULT', 'GV', 'ME008015', '2084', 'RS2084', '/gc/report/export/latest-gv-status-report', NULL, '/gc/report/export/latest-gv-status-report', 1, NULL, '2022-04-27 11:43:24', NULL, '2022-04-28 11:53:53', 0);


-- 角色菜单资源绑定
INSERT INTO `idm_role_resource_mapping` ( `domain_code`, `tenant_code`, `role_code`, `app_code`, `resource_code`, `create_user`, `create_time`, `update_user`, `update_time`, `logic_delete`, `resource_type`) VALUES ( 'SYSTEM_DEFAULT', 'SYSTEM_DEFAULT', 'admin', 'GV', 'm_gc_report', NULL, '2024-01-25 16:09:51', NULL, '2024-01-25 16:09:51', 0, NULL);
INSERT INTO `idm_role_resource_mapping` ( `domain_code`, `tenant_code`, `role_code`, `app_code`, `resource_code`, `create_user`, `create_time`, `update_user`, `update_time`, `logic_delete`, `resource_type`) VALUES ( 'SYSTEM_DEFAULT', 'SYSTEM_DEFAULT', 'admin', 'GV', 'm_gc_report_sales', NULL, '2024-01-25 16:09:51', NULL, '2024-01-25 16:09:51', 0, NULL);
INSERT INTO `idm_role_resource_mapping` ( `domain_code`, `tenant_code`, `role_code`, `app_code`, `resource_code`, `create_user`, `create_time`, `update_user`, `update_time`, `logic_delete`, `resource_type`) VALUES ( 'SYSTEM_DEFAULT', 'SYSTEM_DEFAULT', 'admin', 'GV', 'm_gc_report_redemption', NULL, '2024-01-25 16:09:51', NULL, '2024-01-25 16:09:51', 0, NULL);
INSERT INTO `idm_role_resource_mapping` ( `domain_code`, `tenant_code`, `role_code`, `app_code`, `resource_code`, `create_user`, `create_time`, `update_user`, `update_time`, `logic_delete`, `resource_type`) VALUES ( 'SYSTEM_DEFAULT', 'SYSTEM_DEFAULT', 'admin', 'GV', 'm_gc_report_life_cycle', NULL, '2024-01-25 16:09:51', NULL, '2024-01-25 16:09:51', 0, NULL);
INSERT INTO `idm_role_resource_mapping` ( `domain_code`, `tenant_code`, `role_code`, `app_code`, `resource_code`, `create_user`, `create_time`, `update_user`, `update_time`, `logic_delete`, `resource_type`) VALUES ( 'SYSTEM_DEFAULT', 'SYSTEM_DEFAULT', 'admin', 'GV', 'm_gc_report_bulk_order', NULL, '2024-01-25 16:09:51', NULL, '2024-01-25 16:09:51', 0, NULL);
INSERT INTO `idm_role_resource_mapping` ( `domain_code`, `tenant_code`, `role_code`, `app_code`, `resource_code`, `create_user`, `create_time`, `update_user`, `update_time`, `logic_delete`, `resource_type`) VALUES ( 'SYSTEM_DEFAULT', 'SYSTEM_DEFAULT', 'admin', 'GV', 'm_gc_report_expiry', NULL, '2024-01-25 16:09:51', NULL, '2024-01-25 16:09:51', 0, NULL);
INSERT INTO `idm_role_resource_mapping` ( `domain_code`, `tenant_code`, `role_code`, `app_code`, `resource_code`, `create_user`, `create_time`, `update_user`, `update_time`, `logic_delete`, `resource_type`) VALUES ( 'SYSTEM_DEFAULT', 'SYSTEM_DEFAULT', 'admin', 'GV', 'm_gc_report_cancel_sales', NULL, '2024-01-25 16:09:51', NULL, '2024-01-25 16:09:51', 0, NULL);
INSERT INTO `idm_role_resource_mapping` ( `domain_code`, `tenant_code`, `role_code`, `app_code`, `resource_code`, `create_user`, `create_time`, `update_user`, `update_time`, `logic_delete`, `resource_type`) VALUES ( 'SYSTEM_DEFAULT', 'SYSTEM_DEFAULT', 'admin', 'GV', 'm_gc_report_deactivated', NULL, '2024-01-25 16:09:51', NULL, '2024-01-25 16:09:51', 0, NULL);
INSERT INTO `idm_role_resource_mapping` ( `domain_code`, `tenant_code`, `role_code`, `app_code`, `resource_code`, `create_user`, `create_time`, `update_user`, `update_time`, `logic_delete`, `resource_type`) VALUES ( 'SYSTEM_DEFAULT', 'SYSTEM_DEFAULT', 'admin', 'GV', 'm_gc_report_reactivated', NULL, '2024-01-25 16:09:51', NULL, '2024-01-25 16:09:51', 0, NULL);
INSERT INTO `idm_role_resource_mapping` ( `domain_code`, `tenant_code`, `role_code`, `app_code`, `resource_code`, `create_user`, `create_time`, `update_user`, `update_time`, `logic_delete`, `resource_type`) VALUES ( 'SYSTEM_DEFAULT', 'SYSTEM_DEFAULT', 'admin', 'GV', 'm_gc_report_balance_correction', NULL, '2024-01-25 16:09:51', NULL, '2024-01-25 16:09:51', 0, NULL);
INSERT INTO `idm_role_resource_mapping` ( `domain_code`, `tenant_code`, `role_code`, `app_code`, `resource_code`, `create_user`, `create_time`, `update_user`, `update_time`, `logic_delete`, `resource_type`) VALUES ( 'SYSTEM_DEFAULT', 'SYSTEM_DEFAULT', 'admin', 'GV', 'm_gc_report_regenerate_code', NULL, '2024-01-25 16:09:51', NULL, '2024-01-25 16:09:51', 0, NULL);
INSERT INTO `idm_role_resource_mapping` ( `domain_code`, `tenant_code`, `role_code`, `app_code`, `resource_code`, `create_user`, `create_time`, `update_user`, `update_time`, `logic_delete`, `resource_type`) VALUES ( 'SYSTEM_DEFAULT', 'SYSTEM_DEFAULT', 'admin', 'GV', 'm_gc_report_liability', NULL, '2024-01-25 16:09:51', NULL, '2024-01-25 16:09:51', 0, NULL);
INSERT INTO `idm_role_resource_mapping` ( `domain_code`, `tenant_code`, `role_code`, `app_code`, `resource_code`, `create_user`, `create_time`, `update_user`, `update_time`, `logic_delete`, `resource_type`) VALUES ( 'SYSTEM_DEFAULT', 'SYSTEM_DEFAULT', 'admin', 'GV', 'm_gc_report_audit_trail', NULL, '2024-01-25 16:09:51', NULL, '2024-01-25 16:09:51', 0, NULL);
INSERT INTO `idm_role_resource_mapping` ( `domain_code`, `tenant_code`, `role_code`, `app_code`, `resource_code`, `create_user`, `create_time`, `update_user`, `update_time`, `logic_delete`, `resource_type`) VALUES ( 'SYSTEM_DEFAULT', 'SYSTEM_DEFAULT', 'admin', 'GV', 'm_gc_report_transaction_detail', NULL, '2024-01-25 16:09:51', NULL, '2024-01-25 16:09:51', 0, NULL);
INSERT INTO `idm_role_resource_mapping` ( `domain_code`, `tenant_code`, `role_code`, `app_code`, `resource_code`, `create_user`, `create_time`, `update_user`, `update_time`, `logic_delete`, `resource_type`) VALUES ( 'SYSTEM_DEFAULT', 'SYSTEM_DEFAULT', 'admin', 'GV', 'm_gc_report_expiry_extension', NULL, '2024-01-25 16:09:51', NULL, '2024-01-25 16:09:51', 0, NULL);
INSERT INTO `idm_role_resource_mapping` ( `domain_code`, `tenant_code`, `role_code`, `app_code`, `resource_code`, `create_user`, `create_time`, `update_user`, `update_time`, `logic_delete`, `resource_type`) VALUES ( 'SYSTEM_DEFAULT', 'SYSTEM_DEFAULT', 'admin', 'GV', 'm_gc_report_latest_status', NULL, '2024-01-25 16:09:51', NULL, '2024-01-25 16:09:51', 0, NULL);