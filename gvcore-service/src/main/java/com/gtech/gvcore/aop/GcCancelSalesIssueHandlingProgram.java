package com.gtech.gvcore.aop;

import com.gtech.gvcore.common.enums.IssueHandlingTypeEnum;
import com.gtech.gvcore.common.response.outlet.OutletResponse;
import com.gtech.gvcore.dao.model.IssueHandlingDetails;
import com.gtech.gvcore.giftcard.application.service.GiftCardApplicationService;
import com.gtech.gvcore.giftcard.infrastructure.entity.GcSalesCancelEntity;
import com.gtech.gvcore.giftcard.infrastructure.entity.GiftCardEntity;
import com.gtech.gvcore.giftcard.infrastructure.mapper.GcCancelSalesMapper;
import com.gtech.gvcore.helper.GvCodeHelper;
import com.gtech.gvcore.service.OutletService;
import com.gtech.gvcore.threadpool.ThreadPoolCenter;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.gtech.gvcore.aop.IssueHandlerTransactionAspect.summarizeCompletedIssue;

@Component
public class GcCancelSalesIssueHandlingProgram implements IssueHandlerTransactionAspect.IssueHandlingTransactionDataProgram {

    @Autowired
    GiftCardApplicationService gcService;
    @Autowired
    private GvCodeHelper gcCodeHelper;
    @Autowired
    private GcCancelSalesMapper gcCancelSalesMapper;
    @Autowired
    private OutletService outletService;

    @Override
    public IssueHandlingTypeEnum getIssueHandlingType() {
        return IssueHandlingTypeEnum.GC_CANCEL_SALES;
    }

    @Override
    public void execute(final Collection<IssueHandlingDetails> issueHandlingDetailsList) {
        List<String> cardNumbers = summarizeCompletedIssue(issueHandlingDetailsList);
        List<GiftCardEntity> giftCardEntities = this.gcService.queryByCardNumberList(null, cardNumbers);
        int manageSize = 500;
        List<IssueHandlingDetails> list = (List<IssueHandlingDetails>) issueHandlingDetailsList;
        final Map<String, GiftCardEntity> giftCardEntityMap = giftCardEntities
                .stream().collect(Collectors.toMap(GiftCardEntity::getCardNumber, Function.identity(), (v1, v2) -> v2));
        if (issueHandlingDetailsList.size() > manageSize) {
            int count = issueHandlingDetailsList.size() / manageSize + (issueHandlingDetailsList.size() % manageSize == 0 ? 0 : 1);
            List<IssueHandlingDetails> issueHandlingDetails = new ArrayList<>(issueHandlingDetailsList);
            for (int i = 0; i < count; i++) {

                int fromIndex = i * manageSize;
                int toIndex = (i + 1) * manageSize;
                List<IssueHandlingDetails> saveData = issueHandlingDetails.subList(fromIndex, Math.min(toIndex, issueHandlingDetails.size()));

                ThreadPoolCenter.commonThreadPoolExecute(() -> storeCancelSales(new ArrayList<>(saveData), giftCardEntityMap));
            }
        } else {
            storeCancelSales(list, giftCardEntityMap);
        }
    }

    public void storeCancelSales(List<IssueHandlingDetails> details, final Map<String, GiftCardEntity> giftCardEntityMap) {
        Date createTime = new Date();

        List<GcSalesCancelEntity> collect = new ArrayList<>();
        details.forEach(x -> {
            GiftCardEntity giftCard = giftCardEntityMap.get(x.getVoucherCode());
            if (giftCard == null) {
                return;
            }
            GcSalesCancelEntity gcSalesCancelEntity = new GcSalesCancelEntity();
            gcSalesCancelEntity.setCancelCode(gcCodeHelper.generateTransactionCode());
            gcSalesCancelEntity.setCancelReason(x.getRemarks());
            gcSalesCancelEntity.setCardNumber(x.getVoucherCode());
            gcSalesCancelEntity.setCreateTime(createTime);
            gcSalesCancelEntity.setCancelTime(createTime);
            gcSalesCancelEntity.setCancelReason("cancel release");
            gcSalesCancelEntity.setDenomination(x.getDenomination());
            gcSalesCancelEntity.setCustomerCode(giftCard.getOwnerCustomer());
            gcSalesCancelEntity.setIssuerCode(giftCard.getIssuerCode());
            gcSalesCancelEntity.setOutletCode(giftCard.getSalesOutlet());
            OutletResponse outletByOutletName = null;
            if (StringUtils.isNotEmpty(x.getOutletName())) {
                outletByOutletName = outletService.getOutletByOutletName(x.getOutletName());
            }
            if (null != outletByOutletName) {
                gcSalesCancelEntity.setOutletCode(outletByOutletName.getOutletCode());
                gcSalesCancelEntity.setMerchantCode(outletByOutletName.getMerchantCode());
            }
            gcSalesCancelEntity.setCpgCode(giftCard.getCpgCode());
            gcSalesCancelEntity.setInvoiceNumber(x.getInvoiceNo());
            gcSalesCancelEntity.setApprovalCode(x.getApprovalCode());
            gcSalesCancelEntity.setUpdateTime(createTime);
            collect.add(gcSalesCancelEntity);
        });
        if (!collect.isEmpty()) {
            this.gcCancelSalesMapper.insertList(collect);
        }
    }
}