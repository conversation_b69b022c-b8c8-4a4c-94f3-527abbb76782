package com.gtech.gvcore.service.impl.issuehandle;

import com.gtech.commons.utils.BeanCopyUtils;
import com.gtech.gvcore.common.enums.IssueHandlingProcessStatusEnum;
import com.gtech.gvcore.common.enums.IssueHandlingTypeEnum;
import com.gtech.gvcore.dao.model.IssueHandling;
import com.gtech.gvcore.dao.model.IssueHandlingDetails;
import com.gtech.gvcore.dao.model.Outlet;
import com.gtech.gvcore.giftcard.application.service.GiftCardApplicationService;
import com.gtech.gvcore.giftcard.domain.model.*;
import com.gtech.gvcore.giftcard.domain.service.GcRedemptionDomainService;
import com.gtech.gvcore.giftcard.domain.service.GcSalesDomainService;
import com.gtech.gvcore.giftcard.infrastructure.entity.GiftCardEntity;
import com.gtech.gvcore.giftcard.infrastructure.mapper.GiftCardMapper;
import com.gtech.gvcore.service.IssueHandlerBaseService;
import com.gtech.gvcore.service.OutletService;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.tomcat.util.threads.ThreadPoolExecutor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Lazy;
import tk.mybatis.mapper.util.StringUtil;

import java.util.*;
import java.util.concurrent.LinkedBlockingDeque;
import java.util.concurrent.TimeUnit;
import java.util.function.Function;
import java.util.stream.Collectors;

public abstract class GcIssueHandlerValidateService implements IssueHandlerBaseService {

    protected static final ThreadPoolExecutor EXECUTOR = new ThreadPoolExecutor(8, 20, 100, TimeUnit.SECONDS,
            new LinkedBlockingDeque<>(1000), new ThreadPoolExecutor.CallerRunsPolicy());

    @Lazy
    @Autowired
    protected GiftCardApplicationService giftCardService;
    @Autowired
    protected GiftCardMapper giftCardMapper;

    @Autowired
    protected OutletService outletService;

    @Autowired
    protected GcSalesDomainService salesDomainService;

    @Autowired
    protected GcRedemptionDomainService redemptionDomainService;

    @Value("${gv.active.url:}")
    protected String activeUrl;

    public static Map<String, String> outletMapFinal = new HashMap<>();

    @Data
    public static class RecordInfo {
        private String cardNumber;
        private String invoiceNumber;
        private String approvalCode;

        public RecordInfo(String cardNumber, String invoiceNumber, String approvalCode) {
            this.cardNumber = cardNumber;
            this.invoiceNumber = invoiceNumber;
            this.approvalCode = approvalCode;
        }
    }

    public void checkIfExist(List<IssueHandlingDetails> details, IssueHandlingTypeEnum issueHandlingTypeEnum, String issuerCode) {

        if (CollectionUtils.isEmpty(details)) {
            return;
        }
        List<String> cardNumberList = details.stream().filter(x -> x.getVoucherCode() != null && !x.getVoucherCode().isEmpty()).map(IssueHandlingDetails::getVoucherCode)
                .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(cardNumberList)) {
            details.forEach(vo -> {
                vo.setResult("Card number is empty!");
                vo.setProcessStatus(IssueHandlingProcessStatusEnum.FAILED.code());
            });
            return;
        }
        List<String> newVoucherCodeList = details.stream().map(IssueHandlingDetails::getNewVoucherCode)
                .collect(Collectors.toList());
        cardNumberList.addAll(newVoucherCodeList);

        cardNumberList = cardNumberList.stream().filter(StringUtil::isNotEmpty).collect(Collectors.toList());
        List<GiftCardEntity> cardEntities = giftCardService.queryByCardNumberList(issuerCode, cardNumberList);
        if (CollectionUtils.isEmpty(cardEntities)) {
            details.forEach(vo -> {
                vo.setResult("Card number doesn't exist!");
                vo.setProcessStatus(IssueHandlingProcessStatusEnum.FAILED.code());
            });
            return;
        }
        Map<String, GiftCardEntity> cardEntityMap = cardEntities.stream().collect(Collectors.toMap(GiftCardEntity::getCardNumber, Function.identity()));
        for (IssueHandlingDetails detail : details) {
            String voucherCode = detail.getVoucherCode();
            if (StringUtil.isEmpty(voucherCode)) {
                detail.setResult("Card number is empty!");
                detail.setProcessStatus(IssueHandlingProcessStatusEnum.FAILED.code());
            } else if (!cardEntityMap.containsKey(voucherCode)) {
                detail.setResult("Card number doesn't exist!");
                detail.setProcessStatus(IssueHandlingProcessStatusEnum.FAILED.code());
            } else if (StringUtil.isNotEmpty(detail.getNewVoucherCode())
                    && !cardEntityMap.containsKey(detail.getNewVoucherCode())) {
                detail.setResult("New card number doesn't exist!");
                detail.setProcessStatus(IssueHandlingProcessStatusEnum.FAILED.code());
            } else if (issueHandlingTypeEnum != null) {
                GiftCardEntity giftCard = cardEntityMap.get(voucherCode);
                //检查卡券状态
                StringBuilder message = new StringBuilder();
                boolean flag = checkStatus(giftCard, issueHandlingTypeEnum, detail, message);

                if (!flag) {
                    detail.setResult(message.toString());
                    detail.setProcessStatus(IssueHandlingProcessStatusEnum.FAILED.code());
                } else {
                    detail.setProcessStatus(IssueHandlingProcessStatusEnum.SUCCESS.code());
                    detail.setResult("Success");
                }

            } else {
                detail.setProcessStatus(IssueHandlingProcessStatusEnum.SUCCESS.code());
                detail.setResult("Success");
            }

            //重新生成激活码类型，需要校验老激活码
            if (IssueHandlingTypeEnum.RESET_ACTIVATION.equals(issueHandlingTypeEnum) && detail.getProcessStatus().equals(IssueHandlingProcessStatusEnum.SUCCESS.code())) {
                if (StringUtil.isNotEmpty(detail.getOldActivationCode())) {
                    String oldActivationCode = detail.getOldActivationCode();
                    if (cardEntityMap.get(detail.getVoucherCode()).getActivationCode() != null &&
                            !cardEntityMap.get(detail.getVoucherCode()).getActivationCode().equals(oldActivationCode)) {
                        detail.setResult("Old activation code doesn't exist!");
                        detail.setProcessStatus(IssueHandlingProcessStatusEnum.FAILED.code());
                    }
                }
            }


        }
    }

    private boolean checkStatus(GiftCardEntity voucher, IssueHandlingTypeEnum issueHandlingTypeEnum, IssueHandlingDetails details, StringBuilder errMsg) {

        boolean flag = true;
        switch (issueHandlingTypeEnum) {
            case GC_CANCEL_SALES:
                if (!GcCardStatus.PURCHASED.name().equalsIgnoreCase(voucher.getStatus())) {
                    flag = false;
                    errMsg.append("Gift card status is incorrect.");
                    break;
                }
                if (voucher.getExpiryTime().compareTo(new Date()) < 0) {
                    //如果券的有效期小于当前时间，就是券过期
                    errMsg.append("The card is already expired, could not cancel sales.");
                    flag = false;
                    break;
                }
                if (GcMgtCardStatus.DESTROY.name().equalsIgnoreCase(voucher.getManagementStatus())) {
                    flag = false;
                    errMsg.append("Gift card status is incorrect.");
                    break;
                }
                break;
            case GC_CANCEL_REDEEM:
                if (GcMgtCardStatus.DESTROY.name().equalsIgnoreCase(voucher.getManagementStatus())) {
                    flag = false;
                    errMsg.append("Gift card status is incorrect.");
                    break;
                }
                if (voucher.getExpiryTime().compareTo(new Date()) < 0) {
                    //如果券的有效期小于当前时间，就是券过期
                    errMsg.append("The card is already expired, could not cancel redeem.");
                    flag = false;
                    break;
                }
                break;
            case GC_BULK_REDEEM:
                if (details.getAmount().compareTo(voucher.getBalance()) > 0) {
                    flag = false;
                    errMsg.append("The balance on the gift card is insufficient.");
                    break;
                }
                if (!GcCardStatus.ACTIVATED.name().equals(voucher.getStatus())) {
                    flag = false;
                    errMsg.append("The card is not activated yet, could not redeem.");
                    break;
                }
                if (!GcMgtCardStatus.ENABLE.name().equals(voucher.getManagementStatus())) {
                    flag = false;
                    errMsg.append("Gift card status is incorrect.");
                    break;
                }
                if (voucher.getExpiryTime().compareTo(new Date()) < 0) {
                    //如果券的有效期小于当前时间，就是券过期
                    errMsg.append("The card is already expired, could not redeem.");
                    flag = false;
                    break;
                }
                break;
            case GC_BULK_REACTIVATE:
                if (!GcMgtCardStatus.DISABLE.name().equals(voucher.getManagementStatus())) {
                    flag = false;
                    errMsg.append("Gift card status is incorrect.");
                    break;
                }
                break;
            case GC_BULK_DEACTIVATE:
                if (!GcMgtCardStatus.ENABLE.name().equalsIgnoreCase(voucher.getManagementStatus())) {
                    flag = false;
                    errMsg.append("Gift card status is incorrect.");
                    break;
                }
                break;
            case GC_CHANGE_EXPIRY:
                if (!GcCardStatus.PURCHASED.name().equalsIgnoreCase(voucher.getStatus())) {
                    flag = false;
                    errMsg.append("Gift card status is incorrect.");
                    break;
                }
                if (!GcMgtCardStatus.ENABLE.name().equalsIgnoreCase(voucher.getManagementStatus())) {
                    flag = false;
                    errMsg.append("Gift card status is incorrect.");
                    break;
                }
                if (voucher.getExpiryTime().compareTo(new Date()) < 0) {
                    //如果券的有效期小于当前时间，就是券过期
                    errMsg.append("Gift card has expired.");
                    flag = false;
                    break;
                }
                if (voucher.getActivationExtensionCount() > 0) {
                    flag = false;
                    errMsg.append("The gift card has already  been extended, could not extend.");
                    break;
                }
                try {
                    BeanCopyUtils.jsonCopyBean(voucher, GiftCard.class).extendExpiryTime();
                } catch (Exception e) {
                    errMsg.append("The gift card status is not [Activation Period Ended], could not extend.");
                    flag = false;
                    break;
                }
            case GC_RESET_ACTIVATION:
                if (!GcCardStatus.PURCHASED.name().equalsIgnoreCase(voucher.getStatus())) {
                    flag = false;
                    errMsg.append("Gift card status is incorrect.");
                    break;
                }
                break;
            default:
                break;
        }
        return flag;
    }


    protected <T> void checkInvoiceNumberAndApprovalCode(
            List<IssueHandlingDetails> details,
            IssueHandlingTypeEnum issueHandlingTypeEnum) {
        if (CollectionUtils.isEmpty(details)) {
            return;
        }

        // Common check for invoice number and approval code
        List<String> invoiceNoList = details.stream()
                .map(IssueHandlingDetails::getInvoiceNo)
                .filter(StringUtils::isNotBlank)
                .collect(Collectors.toList());

        List<String> approvalCodeList = details.stream()
                .map(IssueHandlingDetails::getApprovalCode)
                .filter(StringUtils::isNotBlank)
                .collect(Collectors.toList());

        String checkResult = null;
        if (CollectionUtils.isEmpty(invoiceNoList)) {
            checkResult = "Invoice number is empty!";
        }
        if (CollectionUtils.isEmpty(approvalCodeList)) {
            checkResult = "Approval code is empty!";
        }
        if (checkResult != null) {
            String finalResult = checkResult;
            details.forEach(vo -> {
                if (vo.getProcessStatus().equals(IssueHandlingProcessStatusEnum.FAILED.code())) {
                    vo.setResult(vo.getResult() + finalResult);
                } else {
                    vo.setResult(finalResult);
                }
                vo.setProcessStatus(IssueHandlingProcessStatusEnum.FAILED.code());
            });
            return;
        }

        List<String> cardNumbers = details.stream()
                .map(IssueHandlingDetails::getVoucherCode)
                .collect(Collectors.toList());
        List<RecordInfo> records = new ArrayList<>();
        List<GcSalesRecord> entity;
        switch (issueHandlingTypeEnum) {
            case GC_BULK_REDEEM:
                entity = salesDomainService.getSalesRecordByCardNumber(cardNumbers);
                records = BeanCopyUtils.jsonCopyList(entity, RecordInfo.class);
                break;
            case GC_CANCEL_REDEEM:
                records = details.stream().map(x -> {
                    GcRedemptionRecord gcRedemptionRecord = redemptionDomainService.getRedemption(x.getVoucherCode(), x.getInvoiceNo());
                    return new RecordInfo(x.getVoucherCode(), x.getInvoiceNo(), gcRedemptionRecord.getApprovalCode());
                }).collect(Collectors.toList());
                break;
            case GC_CANCEL_SALES:
                entity = salesDomainService.getSalesRecordByCardNumber(cardNumbers);
                records = BeanCopyUtils.jsonCopyList(entity, RecordInfo.class);
                break;
            case GC_CHANGE_EXPIRY:
                entity = salesDomainService.getSalesRecordByCardNumber(cardNumbers);
                records = BeanCopyUtils.jsonCopyList(entity, RecordInfo.class);
                break;
        }

        if (records.isEmpty()) {
            details.forEach(x -> {
                String result = "Card number doesn't exist!";
                x.setResult(x.getResult() + result);
                x.setProcessStatus(IssueHandlingProcessStatusEnum.FAILED.code());
            });
            return;
        }

        Map<String, RecordInfo> cardNumberMap = records.stream()
                .collect(Collectors.toMap(info -> info.cardNumber, info -> info, (k1, k2) -> k1));

        details.forEach(detail ->

        {
            String voucherCode = detail.getVoucherCode();
            String approvalCode = detail.getApprovalCode();
            String invoiceNo = detail.getInvoiceNo();
            String result = null;

            RecordInfo recordInfo = cardNumberMap.get(voucherCode);
            if (recordInfo == null || !recordInfo.invoiceNumber.equals(invoiceNo)) {
                result = "Card number doesn't match the invoice number!";
            }
            if (recordInfo == null || (StringUtils.isNotBlank(recordInfo.approvalCode) && !recordInfo.approvalCode.equals(approvalCode))) {
                result = "Card number doesn't match the approval code!";
            }

            if (result != null) {
                if (detail.getProcessStatus().equals(IssueHandlingProcessStatusEnum.FAILED.code())) {
                    detail.setResult(detail.getResult() + result);
                } else {
                    detail.setResult(result);
                }
                detail.setProcessStatus(IssueHandlingProcessStatusEnum.FAILED.code());
            }
        });
    }

    public void checkOutletName(List<IssueHandlingDetails> details) {
        if (CollectionUtils.isEmpty(details)) {
            return;
        }
        List<String> outletNameList = details.stream().filter(x -> x.getOutletName() != null && !x.getOutletName().isEmpty()).map(IssueHandlingDetails::getOutletName).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(outletNameList)) {
            details.forEach(vo -> {
                String result = "Merchant outlet is empty!";
                if (vo.getProcessStatus().equals(IssueHandlingProcessStatusEnum.FAILED.code())) {
                    vo.setResult(vo.getResult() + result);
                } else {
                    vo.setResult(result);
                }
                vo.setProcessStatus(IssueHandlingProcessStatusEnum.FAILED.code());
            });
            return;
        }
        List<Outlet> outletList = outletService.queryOutletByNames(outletNameList);
        Map<String, String> outletMap = outletList.stream().collect(Collectors.toMap(Outlet::getOutletName, Outlet::getOutletCode, (k1, k2) -> k1));
        outletMapFinal = outletMap;
        details.forEach(detail -> {
            String outletName = detail.getOutletName();
            String result = "";
            if (StringUtil.isEmpty(outletName)) {
                result = "Merchant outlet is empty!";
            } else if (outletMap.isEmpty() || StringUtil.isEmpty(outletMap.get(outletName))) {
                result = "Merchant outlet doesn't exist!";
            }
            if (!StringUtil.isEmpty(result)) {
                if (detail.getProcessStatus().equals(IssueHandlingProcessStatusEnum.FAILED.code())) {
                    detail.setResult(detail.getResult() + result);
                } else {
                    detail.setResult(result);
                }
                detail.setProcessStatus(IssueHandlingProcessStatusEnum.FAILED.code());
            }
        });
    }

    @Override
    public void afterExecute(IssueHandling issueHandling) {
    }

}
