package com.gtech.gvcore.service.impl;

import java.io.IOException;
import java.util.ArrayList;
import java.util.Collections;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Lazy;
import org.springframework.core.env.Environment;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.gtech.basic.idm.common.enums.ResourceTypeEnum;
import com.gtech.basic.idm.dao.entity.ResourceEntity;
import com.gtech.basic.idm.dao.entity.RoleResourceMappingEntity;
import com.gtech.basic.idm.dao.mapper.IResourceMapper;
import com.gtech.basic.idm.dao.mapper.IRoleResourceMappingMapper;
import com.gtech.commons.exception.GTechBaseException;
import com.gtech.commons.utils.BeanCopyUtils;
import com.gtech.commons.utils.StringUtil;
import com.gtech.gvcore.common.constants.GvcoreConstants;
import com.gtech.gvcore.common.enums.FlowEnum;
import com.gtech.gvcore.common.enums.FlowNodeEnum;
import com.gtech.gvcore.common.enums.FlowNoticeTypeEnum;
import com.gtech.gvcore.common.enums.MessageEnventEnum;
import com.gtech.gvcore.common.enums.ResultErrorCodeEnum;
import com.gtech.gvcore.common.request.flow.DeleteFlowNoticeRequest;
import com.gtech.gvcore.common.request.flow.FlowNoticeRequest;
import com.gtech.gvcore.common.request.flow.GetFlowNoticeRequest;
import com.gtech.gvcore.common.request.flow.SendNoticeRequest;
import com.gtech.gvcore.common.response.flow.FlowNoticeResponse;
import com.gtech.gvcore.dao.mapper.FlowNoticeMapper;
import com.gtech.gvcore.dao.model.CustomerOrder;
import com.gtech.gvcore.dao.model.FlowNotice;
import com.gtech.gvcore.dao.model.IssueHandling;
import com.gtech.gvcore.dao.model.UserAccount;
import com.gtech.gvcore.dao.model.VoucherRequest;
import com.gtech.gvcore.service.CustomerOrderService;
import com.gtech.gvcore.service.FlowNoticeService;
import com.gtech.gvcore.service.GvUserAccountService;
import com.gtech.gvcore.service.IssueHandlingService;
import com.gtech.gvcore.service.VoucherRequestService;

import lombok.extern.slf4j.Slf4j;
import tk.mybatis.mapper.entity.Example;

@Service
@Slf4j
public class FlowNoticeServiceImpl implements FlowNoticeService {

	@Autowired
	private FlowNoticeMapper flowNoticeMapper;
	@Autowired
	private GvUserAccountService userAccountService;
	@Value("#{'${test.email.list:}'.split(',')}")
	private List<String> emailList;

	@Value("#{${gv.issuer.warehouse}}")
	private Map<String, String> WHMap;

	@Value("#{${gv.issuer.businesswarehouse}}")
	private Map<String, String> HOMap;

	@Value("${gv.outlet.warehouse.MV01:}")
	private String mv01;
	@Value("${gv.outlet.warehouse.MV03:}")
	private String mv03;
	@Value("${gv.outlet.warehouse.MV04:}")
	private String mv04;

	@Autowired
	private IResourceMapper resourceMapper;
	
	@Autowired
	private IRoleResourceMappingMapper roleResourceMapper;
	
	@Autowired
	private MessageComponent messageComponent;

	@Lazy
	@Autowired
	private CustomerOrderService customerOrderService;
	@Autowired
	private VoucherRequestService voucherRequestService;
	@Autowired
	private IssueHandlingService issueHandlingService;

	@Value("${spring.profiles.active:}")
	private String profile;

	@Override
	@Transactional
	public String saveFlowNotice(FlowNoticeRequest request) {
		DeleteFlowNoticeRequest deleteRequest = new DeleteFlowNoticeRequest();
		deleteRequest.setFlowCode(request.getFlowCode());
		deleteRequest.setFlowNodeCode(request.getFlowNodeCode());
		deleteFlowNotice(deleteRequest);
		List<FlowNotice> noticeList = new ArrayList<>();
		if (CollectionUtils.isNotEmpty(request.getNoticeCodeList())) {
			request.getNoticeCodeList().forEach(noticeCode -> {
				FlowNotice flowNotice = makeNotice(request, noticeCode, FlowNoticeTypeEnum.SEND_FLAG.getCode());
				noticeList.add(flowNotice);
			});
		}
		if (CollectionUtils.isNotEmpty(request.getNoticeCodeCCList())) {
			request.getNoticeCodeCCList().forEach(noticeCode -> {
				FlowNotice flowNotice = makeNotice(request, noticeCode, FlowNoticeTypeEnum.CC_FLAG.getCode());
				noticeList.add(flowNotice);
			});
		}
		flowNoticeMapper.insertList(noticeList);
		return request.getFlowNodeCode();
	}

	private FlowNotice makeNotice(FlowNoticeRequest request, String noticeCode, Integer noticeType) {
		FlowNotice flowNotice = new FlowNotice();
		flowNotice.setFlowCode(request.getFlowCode());
		flowNotice.setFlowNodeCode(request.getFlowNodeCode());
		flowNotice.setFlowNoticeType(noticeType);
		flowNotice.setFlowNoticeCode(noticeCode);
		flowNotice.setFlowNoticeCodeType(request.getFlowNoticeCodeType());
		return flowNotice;
	}

	@Override
	public void deleteFlowNotice(DeleteFlowNoticeRequest request) {
		FlowNotice flowNotice = BeanCopyUtils.jsonCopyBean(request, FlowNotice.class);
		flowNoticeMapper.delete(flowNotice);
	}

	@SuppressWarnings("unchecked")
	@Override
	public FlowNoticeResponse getFlowNotice(GetFlowNoticeRequest request) {
		Map<String, Object> map = JSON.parseObject(JSON.toJSONString(request), Map.class);
		FlowNoticeResponse flowNoticeResponse = new FlowNoticeResponse();
		flowNoticeResponse.setFlowCode(request.getFlowCode());
		flowNoticeResponse.setFlowNodeCode(request.getFlowNodeCode());
		List<FlowNotice> flowList = flowNoticeMapper.query(map);
		if (CollectionUtils.isEmpty(flowList)) {
			flowNoticeResponse.setNoticeCodeList(
					flowList.stream().filter(vo -> FlowNoticeTypeEnum.SEND_FLAG.getCode() == vo.getFlowNoticeType())
							.map(FlowNotice::getFlowNoticeCode).collect(Collectors.toList()));
			flowNoticeResponse.setNoticeCodeCCList(
					flowList.stream().filter(vo -> FlowNoticeTypeEnum.CC_FLAG.getCode() == vo.getFlowNoticeType())
							.map(FlowNotice::getFlowNoticeCode).collect(Collectors.toList()));
		}
		return flowNoticeResponse;
	}

	@Override
	public void send(SendNoticeRequest request) {
		if (StringUtil.isEmpty(request.getFlowCode()) || StringUtil.isEmpty(request.getFlowNodeCode())) {
			throw new GTechBaseException(ResultErrorCodeEnum.PARAMTER_NULL_ERROR.code(),
					ResultErrorCodeEnum.PARAMTER_NULL_ERROR.desc() + ":flowCode or flowNodeCode");
		}
		// 在测试环境直接执行，在生产环境使用新线程
		if (StringUtil.isNotEmpty(profile) && profile.equals("dev")) {
			try {
				sendToNotice(request);
			} catch (IOException e) {
				log.error(e.getMessage(), e);
			}
		} else {
			// redis or mq
			new Thread(() -> {
				try {
					sendToNotice(request);
				} catch (IOException e) {
					log.error(e.getMessage(), e);
				}
			}).start();
		}

	}

	public void sendToNotice(SendNoticeRequest request) throws IOException {
		log.info("send json :{}", JSON.toJSONString(request));
		List<UserAccount> userAccountList = queryUserByFlowNotice(request.getFlowCode(), request.getFlowNodeCode(), request.getBusinessCode());
		List<String> emails = new ArrayList<>();
		StringBuilder userCodeList = new StringBuilder();
		for (UserAccount userAccount : userAccountList) {
			userCodeList.append(userAccount.getUserCode());
			userCodeList.append(",");
			if (!StringUtil.isEmpty(userAccount.getEmail())) {
				emails.add(userAccount.getEmail());
			}
		}
		Set<String> sendEmails = new HashSet<>();
//		sendEmails.addAll(emails);
		String enventCode = getEnventCode(request);
		if (StringUtil.isEmpty(enventCode)) {
			log.info("Doesn't match enventCode, flowCode: {}, flowNodeCode: {}", request.getFlowCode(), request.getFlowNodeCode());
			return;
		}
		if (CollectionUtils.isNotEmpty(request.getEmails())) {
			sendEmails.addAll(request.getEmails());
		}
		sendEmails.addAll(emailList);
		sendEmails = sendEmails.stream().filter(StringUtil::isNotEmpty).collect(Collectors.toSet());
		if (CollectionUtils.isEmpty(sendEmails)) {
			log.info("send -> emails empty");
			return;
		}
		JSONObject messageRequest = new JSONObject();
		messageRequest.put("eventCode", enventCode);
		JSONObject param = new JSONObject();
		param.put("emails", sendEmails);
		param.put("code", request.getBusinessCode());
		if (request.getExtendParams() != null) {
			param.putAll(request.getExtendParams());
		}
		if (userCodeList.length() > 0) {
			param.put("userCodeList", userCodeList.substring(0, userCodeList.length() - 1));
		}
		messageRequest.put("param", param);
		messageComponent.send(messageRequest);
	}

	//TODO 待更改

	public List<UserAccount> queryUserByFlowNotice(String flowCode, String flowNode, String businessCode) {

		String permissionCode = "";
		if (FlowEnum.SALES_VOUCHER_FLOW.getCode().equals(flowCode)) {
			VoucherRequest voucherRequest = voucherRequestService.queryByVoucherRequestCode(businessCode);
			if (voucherRequest == null) {
				throw new GTechBaseException(ResultErrorCodeEnum.NO_DATA_FOUND.code(), ResultErrorCodeEnum.NO_DATA_FOUND.desc());
			}
			permissionCode = voucherRequest.getPermissionCode()+","+voucherRequest.getReceiverCode();
		} else if (FlowEnum.CUSTOMER_ORDER_FLOW.getCode().equals(flowCode)) {
			CustomerOrder customerOrder = customerOrderService.queryByCustomerOrderCode(businessCode);
			if (customerOrder == null) {
				throw new GTechBaseException(ResultErrorCodeEnum.NO_DATA_FOUND.code(), ResultErrorCodeEnum.NO_DATA_FOUND.desc());
			}
			permissionCode = HOMap.getOrDefault(customerOrder.getIssuerCode(),customerOrder.getIssuerCode())
					+","+customerOrder.getOutletCode();
		} else if (flowCode.startsWith(FlowEnum.ISSUE_HANDLING.getCode())) {
			IssueHandling issueHandling = issueHandlingService.queryByIssueHandlingCode(businessCode);
			if (issueHandling == null) {
				throw new GTechBaseException(ResultErrorCodeEnum.NO_DATA_FOUND.code(), ResultErrorCodeEnum.NO_DATA_FOUND.desc());
			}
			permissionCode = issueHandling.getIssuerCode();
		}

		FlowNotice queryFlowNotice = new FlowNotice();
		queryFlowNotice.setFlowCode(flowCode);
		queryFlowNotice.setFlowNodeCode(flowNode);
		queryFlowNotice.setStatus(GvcoreConstants.STATUS_ENABLE);
		List<FlowNotice> flowNoticelist = flowNoticeMapper.select(queryFlowNotice);
		if (CollectionUtils.isEmpty(flowNoticelist)) {
			log.info("flow notice empty:flow code:【{}】,flow node code:【{}】", flowCode, flowNode);
			return Collections.emptyList();
		}
		
		// default by resource
		List<String> resourceList = flowNoticelist.stream().map(FlowNotice::getFlowNoticeCode)
				.collect(Collectors.toList());
		List<String> roleCodeList = getRoleCodeListByResource(resourceList);
		List<UserAccount> userAccountList = userAccountService.queryUserByRolesAndDataPermissions(roleCodeList, permissionCode);
		if (CollectionUtils.isEmpty(userAccountList)) {
			log.info("user account empty, role code list: {}", JSON.toJSONString(roleCodeList));
			return Collections.emptyList();
		}
		return userAccountList;
	}



	/*public List<UserAccount> queryUserByFlowNotice(String flowCode, String flowNode, String businessCode) {

		String permissionCode = "";
		if (FlowEnum.SALES_VOUCHER_FLOW.getCode().equals(flowCode)) {
			VoucherRequest voucherRequest = voucherRequestService.queryByVoucherRequestCode(businessCode);
			if (voucherRequest == null) {
				throw new GTechBaseException(ResultErrorCodeEnum.NO_DATA_FOUND.code(), ResultErrorCodeEnum.NO_DATA_FOUND.desc());
			}
			permissionCode = voucherRequest.getIssuerCode();
		} else if (FlowEnum.CUSTOMER_ORDER_FLOW.getCode().equals(flowCode)) {
			CustomerOrder customerOrder = customerOrderService.queryByCustomerOrderCode(businessCode);
			if (customerOrder == null) {
				throw new GTechBaseException(ResultErrorCodeEnum.NO_DATA_FOUND.code(), ResultErrorCodeEnum.NO_DATA_FOUND.desc());
			}
			permissionCode = customerOrder.getIssuerCode();
		} else if (flowCode.startsWith(FlowEnum.ISSUE_HANDLING.getCode())) {
			IssueHandling issueHandling = issueHandlingService.queryByIssueHandlingCode(businessCode);
			if (issueHandling == null) {
				throw new GTechBaseException(ResultErrorCodeEnum.NO_DATA_FOUND.code(), ResultErrorCodeEnum.NO_DATA_FOUND.desc());
			}
			permissionCode = issueHandling.getIssuerCode();
		}

		FlowNotice queryFlowNotice = new FlowNotice();
		queryFlowNotice.setFlowCode(flowCode);
		queryFlowNotice.setFlowNodeCode(flowNode);
		queryFlowNotice.setStatus(GvcoreConstants.STATUS_ENABLE);
		List<FlowNotice> flowNoticelist = flowNoticeMapper.select(queryFlowNotice);
		if (CollectionUtils.isEmpty(flowNoticelist)) {
			log.info("flow notice empty:flow code:【{}】,flow node code:【{}】", flowCode, flowNode);
			return Collections.emptyList();
		}

		// default by resource
		List<String> resourceList = flowNoticelist.stream().map(FlowNotice::getFlowNoticeCode)
				.collect(Collectors.toList());
		List<String> roleCodeList = getRoleCodeListByResource(resourceList);
		List<UserAccount> userAccountList = userAccountService.queryUserByRolesAndDataPermissions(roleCodeList, permissionCode);
		if (CollectionUtils.isEmpty(userAccountList)) {
			log.info("user account empty, role code list: {}", JSON.toJSONString(roleCodeList));
			return Collections.emptyList();
		}
		return userAccountList;
	}*/

	private List<String> getRoleCodeListByResource(List<String> resourceList) {
		
		Example example = new Example(ResourceEntity.class);
		example.createCriteria().andIn(ResourceEntity.C_RESOURCE_CODE, resourceList);
		List<ResourceEntity> list = resourceMapper.selectByCondition(example);
		if (CollectionUtils.isEmpty(list)) {
			return Collections.emptyList();
		}
		List<String> resourceCodeList = new ArrayList<>();
		for (ResourceEntity vo : list) {
			Integer resourceType = vo.getResourceType();
			if (ResourceTypeEnum.BUTTON.number() == resourceType) {
				resourceCodeList.add(vo.getResourceCode());
			} else if (ResourceTypeEnum.PAGE.number() == resourceType) {
				resourceCodeList.add(vo.getResourceParent());
			}
		}
		return queryRolesByResource(resourceCodeList);
	}

	private List<String> queryRolesByResource(List<String> resourceCodeList) {
		if (CollectionUtils.isEmpty(resourceCodeList)) {
			return Collections.emptyList();
		}
		Example example = new Example(RoleResourceMappingEntity.class);
		example.createCriteria().andIn(RoleResourceMappingEntity.C_RESOURCE_CODE, resourceCodeList);
		List<RoleResourceMappingEntity> list = roleResourceMapper.selectByCondition(example);
		if (CollectionUtils.isEmpty(resourceCodeList)) {
			return Collections.emptyList();
		}
		return list.stream().map(RoleResourceMappingEntity::getRoleCode).distinct().collect(Collectors.toList());
	}

	private String getEnventCode(SendNoticeRequest request) {
		String flowCode = request.getFlowCode();
		String flowNodeCode = request.getFlowNodeCode();
		if (FlowEnum.SALES_VOUCHER_FLOW.getCode().equals(flowCode)) {
			return getSalesEnvent(flowNodeCode);
		} else if (FlowEnum.TRANSFER_ORDER_FLOW.getCode().equals(flowCode) || FlowEnum.RETURN_VOUCHER_FLOW.getCode().equals(flowCode) ) {
			return getTransferReturnEnvent(flowNodeCode);
		} else if (FlowEnum.CUSTOMER_ORDER_FLOW.getCode().equals(flowCode)) {
			return getCustomerEnvent(flowNodeCode);
		} else if (flowCode.startsWith(FlowEnum.ISSUE_HANDLING.getCode())) {
			return getIssueHandlingEnvent(flowNodeCode);
		} 
		return null;
	}
	
	private String getIssueHandlingEnvent(String flowNodeCode) {
		if (FlowNodeEnum.SUBMIT.getCode().equals(flowNodeCode)) {
			return MessageEnventEnum.SUBMIT_ISSUE_HANDLING.getCode();
		} else if (FlowNodeEnum.APPROVE.getCode().equals(flowNodeCode) || FlowNodeEnum.REJECTED.getCode().equals(flowNodeCode)) {
			return MessageEnventEnum.APPROVE_ISSUE_HANDLING.getCode();
		} else if (FlowNodeEnum.EXECUTE.getCode().equals(flowNodeCode)) {
			return MessageEnventEnum.EXECUTE_ISSUE_HANDLING.getCode();
		}
		return null;
	}
	
	private String getCustomerEnvent(String flowNodeCode) {
		if (FlowNodeEnum.CUSTOMER_CREATED.getCode().equals(flowNodeCode)) {
			return MessageEnventEnum.CUSTOMER_ORDER_CREATE.getCode();
		} else if (FlowNodeEnum.CREATED.getCode().equals(flowNodeCode)) {
			return MessageEnventEnum.CUSTOMER_ORDER_CREATE_BY_OP.getCode();
		} else if (FlowNodeEnum.SUBMIT.getCode().equals(flowNodeCode)) {
			return MessageEnventEnum.SUBMIT_CUSTOMER_ORDER.getCode();
		} else if (FlowNodeEnum.APPROVE.getCode().equals(flowNodeCode)) {
			return MessageEnventEnum.APPROVE_CUSTOMER_ORDER.getCode();
		} else if (FlowNodeEnum.REJECTED.getCode().equals(flowNodeCode)) {
			return MessageEnventEnum.REJECT_CUSTOMER_ORDER.getCode();
		} else if (FlowNodeEnum.ISSUANCE.getCode().equals(flowNodeCode)) {
			return MessageEnventEnum.ISSUANCE_CUSTOMER_ORDER.getCode();
		} else if (FlowNodeEnum.RELEASE.getCode().equals(flowNodeCode)) {
			return MessageEnventEnum.RELEASE_CUSTOMER_ORDER.getCode();
		} else if (FlowNodeEnum.RECEIVE.getCode().equals(flowNodeCode)) {
			return MessageEnventEnum.RECEIVE_CUSTOMER_ORDER.getCode();
		} else if (FlowNodeEnum.DELIVER.getCode().equals(flowNodeCode)) {
			return MessageEnventEnum.DELIVER_CUSTOMER_ORDER.getCode();
		} else if (FlowNodeEnum.COMPLETED.getCode().equals(flowNodeCode)) {
			return MessageEnventEnum.EGV_CUSTOMER_ORDER_COMPLETED.getCode();
		}
		return null;
	}


	private String getTransferReturnEnvent(String flowNodeCode) {
		if (FlowNodeEnum.CREATED.getCode().equals(flowNodeCode)) {
			return MessageEnventEnum.CREATE_VOUCHER_RETURN_TRANSFER.getCode();
		} else if ( FlowNodeEnum.APPROVE.getCode().equals(flowNodeCode)) {
			return MessageEnventEnum.APPROVE_VOUCHER_RETURN_TRANSFER.getCode();
		} else if  (FlowNodeEnum.REJECTED.getCode().equals(flowNodeCode)) {
			return MessageEnventEnum.REJECT_VOUCHER_RETURN_TRANSFER.getCode();
		} else if (FlowNodeEnum.ALLOCATION.getCode().equals(flowNodeCode)) {
			return MessageEnventEnum.RETURN_TRANSFER_VOUCHER_REQUEST.getCode();
		} else if (FlowNodeEnum.RECEIVE.getCode().equals(flowNodeCode)) {
			return MessageEnventEnum.RECEIVE_VOUCHER_RETURN_TRANSFER.getCode();
		}
		return null;
	}

	private String getSalesEnvent(String flowNodeCode) {
		if (FlowNodeEnum.CREATED.getCode().equals(flowNodeCode)) {
			return MessageEnventEnum.CREATE_VOUCHER_REQUEST.getCode();
		} else if (FlowNodeEnum.APPROVE.getCode().equals(flowNodeCode)) {
			return MessageEnventEnum.APPROVE_VOUCHER_REQUEST.getCode();
		} else if (FlowNodeEnum.REJECTED.getCode().equals(flowNodeCode)) {
			return MessageEnventEnum.REJECT_VOUCHER_REQUEST.getCode();
		} else if (FlowNodeEnum.ALLOCATION.getCode().equals(flowNodeCode)) {
			return MessageEnventEnum.ALLOCATE_VOUCHER_REQUEST.getCode();
		} else if (FlowNodeEnum.RECEIVE.getCode().equals(flowNodeCode)) {
			return MessageEnventEnum.RECEIVE_VOUCHER_REQUEST.getCode();
		}
		return null;
	}
}
