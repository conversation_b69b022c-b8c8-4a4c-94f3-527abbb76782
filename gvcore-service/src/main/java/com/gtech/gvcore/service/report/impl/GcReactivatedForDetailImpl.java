package com.gtech.gvcore.service.report.impl;

import com.gtech.commons.utils.ConvertUtils;
import com.gtech.commons.utils.DateUtil;
import com.gtech.gvcore.common.enums.IssueHandlingTypeEnum;
import com.gtech.gvcore.common.enums.ReportExportTypeEnum;
import com.gtech.gvcore.common.enums.TransactionTypeEnum;
import com.gtech.gvcore.common.request.orderreport.CreateReportRequest;
import com.gtech.gvcore.dao.model.Cpg;
import com.gtech.gvcore.dao.model.Customer;
import com.gtech.gvcore.dao.model.Merchant;
import com.gtech.gvcore.dao.model.Outlet;
import com.gtech.gvcore.service.IssueHandlingDetailsService;
import com.gtech.gvcore.service.report.BusinessReport;
import com.gtech.gvcore.service.report.SingleReport;
import com.gtech.gvcore.service.report.base.ReportSupport;
import com.gtech.gvcore.service.report.extend.joindate.JoinDataMap;
import com.gtech.gvcore.service.report.extend.poll.PollPageHelper;
import com.gtech.gvcore.service.report.impl.bean.GcReactivatedForDetailBean;
import com.gtech.gvcore.service.report.impl.bo.GcReactivatedBo;
import com.gtech.gvcore.service.report.impl.param.GcReactivatedQueryData;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.Collection;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * @Author: gao.yuhua
 * @Date: 2022/5/11 13:57
 * @Description: Gift Card Reactivated (Block) Report Detail Implementation
 */
@Service
public class GcReactivatedForDetailImpl extends ReportSupport
        implements BusinessReport<GcReactivatedQueryData, GcReactivatedForDetailBean>, SingleReport {

    @Autowired
    protected IssueHandlingDetailsService issueHandlingDetailsService;

    @Override
    public GcReactivatedQueryData builderQueryParam(CreateReportRequest reportParam) {

        GcReactivatedQueryData queryData = new GcReactivatedQueryData();

        queryData.setIssuerCodeList(StringUtils.isNotBlank(reportParam.getIssuerCode()) ? Collections.singletonList(reportParam.getIssuerCode()) : null);
        queryData.setMerchantCodeList(reportParam.getMerchantCodes());
        queryData.setOutletCodeList(reportParam.getOutletCodes());

        queryData.setTransactionDateStart(reportParam.getTransactionDateStart());
        queryData.setTransactionDateEnd(reportParam.getTransactionDateEnd());

        queryData.setCpgCodeList(reportParam.getCpgCodes());
        queryData.setInvoiceNumber(reportParam.getInvoiceNo());

        queryData.setVoucherEffectiveDateStart(reportParam.getVoucherEffectiveDateStart());
        queryData.setVoucherEffectiveDateEnd(reportParam.getVoucherEffectiveDateEnd());

        queryData.setTransactionType(TransactionTypeEnum.GIFT_CARD_REACTIVATE.getCode());

        return queryData;
    }

    @Override
    public List<GcReactivatedForDetailBean> getExportData(GcReactivatedQueryData queryData) {

        //find
        final Collection<GcReactivatedBo> list =
                Optional.of(PollPageHelper.pollGroupNewTransactionByCodeSelect(gcReportBusinessMapper::selectGcReactivated, queryData))
                        .orElse(Collections.emptyList());

        //empty
        if (CollectionUtils.isEmpty(list)) return Collections.emptyList();

        //init map
        final JoinDataMap<Outlet> outletMap = super.getMapByCode(list, GcReactivatedBo::getOutletCode, Outlet.class);
        final JoinDataMap<Merchant> merchantMap = super.getMapByCode(list, GcReactivatedBo::getMerchantCode, Merchant.class);
        final JoinDataMap<Cpg> cpgMap = super.getMapByCode(list, GcReactivatedBo::getCpgCode, Cpg.class);
        final JoinDataMap<Customer> customerMap = super.getMapByCode(list, GcReactivatedBo::getOwnerCustomer, Customer.class);
        final Map<String, String> voucherMap = issueHandlingDetailsService.queryRemarkByVoucherCodeAndIssueType(list.stream().map(GcReactivatedBo::getVoucherCode)
                .distinct().collect(Collectors.toList()), IssueHandlingTypeEnum.GC_BULK_REACTIVATE);

        //convert result
        return list.stream()
                .map(e -> new GcReactivatedForDetailBean()
                        .setVoucherAmount(super.toAmount(ConvertUtils.toBigDecimal(e.getDenomination(), BigDecimal.ZERO)))
                        .setVoucherNumber(e.getVoucherCode())
                        .setTransactionDate(DateUtil.format(e.getTransactionDate(), "yyyy-MM-dd HH:mm:ss"))
                        .setInvoiceNumber(e.getInvoiceNumber())
                        .setReactivatedReason(StringUtils.isNotBlank(voucherMap.get(e.getVoucherCode())) ? voucherMap.get(e.getVoucherCode()) : e.getUnblockReason())
                        .setVpg(cpgMap.findValue(e.getCpgCode()).getCpgName())
                        .setMerchantOut(outletMap.findValue(e.getOutletCode()).getOutletName())
                        .setMerchant(merchantMap.findValue(e.getMerchantCode()).getMerchantName())
                        .setCustomerName(customerMap.findValue(e.getOwnerCustomer()).getCustomerName()))
                .collect(Collectors.toList());
    }

    @Override
    public ReportExportTypeEnum exportTypeEnum() {
        return ReportExportTypeEnum.GC_REACTIVATED_DETAILED;
    }
}
