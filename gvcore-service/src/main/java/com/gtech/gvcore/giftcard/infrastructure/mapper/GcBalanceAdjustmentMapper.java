package com.gtech.gvcore.giftcard.infrastructure.mapper;

import com.gtech.commons.dao.mapper.GTechBaseMapper;
import com.gtech.gvcore.giftcard.infrastructure.entity.GcBalanceAdjustmentEntity;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * 礼品卡余额调整记录数据访问接口
 */
@Mapper
public interface GcBalanceAdjustmentMapper extends GTechBaseMapper<GcBalanceAdjustmentEntity> {
    
    /**
     * 根据卡号查询余额调整记录
     */
    @Select("SELECT * FROM gc_balance_adjustment WHERE card_number = #{cardNumber}")
    List<GcBalanceAdjustmentEntity> selectByCardNumber(@Param("cardNumber") String cardNumber);
    
    /**
     * 根据调整类型查询余额调整记录
     */
    @Select("SELECT * FROM gc_balance_adjustment WHERE adjustment_type = #{adjustmentType}")
    List<GcBalanceAdjustmentEntity> selectByAdjustmentType(@Param("adjustmentType") String adjustmentType);
    
    /**
     * 根据调整人查询余额调整记录
     */
    @Select("SELECT * FROM gc_balance_adjustment WHERE adjusted_by = #{adjustedBy}")
    List<GcBalanceAdjustmentEntity> selectByAdjustedBy(@Param("adjustedBy") String adjustedBy);
} 