package com.gtech.gvcore.giftcard.application.service;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.gtech.basic.filecloud.api.model.Resp;
import com.gtech.basic.filecloud.commons.PagedData;
import com.gtech.basic.filecloud.commons.PagedDatas;
import com.gtech.basic.filecloud.exports.excel.spec.ExcelExportSpec;
import com.gtech.basic.filecloud.exports.management.FileExport;
import com.gtech.basic.filecloud.exports.management.FileExportManager;
import com.gtech.basic.filecloud.exports.management.FileExportResult;
import com.gtech.commons.exception.GTechBaseException;
import com.gtech.commons.page.PageData;
import com.gtech.commons.result.Result;
import com.gtech.commons.utils.BeanCopyUtils;
import com.gtech.commons.utils.CryptoUtils;
import com.gtech.commons.utils.DateUtil;
import com.gtech.commons.utils.StringUtil;
import com.gtech.gvcore.common.constants.GvcoreConstants;
import com.gtech.gvcore.common.enums.MessageEnventEnum;
import com.gtech.gvcore.common.enums.ResultErrorCodeEnum;
import com.gtech.gvcore.common.request.base.PageBean;
import com.gtech.gvcore.common.request.senddigitalvoucherexceltoemail.SendDigitalVoucherExcelToEmailRequest;
import com.gtech.gvcore.common.response.voucherbatch.VoucherBatchResponse;
import com.gtech.gvcore.dao.mapper.CustomerOrderEmailMapper;
import com.gtech.gvcore.dao.model.CustomerOrder;
import com.gtech.gvcore.dao.model.CustomerOrderEmail;
import com.gtech.gvcore.dao.model.VoucherBatch;
import com.gtech.gvcore.giftcard.application.dto.*;
import com.gtech.gvcore.giftcard.application.request.CreateGiftCardRequest;
import com.gtech.gvcore.giftcard.domain.model.GcRedemptionRecord;
import com.gtech.gvcore.giftcard.domain.model.GiftCard;
import com.gtech.gvcore.giftcard.domain.service.*;
import com.gtech.gvcore.giftcard.infrastructure.entity.GiftCardEntity;
import com.gtech.gvcore.giftcard.infrastructure.mapper.GiftCardMapper;
import com.gtech.gvcore.giftcard.masterdata.gcpg.dto.GcCpgDTO;
import com.gtech.gvcore.helper.FileCompressionHelper;
import com.gtech.gvcore.helper.GvCodeHelper;
import com.gtech.gvcore.helper.OssHelper;
import com.gtech.gvcore.helper.VoucherNumberHelper;
import com.gtech.gvcore.service.impl.MessageComponent;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.*;
import java.util.concurrent.CompletableFuture;

import java.util.concurrent.atomic.AtomicInteger;
import com.gtech.gvcore.common.utils.ProgressTracker;
import tk.mybatis.mapper.entity.Example;
import tk.mybatis.mapper.weekend.WeekendSqls;

import java.util.concurrent.atomic.AtomicReference;
import java.util.stream.Collectors;

/**
 * 礼品卡统一应用服务
 * 整合了激活、销售、使用等功能
 */
@Slf4j
@Service
public class GiftCardApplicationService {
    
    @Autowired
    private GcDomainService gcDomainService;

    @Autowired
    private GcSalesDomainService salesDomainService;
    @Autowired
    private GcActivateDomainService activateDomainService;

    @Autowired
    private GcRedemptionDomainService redemptionDomainService;

    @Autowired
    private GcAdjustBalanceDomainService gcAdjustBalanceDomainService;

    @Autowired
    private com.gtech.gvcore.giftcard.masterdata.gcpg.service.GcCpgService gcCpgService;

    @Autowired
    private com.gtech.gvcore.giftcard.service.CardNumberService cardNumberService;
    
    @Autowired
    private ProgressTracker progressTracker;

    @Autowired
    private GiftCardMapper giftCardMapper;

    @Autowired
    private CustomerOrderEmailMapper customerOrderEmailMapper;

    @Autowired
    private GvCodeHelper gvCodeHelper;

    @Autowired
    private FileExportManager fileExportManager;

    @Autowired
    private VoucherNumberHelper voucherNumberHelper;

    @Autowired
    private OssHelper ossHelper;

    @Autowired
    private MessageComponent messageComponent;

    public static final String EXCEL = ".xlsx";

    @Value("${gv.active.url}")
    private String activeUrl;

    /**
     * 系统初始化时，预加载所有卡号到Redis
     */
    /*@PostConstruct
    public void init() {
        log.info("初始化卡号缓存，预加载所有已有卡号到Redis...");
        try {
            // 从数据库读取已有的卡号列表
            Set<String> existingCardNumbers = gcDomainService.findAllCardNumbers();
            
            // 加载到Redis缓存
            if (existingCardNumbers != null && !existingCardNumbers.isEmpty()) {
                cardNumberService.loadCardNumbersFromDatabase(existingCardNumbers);
                log.info("成功加载{}个已存在的卡号到Redis缓存", existingCardNumbers.size());
            } else {
                log.info("数据库中没有已存在的卡号");
            }
        } catch (Exception e) {
            log.error("加载已有卡号到Redis缓存失败: {}", e.getMessage(), e);
        }
    }*/


    /**
     * Query gift card info by card number list
     * */
    public List<GiftCardEntity> queryByCardNumberList(String issuerCode, List<String> cardNumberList) {

        if (org.apache.commons.collections4.CollectionUtils.isEmpty(cardNumberList)) {
            return Collections.emptyList();
        }
        cardNumberList.removeAll(Collections.singleton(null));
        cardNumberList.removeAll(Collections.singleton(""));
        WeekendSqls<GiftCardEntity> custom = WeekendSqls.custom();
        custom.andIn(GiftCardEntity::getCardNumber, cardNumberList);
        if (issuerCode!=null){
            custom.andEqualTo(GiftCardEntity::getIssuerCode, issuerCode);
        }
        Example example = Example.builder(GiftCardEntity.class).where(custom).build();
        List<GiftCardEntity> list = this.giftCardMapper.selectByCondition(example);

        if (org.apache.commons.collections4.CollectionUtils.isEmpty(list)) {
            return Collections.emptyList();
        }
        return list;
    }

    
    /**
     * 查询礼品卡信息
     */
    public Result<GiftCardDTO> getGiftCardInfo(String cardNumber) {
        try {
            GiftCardDTO giftCardDTO = gcDomainService.getGiftCardInfo(cardNumber);
            return Result.ok(giftCardDTO);
        } catch (Exception e) {
            log.error("查询礼品卡信息失败: {}", e.getMessage(), e);
            return Result.failed(e.getMessage());
        }
    }

    public int deleteFromOrder(String orderCode){
        return gcDomainService.deleteGiftCards(orderCode);
    }
    
    /**
     * 激活礼品卡
     */
    @Transactional(rollbackFor = Exception.class)
    public Result<ActivationDTO> activateGiftCard(ActivationDTO activationDTO) {
        try {
            activationDTO.validate();
            activateDomainService.activateGiftCard(activationDTO);
            return Result.ok(activationDTO);
        } catch (IllegalArgumentException e) {
            log.error("激活礼品卡参数错误: {}", e.getMessage(), e);
            return Result.failed(e.getMessage());
        } catch (Exception e) {
            log.error("激活礼品卡失败: {}", e.getMessage(), e);
            return Result.failed("系统异常，请稍后重试");
        }
    }

    /**
     * 记录礼品卡销售
     */
    @Transactional
    public Result<GcSalesDTO> recordSales(GcSalesDTO gcSalesDTO) {

        List<GiftCard> cards = gcDomainService.findByBatchCode(gcSalesDTO.getCustomerOrderCode());
        if (CollectionUtils.isEmpty(cards)) {
            log.error("礼品卡销售记录保存失败 {}", JSON.toJSONString(gcSalesDTO));
            throw new GTechBaseException(ResultErrorCodeEnum.GIFT_CARD_SALES_ERROR.code(), ResultErrorCodeEnum.GIFT_CARD_SALES_ERROR.desc());
        }
        Map<String, List<GiftCard>> cardCpgMap = cards.stream().collect(Collectors.groupingBy(GiftCard::getCpgCode));

        AtomicReference<Integer> recordSales = new AtomicReference<>(0);
        AtomicReference<Integer> updateCard = new AtomicReference<>(0);

        cardCpgMap.forEach((cpg,cardList)->{

            GcSalesDTO cpgSales = BeanCopyUtils.jsonCopyBean(gcSalesDTO, GcSalesDTO.class);
            cpgSales.setCpgCode(cpg);
            cpgSales.setCardNumber(cardList.stream().map(GiftCard::getCardNumber).collect(Collectors.toList()));
            cpgSales.setDenomination(cardList.get(0).getDenomination());
            cpgSales.validate();
            recordSales.updateAndGet(v -> v + salesDomainService.recordSales(cpgSales));
            updateCard.updateAndGet(v -> v + gcDomainService.sellGiftCard(cpgSales));
        });

        Integer updateCardCount = updateCard.get();
        Integer recordSalesCount = recordSales.get();

        if (recordSalesCount.equals(updateCardCount) && recordSalesCount.equals(cards.size())) {
            return Result.ok(gcSalesDTO);
        } else {
            log.error("礼品卡销售记录保存失败 {}", JSON.toJSONString(gcSalesDTO));
            throw new GTechBaseException(ResultErrorCodeEnum.GIFT_CARD_SALES_ERROR.code(), ResultErrorCodeEnum.GIFT_CARD_SALES_ERROR.desc());
        }
    }
    
    /**
     * 取消礼品卡销售
     */
    @Transactional(rollbackFor = Exception.class)
    public Result<SalesCancelDTO> cancelSales(SalesCancelDTO salesCancelDTO) {
        try {
            salesCancelDTO.validate();
            SalesCancelDTO result = salesDomainService.cancelSales(salesCancelDTO);
            return Result.ok(result);
        } catch (IllegalArgumentException e) {
            log.error("取消礼品卡销售参数错误: {}", e.getMessage(), e);
            return Result.failed(e.getMessage());
        } catch (Exception e) {
            log.error("取消礼品卡销售失败: {}", e.getMessage(), e);
            return Result.failed("系统异常，请稍后重试");
        }
    }
    
    /**
     * 记录礼品卡使用
     */
    @Transactional(rollbackFor = Exception.class)
    public Result<RedemptionDTO> recordRedemption(RedemptionDTO redemptionDTO) {
        try {
            redemptionDTO.validate();
            RedemptionDTO result = redemptionDomainService.recordRedemption(redemptionDTO);
            return Result.ok(result);
        } catch (IllegalArgumentException e) {
            log.error("记录礼品卡使用参数错误: {}", e.getMessage(), e);
            return Result.failed(e.getMessage());
        } catch (Exception e) {
            log.error("记录礼品卡使用失败: {}", e.getMessage(), e);
            return Result.failed("系统异常，请稍后重试");
        }
    }
    
    /**
     * 调整礼品卡余额
     */
    @Transactional(rollbackFor = Exception.class)
    public Result<BalanceAdjustmentDTO> adjustBalance(BalanceAdjustmentDTO adjustmentDTO) {
        try {
            adjustmentDTO.validate();
            BalanceAdjustmentDTO result = gcAdjustBalanceDomainService.adjustBalance(adjustmentDTO);
            return Result.ok(result);
        } catch (IllegalArgumentException e) {
            log.error("调整礼品卡余额参数错误: {}", e.getMessage(), e);
            return Result.failed(e.getMessage());
        } catch (Exception e) {
            log.error("调整礼品卡余额失败: {}", e.getMessage(), e);
            return Result.failed("系统异常，请稍后重试");
        }
    }


    
    /**
     * 根据卡号查询使用记录
     */
    public Result<List<GcRedemptionRecord>> getRedemptionsByCardNumber(String cardNumber) {
        try {
            List<GcRedemptionRecord> redemptionDTOList = redemptionDomainService.getRedemptionsByCardNumber(cardNumber);
            return Result.ok(redemptionDTOList);
        } catch (Exception e) {
            log.error("根据卡号查询礼品卡使用记录失败: {}", e.getMessage(), e);
            return Result.failed(e.getMessage());
        }
    }

    /**
     * 优化版批量生成礼品卡方法
     */
    public Result<String> generateGiftCardsBatchOptimizedV2(CreateGiftCardRequest request) {
        log.info("开始批量生成礼品卡, 请求参数: {}", JSON.toJSONString(request));

        try {
            // 1. 验证参数并准备数据
            BatchContext context = validateAndPrepare(request);
            if (context.errorMessage != null) {
                return Result.failed(context.errorMessage);
            }

            // 2. 初始化进度跟踪
            String taskName = "批量生成礼品卡-" + context.batchId;
            progressTracker.initMainTask(context.batchId, context.totalCards, taskName);

            // 3. 异步执行批量生成
            execute(context);

            log.info("批量生成礼品卡任务已提交, 批次ID: {}, 总卡数: {}", context.batchId, context.totalCards);
            return Result.ok(context.batchId);

        } catch (Exception e) {
            log.error("批量生成礼品卡失败: {}", e.getMessage(), e);
            throw new GTechBaseException(ResultErrorCodeEnum.GENERAL_ERROR.code(), ResultErrorCodeEnum.GENERAL_ERROR.desc());
        }
    }

    /**
     * 验证参数并准备批处理数据
     */
    private BatchContext validateAndPrepare(CreateGiftCardRequest request) {
        BatchContext context = new BatchContext();

        // 1. 基础验证
        if (request == null) {
            context.errorMessage = "请求不能为空";
            return context;
        }

        if (request.getBatchItems() == null || request.getBatchItems().isEmpty()) {
            context.errorMessage = "请求必须包含有效的生成参数";
            return context;
        }

        // 2. 生成批次ID
        context.batchId = StringUtils.isNotBlank(request.getBatchId()) ?
                request.getBatchId() : UUID.randomUUID().toString();

        // 3. 预加载CPG信息
        Map<String, GcCpgDTO> cpgInfoMap = new HashMap<>();
        for (CreateGiftCardRequest.GiftCardBatchItem item : request.getBatchItems()) {
            String cpgCode = item.getCpgCode();
            if (!cpgInfoMap.containsKey(cpgCode)) {
                GcCpgDTO cpgInfo = gcCpgService.getCpg(cpgCode);
                if (cpgInfo == null) {
                    context.errorMessage = "CPG不存在: " + cpgCode;
                    return context;
                }
                cpgInfoMap.put(cpgCode, cpgInfo);
            }
        }

        // 4. 准备批次项
        List<BatchCreateItem> batchItems = new ArrayList<>();
        for (CreateGiftCardRequest.GiftCardBatchItem item : request.getBatchItems()) {
            if (item.getQuantity() == null || item.getQuantity() <= 0) {
                context.errorMessage = "批量项中的数量必须大于0";
                return context;
            }

            GcCpgDTO cpgInfo = cpgInfoMap.get(item.getCpgCode());
            BigDecimal denomination = item.getDenomination() != null ?
                    item.getDenomination() : cpgInfo.getDenomination();

            Date effectiveDate = gcCpgService.calculateEffectiveDate(item.getCpgCode());
            Date activationDeadline = gcCpgService.calculateActivationDeadline(item.getCpgCode());

            batchItems.add(new BatchCreateItem(
                    item.getCpgCode(),
                    denomination,
                    item.getQuantity(),
                    request.getIssuerCode(),
                    request.getCustomerOrderCode(),
                    item.getCustomerOrderDetailCode(),
                    request.getCreateUser(),
                    effectiveDate,
                    activationDeadline
            ));
        }

        if (batchItems.isEmpty()) {
            context.errorMessage = "没有有效的礼品卡生成项";
            return context;
        }

        // 5. 计算总卡数
        int totalCards = batchItems.stream()
                .mapToInt(BatchCreateItem::getQuantity)
                .sum();

        context.batchItems = batchItems;
        context.totalCards = totalCards;
        return context;
    }

    private void execute(BatchContext context) {
        try {
            // 更新任务状态为处理中
            progressTracker.updateMainTaskProgress(
                    context.batchId, 0, ProgressTracker.TaskStatus.PROCESSING, "开始批量生成礼品卡");

            AtomicInteger processedTotal = new AtomicInteger(0);

            // 并行处理所有批次项
            List<CompletableFuture<Void>> futures = context.batchItems.stream()
                    .map(item -> processItemAsyncOpt(context.batchId, item, processedTotal, context.totalCards))
                    .collect(Collectors.toList());

            // 等待所有任务完成
            CompletableFuture.allOf(futures.toArray(new CompletableFuture[0])).join();

            // 更新最终状态
            int finalProcessed = processedTotal.get();
            ProgressTracker.TaskStatus finalStatus =
                    (finalProcessed >= context.totalCards) ?
                            ProgressTracker.TaskStatus.COMPLETED : ProgressTracker.TaskStatus.PROCESSING;

            String finalMessage = (finalProcessed >= context.totalCards) ?
                    "批量生成礼品卡全部完成" :
                    String.format("批量生成礼品卡部分完成(%d/%d)", finalProcessed, context.totalCards);

            progressTracker.updateMainTaskProgress(
                    context.batchId, finalProcessed, finalStatus, finalMessage);

            log.info("批量生成礼品卡处理完成, 批次ID: {}, 状态: {}, 总数: {}/{}",
                    context.batchId, finalStatus, finalProcessed, context.totalCards);

        } catch (Exception e) {
            String errorMsg = "批量生成礼品卡处理异常: " + e.getMessage();
            log.error(errorMsg, e);
            progressTracker.updateMainTaskProgress(
                    context.batchId, 0, ProgressTracker.TaskStatus.FAILED, errorMsg);
            throw new GTechBaseException(ResultErrorCodeEnum.GENERAL_ERROR.code(), ResultErrorCodeEnum.GENERAL_ERROR.desc());
        }
    }

    /**
     * 异步处理单个批次项
     */
    private CompletableFuture<Void> processItemAsyncOpt(
            String batchId, BatchCreateItem item, AtomicInteger processedTotal, int totalCards) {

        return CompletableFuture.runAsync(() -> {
            String subTaskId = item.getCpgCode() + "-" + item.hashCode();

            try {
                // 初始化子任务
                String subTaskName = String.format("生成礼品卡 - CPG: %s, 面额: %s, 数量: %d",
                        item.getCpgCode(), item.getDenomination(), item.getQuantity());
                progressTracker.initSubTask(batchId, subTaskId, item.getQuantity(), subTaskName);

                // 分批生成卡片
                generateCardsInChunks(
                        batchId, subTaskId, item, processedTotal, totalCards);


            } catch (Exception e) {
                String errorMsg = String.format("子任务处理异常: CPG: %s, 数量: %d, 错误: %s",
                        item.getCpgCode(), item.getQuantity(), e.getMessage());
                log.error(errorMsg, e);

                progressTracker.updateSubTaskProgress(
                        batchId, subTaskId, 0, ProgressTracker.TaskStatus.FAILED, errorMsg);
                throw new GTechBaseException(ResultErrorCodeEnum.GENERAL_ERROR.code(),ResultErrorCodeEnum.GENERAL_ERROR.desc());
            }
        });
    }



    /**
     * 简单的批处理上下文类
     */
    private static class BatchContext {
        String batchId;
        List<BatchCreateItem> batchItems;
        int totalCards;
        String errorMessage;
    }



    // 生成卡片
    private int generateCardsInChunks(String batchId, String subTaskId,
                             BatchCreateItem item, AtomicInteger processedTotal, int totalItems) {
        
        int batchSize = 1000;  // 每次生成的最大数量
        int totalQuantity = item.getQuantity();
        int processedSubTask = 0;
        
        // 分批生成卡片
        for (int offset = 0; offset < totalQuantity; offset += batchSize) {
            int currentBatchSize = Math.min(batchSize, totalQuantity - offset);
            
            // 批量创建礼品卡
            List<String> generatedCardNumbers = gcDomainService.batchCreateGiftCards(batchId,
                    item.getCpgCode(), item.getDenomination(), 
                    currentBatchSize, item.getIssuerCode());
            
            // 更新进度
            processedSubTask += generatedCardNumbers.size();
            int currentTotal = processedTotal.addAndGet(generatedCardNumbers.size());
            
            // 更新子任务进度
            updateProgress(batchId, subTaskId, item, 
                          processedSubTask, currentTotal, totalItems);
        }
        
        return processedSubTask;
    }

    // 更新进度
    private void updateProgress(String batchId, String subTaskId, BatchCreateItem item,
                              int processedSubTask, int currentTotal, int totalItems) {
        
        // 更新子任务进度
        progressTracker.updateSubTaskProgress(
                batchId, subTaskId, processedSubTask,
                ProgressTracker.TaskStatus.PROCESSING,
                String.format("已生成 %d/%d 张卡", processedSubTask, item.getQuantity()));
        
        // 更新主任务进度
        progressTracker.updateMainTaskProgress(
                batchId, currentTotal, ProgressTracker.TaskStatus.PROCESSING,
                String.format("已生成 %d/%d 张卡", currentTotal, totalItems));
    }

    // 处理异步处理异常
    private void handleProcessingException(String batchId, Throwable e) {
        String errorMsg = "异步生成礼品卡任务异常: " + e.getMessage();
        log.error(errorMsg, e);
        progressTracker.updateMainTaskProgress(
                batchId, 0, ProgressTracker.TaskStatus.FAILED, errorMsg);
    }

    /**
     * 获取批量生成礼品卡的进度
     * 
     * @param batchId 批次ID
     * @return 进度信息（完成百分比和状态）
     */
    public Result<ProgressTracker.TaskProgress> getBatchProgress(String batchId) {
        try {
            if (batchId == null || batchId.isEmpty()) {
                return Result.failed("批次ID不能为空");
            }
            
            ProgressTracker.TaskProgress progress = progressTracker.getMainTaskProgress(batchId);
            
            if (progress == null || progress.getStatus() == ProgressTracker.TaskStatus.UNKNOWN) {
                return Result.failed("找不到指定批次的进度信息: " + batchId);
            }
            
            return Result.ok(progress);
        } catch (Exception e) {
            log.error("获取批量任务进度失败: {}", e.getMessage(), e);
            return Result.failed(e.getMessage());
        }
    }
    
    /**
     * 获取批量生成礼品卡的子任务进度
     * 
     * @param batchId 批次ID
     * @return 子任务进度信息映射
     */
    public Result<Map<String, ProgressTracker.TaskProgress>> getBatchSubTasksProgress(String batchId) {
        try {
            if (batchId == null || batchId.isEmpty()) {
                return Result.failed("批次ID不能为空");
            }
            
            Map<String, ProgressTracker.TaskProgress> subTasks = progressTracker.getSubTasks(batchId);
            
            if (subTasks.isEmpty()) {
                return Result.failed("找不到指定批次的子任务进度信息: " + batchId);
            }
            
            return Result.ok(subTasks);
        } catch (Exception e) {
            log.error("获取批量任务子任务进度失败: {}", e.getMessage(), e);
            return Result.failed(e.getMessage());
        }
    }
    
    /**
     * 准备批量创建项
     */
    private List<BatchCreateItem> prepareBatchItems(CreateGiftCardRequest request) {
        List<BatchCreateItem> result = new ArrayList<>();
        
        // 提前收集所有需要的CPG编码
        Set<String> cpgCodes = request.getBatchItems().stream()
                .map(CreateGiftCardRequest.GiftCardBatchItem::getCpgCode)
                .collect(Collectors.toSet());
        
        // 批量查询CPG信息并缓存
        Map<String, GcCpgDTO> cpgInfoMap = new HashMap<>();
        for (String cpgCode : cpgCodes) {
            GcCpgDTO cpgInfo = gcCpgService.getCpg(cpgCode);
            if (cpgInfo != null) {
                cpgInfoMap.put(cpgCode, cpgInfo);
            } else {
                throw new GTechBaseException(ResultErrorCodeEnum.NO_CPG_DATA_FOUND.code(), ResultErrorCodeEnum.NO_CPG_DATA_FOUND.desc());
            }
        }
        
        // 处理批量项列表
        if (CollectionUtils.isNotEmpty(request.getBatchItems())) {
            for (CreateGiftCardRequest.GiftCardBatchItem item : request.getBatchItems()) {
                if (item.getQuantity() != null && item.getQuantity() > 0) {
                    GcCpgDTO cpgInfo = cpgInfoMap.get(item.getCpgCode());
                    BigDecimal denomination = item.getDenomination();
                    if (denomination == null) {
                        denomination = cpgInfo.getDenomination();
                    }
                    Date effectiveDate = calculateEffectiveDate(cpgInfo);
                    Date activationDeadline = calculateActivationDeadline(cpgInfo);
                    String issuerCode = request.getIssuerCode();

                    
                    result.add(new BatchCreateItem(
                        item.getCpgCode(),
                        denomination,
                        item.getQuantity(),
                        issuerCode,
                        request.getCustomerOrderCode(),
                        item.getCustomerOrderDetailCode(),
                        request.getCreateUser(),
                        effectiveDate,
                        activationDeadline

                    ));
                } else {
                    throw new IllegalArgumentException("批量项中的数量必须大于0");
                }
            }
        } 

        
        return result;
    }
    
    /**
     * 从CPG信息计算生效日期
     */
    private Date calculateEffectiveDate(GcCpgDTO cpgInfo) {
        // 必须有有效的CPG信息
        if (cpgInfo == null || cpgInfo.getCpgCode() == null) {
            throw new IllegalArgumentException("CPG信息不能为空");
        }
        
        // 直接使用GcCpgService计算生效日期
        Date effectiveDate = gcCpgService.calculateEffectiveDate(cpgInfo.getCpgCode());
        if (effectiveDate == null) {
            throw new IllegalArgumentException("无法从CPG获取生效日期: " + cpgInfo.getCpgCode());
        }
        
        return effectiveDate;
    }

    /**
     * 从CPG信息计算生效日期
     */
    private Date calculateActivationDeadline(GcCpgDTO cpgInfo) {
        // 必须有有效的CPG信息
        if (cpgInfo == null || cpgInfo.getCpgCode() == null) {
            throw new IllegalArgumentException("CPG信息不能为空");
        }

        // 直接使用GcCpgService计算生效日期
        Date effectiveDate = gcCpgService.calculateActivationDeadline(cpgInfo.getCpgCode());
        if (effectiveDate == null) {
            throw new IllegalArgumentException("无法从CPG获取生效日期: " + cpgInfo.getCpgCode());
        }

        return effectiveDate;
    }

    public void sendGiftCardExcelToEmail(SendDigitalVoucherExcelToEmailRequest request) {

        String invoiceNumber = request.getInvoiceNumber();

        PageBean pageBean = new PageBean();
        pageBean.setPageSize(500);
        pageBean.setPageNum(0);
        String fileName = gvCodeHelper.generateFileNameCode();


        PagedData<ExportGiftCardDto> cardData = PagedDatas
                .<ExportGiftCardDto, PageData<ExportGiftCardDto>>builder()
                .query(() -> gcDomainService.findByBatchCode(request.getCustomerOrderCode(), pageBean,invoiceNumber,activeUrl))
                .queryResultConverter(PageData::getList)
                .hasNextPage(pageResult -> pageBean.getPageNum() <= totalPageSize(pageResult.getTotal(), pageBean.getPageSize()))
                .afterQuery(() ->
                        pageBean.setPageNum(pageBean.getPageNum() + 1)
                ).build();



        //电子券excel暂无密码
        String secretCode = voucherNumberHelper.randomPassword(10);
        ExcelExportSpec.SheetBuilder<ExportGiftCardDto> sheet = ExcelExportSpec.builder()
                .sheet(ExportGiftCardDto.class, "Gift Card")
                .dataSource(cardData);


        FileExport fileExport = FileExport.builder()
                .domainCode(GvcoreConstants.SYSTEM_DEFAULT)
                .tenantCode(GvcoreConstants.SYSTEM_DEFAULT)
                .userId(request.getCreateUser())
                .name(fileName)
                .category("voucher.digital")
                .spec(sheet.build())
                .build();

        String accessUrl = "";
        Resp<FileExportResult> result = FileExportResult.from(fileExportManager.export(fileExport));
        accessUrl = result.getData().getAccessUrl();

        final FileCompressionHelper.EncryptCompressionParameter parameter = FileCompressionHelper.EncryptCompressionParameter.builder()
                .fileUrl(accessUrl)
                .fileName(fileName + EXCEL)
                .password(secretCode)
                .build();
        accessUrl = ossHelper.compressionUploadToOss(parameter, fileName);
        String[] split = request.getEmail().split(",");
        for (String email : split) {

            List<CustomerOrderEmail> emailExistArray = customerOrderEmailMapper.select(new CustomerOrderEmail().setEmailAddress(email).setCustomerOrderCode(request.getCustomerOrderCode()));

            //构建邮件记录对象
            int status;
            //发送EXCEL邮件 TEST
            try {

                //SEND
                VoucherBatchResponse voucherBatch = new VoucherBatchResponse();
                voucherBatch.setFileName(fileName);
                sendExcelToEmail(voucherBatch, ossHelper.grantAccessUrl(accessUrl), email, secretCode, MessageEnventEnum.EGV_CUSTOMER_ORDER_COMPLETED);
                sendExcelToEmail(voucherBatch, null, email, secretCode, MessageEnventEnum.SEND_E_VOUCHER_FILE_PWD);

                status = 1;
                log.info("邮件发送完毕{}",JSON.toJSONString(request));
            } catch (Exception e) {

                //ERROR
                log.error("发送邮件失败", e);
                status = 0;
            }

            //insert
            if (org.apache.commons.collections4.CollectionUtils.isEmpty(emailExistArray)) {
                customerOrderEmailMapper.insertSelective(new CustomerOrderEmail()
                        .setEmailCode(gvCodeHelper.generateCustomerOrderEmailCode())
                        .setEmailAddress(email)
                        .setExcelFileUrl(accessUrl)
                        .setSecretCode(CryptoUtils.aesEncrypt(secretCode))
                        .setCreateUser(request.getCreateUser())
                        .setCustomerOrderCode(request.getCustomerOrderCode())
                        .setFileName(fileName)
                        .setSendStatus(status));
            } else {
                customerOrderEmailMapper.updateByPrimaryKeySelective(new CustomerOrderEmail()
                        .setId(emailExistArray.get(0).getId())
                        .setSendTime(new Date())
                        .setFileName(fileName)
                        .setSecretCode(CryptoUtils.aesEncrypt(secretCode))
                        .setExcelFileUrl(accessUrl)
                        .setSendStatus(status));

            }
        }

    }
    private int totalPageSize(Long total, int pageSize) {
        return (int) (total % pageSize == 0 ? total / pageSize : total / pageSize + 1);
    }


    private void sendExcelToEmail(VoucherBatchResponse voucherBatch, String fileUrl, String email, String pwd, MessageEnventEnum type) {

        JSONObject messageRequest = new JSONObject();
        messageRequest.put("eventCode", type.getCode());
        JSONObject param = new JSONObject();
        String json = JSON.toJSONString(voucherBatch);
        param.putAll(JSONObject.parseObject(json, Map.class));
        param.put(VoucherBatch.C_CREATE_TIME, DateUtil.format(voucherBatch.getCreateTime(), GvcoreConstants.EMAIL_DATE_FORMAT));
        param.put("email", email);
        param.put("password", pwd);
        String fileName = voucherBatch.getFileName() + ".zip";
        param.put("filename", fileName);
        if (StringUtil.isNotEmpty(fileUrl)) {
            JSONArray attachments = new JSONArray();
            JSONObject files = new JSONObject();
            files.put("filename", fileName);
            files.put("url", fileUrl);
            attachments.add(files);
            param.put("attachments", attachments);
        }
        messageRequest.put("param", param);
        messageComponent.send(messageRequest);
    }

    //取消 release
    public void cancelRelease(CustomerOrder customerOrder) {
        SalesCancelDTO salesCancelDTO = new SalesCancelDTO();
        salesCancelDTO.setSalesCode(customerOrder.getCustomerOrderCode());
        salesCancelDTO.setCancelReason("cancel release");
        salesDomainService.cancelSalesByPoNumber(salesCancelDTO);
        gcDomainService.cancelRelease(customerOrder);
    }


    /**
     * 批量创建项内部类 - 简化参数传递
     */
    private static class BatchCreateItem {
        private final String cpgCode;
        private final BigDecimal denomination;
        private final int quantity;
        private final String issuerCode;
        private final String customerOrderCode;
        private final String customerOrderDetailCode;
        private final String createUser;
        private final Date effectiveDate;
        private final Date activationDeadline;

        public BatchCreateItem(String cpgCode, BigDecimal denomination, int quantity, 
                             String issuerCode, String customerOrderCode, 
                             String customerOrderDetailCode, String createUser,
                             Date effectiveDate, Date activationDeadline) {
            this.cpgCode = cpgCode;
            this.denomination = denomination;
            this.quantity = quantity;
            this.issuerCode = issuerCode;
            this.customerOrderCode = customerOrderCode;
            this.customerOrderDetailCode = customerOrderDetailCode;
            this.createUser = createUser;
            this.effectiveDate = effectiveDate;
            this.activationDeadline = activationDeadline;
        }
        
        public String getCpgCode() {
            return cpgCode;
        }
        
        public BigDecimal getDenomination() {
            return denomination;
        }
        
        public int getQuantity() {
            return quantity;
        }
        
        public String getIssuerCode() {
            return issuerCode;
        }
        
        public String getCustomerOrderCode() {
            return customerOrderCode;
        }
        
        public String getCustomerOrderDetailCode() {
            return customerOrderDetailCode;
        }
        
        public String getCreateUser() {
            return createUser;
        }
        
        public Date getEffectiveDate() {
            return effectiveDate;
        }
        public Date getActivationDeadline() {
            return activationDeadline;
        }
    }

    /**
     * 计算总共需要生成的礼品卡数量
     */
    private int calculateTotalCards(List<BatchCreateItem> batchItems) {
        return batchItems.stream()
                .mapToInt(BatchCreateItem::getQuantity)
                .sum();
    }

    /**
     * 验证批次项参数
     */
    private void validateBatchItemParameters(BatchCreateItem item) {
        if (item.getCpgCode() == null || item.getCpgCode().isEmpty()) {
            throw new IllegalArgumentException("CPG编码不能为空");
        }
        if (item.getDenomination() == null) {
            throw new IllegalArgumentException("面额不能为空");
        }
        if (item.getQuantity() <= 0) {
            throw new IllegalArgumentException("数量必须大于0");
        }
        if (item.getIssuerCode() == null || item.getIssuerCode().isEmpty()) {
            throw new IllegalArgumentException("发行方编码不能为空");
        }
        if (item.getEffectiveDate() == null) {
            throw new IllegalArgumentException("生效日期不能为空");
        }
    }
} 