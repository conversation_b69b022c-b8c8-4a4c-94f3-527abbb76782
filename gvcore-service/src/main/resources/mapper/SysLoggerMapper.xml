<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.gtech.gvcore.dao.mapper.SysLoggerMapper">

  <insert id="insert">
    insert gv_sys_logger_${month} ( 
	    user_code,request_path,request_data,response_data,create_time
	 ) values 
       ( #{data.userCode,jdbcType=VARCHAR},#{data.requestPath,jdbcType=VARCHAR},#{data.requestData,jdbcType=VARCHAR},#{data.responseData,jdbcType=VARCHAR},#{data.createTime,jdbcType=TIMESTAMP} )
  </insert>
  
  <select id="selectOne"  resultType="com.gtech.gvcore.dao.model.SysLogger">
    select * from  gv_sys_logger_${month} where id = #{id}
  </select>
  
  <update id="truncate" parameterType="java.lang.Integer">
  	truncate gv_sys_logger_${month}
  </update>

  <update id="truncateReportTable" parameterType="java.lang.Integer">
    truncate gv_order_report_data_${month}
  </update>

<sql id="t_query_condition">
    <trim prefix="WHERE" prefixOverrides="AND |OR ">
      <if test="requestId != null">
        AND (id = #{requestId})
      </if>
      <if test="userCode != null and userCode.trim().length() != 0">
        AND (user_code = #{userCode})
      </if>
      <if test="operateTimeBegin != null">
        AND (create_time >= #{operateTimeBegin})
      </if>
      <if test="operateTimeEnd != null">
        AND (create_time &lt;#{operateTimeEnd})
      </if>
    </trim>
  </sql>
  <select id="query" resultType="com.gtech.gvcore.dao.model.SysLogger">
    select id, user_code as userCode,create_time as createTime,request_path as requestPath
     from gv_sys_logger_${month} 
      <include refid="t_query_condition" />
  </select>
</mapper>