package com.gtech.gvcore.common.request.gcapi;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.Date;

/**
 * GC Issuance Request Class
 */
@Data
@ApiModel(value = "GcIssuanceRequest", description = "Gift Card Issuance Request")
public class GcIssuanceRequest {

    @ApiModelProperty(value = "Transaction Code", notes = "Fill in with 805 - GC Issuance", required = true, example = "805", position = 1)
    @NotNull(message = "transactionCode cannot be null")
    private Integer transactionCode;

    @ApiModelProperty(value = "Input Type", notes = "4-EGC (e-Gift Cards)", required = true, example = "4", position = 2)
    @NotEmpty(message = "inputType cannot be empty")
    private String inputType;

    @ApiModelProperty(value = "Number of Gift Cards", notes = "Gift Cards quantity", required = true, example = "10", position = 3)
    private Integer numberOfGiftCards;

    @ApiModelProperty(value = "Invoice Number", notes = "Invoice number from POS/E-commerce", required = true, example = "INV-12345", position = 4)
    @NotEmpty(message = "invoiceNumber cannot be empty")
    private String invoiceNumber;

    @ApiModelProperty(value = "Invoice Date", notes = "Invoice Date of client side in YYYY-MM-DDTHH:MM:SS format", example = "2023-01-01T12:00:00", position = 5)
    private Date invoiceDate;

    @ApiModelProperty(value = "Buyer", notes = "Buyer information", position = 6)
    private CustomerInfo buyer;

    @ApiModelProperty(value = "Notes", notes = "Reference text", example = "Special order reference", position = 7)
    private String notes;

    @ApiModelProperty(value = "Retry Key", notes = "Reference number for the request", example = "RETRY123456", position = 8)
    private String retryKey;

    @ApiModelProperty(value = "GCPG", notes = "GCPG Name", required = true, example = "STANDARD_GIFT_CARD", position = 9)
    private String gcpg;

    @ApiModelProperty(value = "Source", notes = "Reference for outletname/POSname/brandname/partnername", example = "MOBILE_APP", position = 10)
    private String source;

} 