package com.gtech.gvcore.service.report.impl;

import com.gtech.commons.utils.DateUtil;
import com.gtech.gvcore.common.enums.ReportExportTypeEnum;
import com.gtech.gvcore.common.request.orderreport.CreateReportRequest;
import com.gtech.gvcore.dao.model.Cpg;
import com.gtech.gvcore.dao.model.Customer;
import com.gtech.gvcore.dao.model.Merchant;
import com.gtech.gvcore.dao.model.Outlet;
import com.gtech.gvcore.service.report.BusinessReport;
import com.gtech.gvcore.service.report.SingleReport;
import com.gtech.gvcore.service.report.base.ReportSupport;
import com.gtech.gvcore.service.report.extend.joindate.JoinDataMap;
import com.gtech.gvcore.service.report.extend.poll.PollPageHelper;
import com.gtech.gvcore.service.report.impl.bean.GcExtendExpiryForDetailBean;
import com.gtech.gvcore.service.report.impl.bo.GcExtendExpiryBo;
import com.gtech.gvcore.service.report.impl.param.GcExtendExpiryQueryData;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.Collection;
import java.util.Collections;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * @Author: gao.yuhua
 * @Date: 2022/5/11 13:57
 * @Description: Gift Card Extend Expiry Report Detail Implementation
 */
@Service
public class GcExtendExpiryForDetailImpl extends ReportSupport
        implements BusinessReport<GcExtendExpiryQueryData, GcExtendExpiryForDetailBean>, SingleReport {

    @Override
    public GcExtendExpiryQueryData builderQueryParam(CreateReportRequest reportParam) {

        GcExtendExpiryQueryData queryData = new GcExtendExpiryQueryData();

        queryData.setIssuerCodeList(StringUtils.isNotBlank(reportParam.getIssuerCode()) ? Collections.singletonList(reportParam.getIssuerCode()) : null);
        queryData.setMerchantCodeList(reportParam.getMerchantCodes());
        queryData.setOutletCodeList(reportParam.getOutletCodes());

        // 交易时间范围对应延长时间
        queryData.setTransactionDateStart(reportParam.getTransactionDateStart());
        queryData.setTransactionDateEnd(reportParam.getTransactionDateEnd());

        queryData.setCpgCodeList(reportParam.getCpgCodes());

        return queryData;
    }

    @Override
    public List<GcExtendExpiryForDetailBean> getExportData(GcExtendExpiryQueryData queryData) {

        //find
        final Collection<GcExtendExpiryBo> list =
                Optional.of(PollPageHelper.pollGroupNewTransactionByCodeSelect(gcReportBusinessMapper::selectGcExtendExpiry, queryData))
                        .orElse(Collections.emptyList());

        //empty
        if (CollectionUtils.isEmpty(list)) return Collections.emptyList();

        //init map
        final JoinDataMap<Cpg> cpgMap = super.getMapByCode(list, GcExtendExpiryBo::getCpgCode, Cpg.class);
        final JoinDataMap<Outlet> outletMap = super.getMapByCode(list, GcExtendExpiryBo::getOutletCode, Outlet.class);
        final JoinDataMap<Merchant> merchantMap = super.getMapByCode(outletMap.values(), Outlet::getMerchantCode, Merchant.class);
        final JoinDataMap<Customer> customerMap = super.getMapByCode(list, GcExtendExpiryBo::getOwnerCustomer, Customer.class);

        //convert result
        return list.stream()
                .map(e -> {
                    Outlet outlet = outletMap.findValue(e.getOutletCode());
                    return new GcExtendExpiryForDetailBean()
                            .setGiftCardNumber(e.getCardNumber())
                            .setMerchant(merchantMap.findValue(outlet.getMerchantCode()).getMerchantName())
                            .setMerchantOutlet(outlet.getOutletName())
                            .setTransactionType("Expiry Extension") // 固定为延长过期类型
                            .setGiftCardProgramGroup(cpgMap.findValue(e.getCpgCode()).getCpgName())
                            .setTransactionDate(DateUtil.format(e.getExtensionTime(), "yyyy-MM-dd HH:mm:ss"))
                            .setIssuanceDate(e.getSalesTime() != null ? DateUtil.format(e.getSalesTime(), "yyyy-MM-dd HH:mm:ss") : "")
                            .setActivationPeriodEnded(e.getOldActivationDeadline() != null ? DateUtil.format(e.getOldActivationDeadline(), "yyyy-MM-dd HH:mm:ss") : "")
                            .setGracePeriodEnded(e.getNewActivationDeadline() != null ? DateUtil.format(e.getNewActivationDeadline(), "yyyy-MM-dd HH:mm:ss") : "")
                            .setCustomerName(customerMap.findValue(e.getOwnerCustomer()).getCustomerName())
                            .setSource(e.getSource());
                })
                .collect(Collectors.toList());
    }

    @Override
    public ReportExportTypeEnum exportTypeEnum() {
        return ReportExportTypeEnum.GC_EXTEND_EXPIRY_DETAILED;
    }
}
