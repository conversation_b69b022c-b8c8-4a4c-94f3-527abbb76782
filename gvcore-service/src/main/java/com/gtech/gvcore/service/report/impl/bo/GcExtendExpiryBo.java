package com.gtech.gvcore.service.report.impl.bo;

import com.gtech.commons.utils.DateUtil;
import com.gtech.gvcore.service.report.extend.poll.GroupNewTransactionByVoucherCodeSupport;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

import java.math.BigDecimal;
import java.util.Date;

/**
 * @ClassName GcExtendExpiryBo
 * @Description Gift Card Extend Expiry Business Object
 * <AUTHOR>
 * @Date 2023/5/10 10:40
 * @Version V1.0
 **/
@Getter
@Setter
@Accessors(chain = true)
public class GcExtendExpiryBo implements GroupNewTransactionByVoucherCodeSupport {

    private String transactionCode;

    private BigDecimal transactionNumber;

    public void setTransactionCode(String transactionCode) {
        if (transactionCode != null && transactionCode.length() > 3) {
            this.transactionNumber = new BigDecimal(transactionCode.substring(3));
        }
        this.transactionCode = transactionCode;
    }

    /**
     * Card Number.
     */
    private String cardNumber;

    /**
     * Extension Code.
     */
    private String extensionCode;

    /**
     * Outlet code.
     */
    private String outletCode;

    /**
     * Cpg code.
     */
    private String cpgCode;

    /**
     * Extension Time.
     */
    private String extensionTime;

    /**
     * Old Activation Deadline.
     */
    private String oldActivationDeadline;

    /**
     * New Activation Deadline.
     */
    private String newActivationDeadline;

    /**
     * Extension Count.
     */
    private Integer extensionCount;

    /**
     * Issuer Code.
     */
    private String issuerCode;

    /**
     * Merchant Code.
     */
    private String merchantCode;

    /**
     * Invoice Number.
     */
    private String invoiceNumber;

    /**
     * Approval Code.
     */
    private String approvalCode;

    /**
     * Notes.
     */
    private String notes;

    /**
     * Batch Number.
     */
    private String batchNumber;

    /**
     * Source.
     */
    private String source;

    /**
     * Amount (需要从gift card表获取).
     */
    private BigDecimal amount;

    /**
     * Sales Time (Issuance Date).
     */
    private String salesTime;

    /**
     * Activation Time.
     */
    private String activationTime;

    /**
     * Owner Customer.
     */
    private String ownerCustomer;

    public Date getExtensionTime() {
        return DateUtil.parseDate(extensionTime, DateUtil.FORMAT_YYYYMMDDHHMISS);
    }

    public Date getSalesTime() {
        return DateUtil.parseDate(salesTime, DateUtil.FORMAT_YYYYMMDDHHMISS);
    }

    public Date getActivationTime() {
        return DateUtil.parseDate(activationTime, DateUtil.FORMAT_YYYYMMDDHHMISS);
    }

    public Date getOldActivationDeadline() {
        return DateUtil.parseDate(oldActivationDeadline, DateUtil.FORMAT_YYYYMMDDHHMISS);
    }

    public Date getNewActivationDeadline() {
        return DateUtil.parseDate(newActivationDeadline, DateUtil.FORMAT_YYYYMMDDHHMISS);
    }

    @Override
    public String getVoucherCode() {
        return this.cardNumber;
    }
}
