package com.gtech.gvcore.giftcard.domain.model;

import com.gtech.gvcore.giftcard.domain.service.BarcodeConverter;
import com.gtech.gvcore.giftcard.domain.service.CardNumberGenerator;
import com.gtech.gvcore.giftcard.util.PeriodCalculator;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 礼品卡实体
 */
@Data
public class GiftCard {
    private String issuerCode;
    private String cardBatchCode;
    private String cardNumber;
    private String barCode;
    private GcCardStatus status;
    private GcMgtCardStatus managementStatus;
    private BigDecimal balance;
    private String cpgCode; 
    private BigDecimal denomination;
    private String activationCode;
    private String pinCode;
    private Date activationDeadline;
    private Integer activationExtensionCount;
    private Date activationTime;
    private Date expiryTime;
    private String salesOutlet;
    private Date salesTime;
    private String ownerCustomer;
    private String effectivePeriod;
    private String activationGracePeriod;




    // 激活礼品卡
    public void activate() {
        validateCanActivate();
        this.activationTime = new Date();
        this.status = GcCardStatus.ACTIVATED;
        this.balance = this.denomination;
        //激活过期时间 = 当前时间 + 过期时间公式时间
        this.expiryTime = PeriodCalculator.calculateDate(effectivePeriod);
    }

    //延长过期时间
    public Date extendExpiryTime() {

        if (checkActivated()) {
            throw new IllegalStateException("Gift card has been activated and cannot be extended");
        }

        if (!checkPurchased()){
            throw new IllegalStateException("Gift card status is incorrect and cannot be extended");
        }

        if (checkExpired()) {
            throw new IllegalStateException("Gift card has expired and cannot be extended");
        }

        if (checkActivationExpired()) {
            throw new IllegalStateException("Gift card has not reached the activation expiration time and cannot be extended");
        }
        
        if (activationExtensionCount >= 1) {
            throw new IllegalStateException("Gift card extensions have reached the maximum limit");
        }

        activationExtensionCount++;

        return this.activationDeadline = PeriodCalculator.calculateDate(activationGracePeriod,activationDeadline);
    }



    // 扣减余额
    public void deduct(BigDecimal amount) {
        if (!checkActivated()) {
            throw new IllegalStateException("礼品卡未激活，无法扣减余额");
        }
        if (checkExpired()) {
            throw new IllegalStateException("礼品卡已过期，无法扣减余额");
        }
        if (amount.compareTo(BigDecimal.ZERO) <= 0) {
            throw new IllegalArgumentException("扣减金额必须大于零");
        }
        if (amount.compareTo(this.balance) > 0) {
            throw new IllegalStateException("余额不足");
        }
        this.balance = this.balance.subtract(amount);
    }

    // 增加余额
    public void addBalance(BigDecimal amount) {
        if (!checkActivated()) {
            throw new IllegalStateException("礼品卡未激活，无法增加余额");
        }
        if (checkExpired()) {
            throw new IllegalStateException("礼品卡已过期，无法增加余额");
        }
        if (amount.compareTo(BigDecimal.ZERO) <= 0) {
            throw new IllegalArgumentException("增加金额必须大于零");
        }
        this.balance = this.balance.add(amount);
    }

    // 验证是否可以激活
    private void validateCanActivate() {
        if (this.status != GcCardStatus.PURCHASED) {
            throw new IllegalStateException("礼品卡状态不正确");
        }

        if (this.activationDeadline.compareTo(new Date()) < 0) {
            throw new IllegalStateException("礼品卡已过期");
        }
    }

    // 检查是否已过期
    public boolean checkExpired() {
        if (this.expiryTime == null) {
            throw new IllegalStateException("礼品卡没有设置过期时间");
        }
        return this.expiryTime.before(new Date());
    }

    // 检查是否超出激活过期时间
    public boolean checkActivationExpired() {
        if (this.activationDeadline == null) {
            throw new IllegalStateException("礼品卡没有设置激活过期时间");
        }
        return this.activationDeadline.after(new Date());
    }

    

    // 检查是否已激活
    public boolean checkActivated() {
        return status == GcCardStatus.ACTIVATED && managementStatus == GcMgtCardStatus.ENABLE;
    }


    // 检查是否可激活
    public boolean checkPurchased() {
        return status == GcCardStatus.PURCHASED && managementStatus == GcMgtCardStatus.ENABLE;
    }

    // 验证卡号是否有效
    public boolean checkValidCardNumber(CardNumberGenerator cardNumberGenerator) {
        return cardNumberGenerator.isValid(this.cardNumber);
    }

    // 验证激活码是否有效
    public boolean isValidActivationCode(String activationCode) {
        return this.activationCode != null && this.activationCode.equals(activationCode);
    }


    // 从条形码提取卡号
    public static String extractCardNumberFromBarcode(String barcode, BarcodeConverter barcodeConverter) {
        return barcodeConverter.extractCardNumber(barcode);
    }
    
    

    // 批量创建用的无参构造函数
    public GiftCard() {
        // 初始化状态
        this.status = GcCardStatus.PURCHASED;
        this.managementStatus = GcMgtCardStatus.ENABLE;
        this.activationExtensionCount = 0;
        this.balance = BigDecimal.ZERO;
    }
} 