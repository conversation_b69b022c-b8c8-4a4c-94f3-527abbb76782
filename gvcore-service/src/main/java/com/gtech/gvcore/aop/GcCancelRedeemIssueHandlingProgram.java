package com.gtech.gvcore.aop;

import com.gtech.gvcore.common.enums.IssueHandlingTypeEnum;
import com.gtech.gvcore.common.enums.RedemptionStatusEnum;
import com.gtech.gvcore.dao.model.IssueHandlingDetails;
import com.gtech.gvcore.giftcard.application.service.GiftCardApplicationService;
import com.gtech.gvcore.giftcard.infrastructure.entity.GcBalanceAdjustmentEntity;
import com.gtech.gvcore.giftcard.infrastructure.entity.GcRedemptionEntity;
import com.gtech.gvcore.giftcard.infrastructure.entity.GiftCardEntity;
import com.gtech.gvcore.giftcard.infrastructure.mapper.GcBalanceAdjustmentMapper;
import com.gtech.gvcore.giftcard.infrastructure.mapper.GcRedemptionMapper;
import com.gtech.gvcore.helper.GvCodeHelper;
import com.gtech.gvcore.threadpool.ThreadPoolCenter;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import tk.mybatis.mapper.entity.Example;
import tk.mybatis.mapper.weekend.WeekendSqls;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.gtech.gvcore.aop.IssueHandlerTransactionAspect.summarizeCompletedIssue;

@Component
@Slf4j
public class GcCancelRedeemIssueHandlingProgram implements IssueHandlerTransactionAspect.IssueHandlingTransactionDataProgram {

    @Autowired
    GiftCardApplicationService gcService;
    @Autowired
    private GvCodeHelper gcCodeHelper;
    @Autowired
    private GcBalanceAdjustmentMapper gcBalanceAdjustmentMapper;
    @Autowired
    private GcRedemptionMapper gcRedemptionMapper;

    @Override
    public IssueHandlingTypeEnum getIssueHandlingType() {
        return IssueHandlingTypeEnum.GC_CANCEL_REDEEM;
    }

    @Override
    public void execute(final Collection<IssueHandlingDetails> issueHandlingDetailsList) {
        List<String> cardNumbers = summarizeCompletedIssue(issueHandlingDetailsList);
        List<GiftCardEntity> giftCardEntities = this.gcService.queryByCardNumberList(null, cardNumbers);
        int manageSize = 500;
        List<IssueHandlingDetails> list = (List<IssueHandlingDetails>) issueHandlingDetailsList;
        final Map<String, GiftCardEntity> giftCardEntityMap = giftCardEntities
                .stream().collect(Collectors.toMap(GiftCardEntity::getCardNumber, Function.identity(), (v1, v2) -> v2));
        if (issueHandlingDetailsList.size() > manageSize) {
            int count = issueHandlingDetailsList.size() / manageSize + (issueHandlingDetailsList.size() % manageSize == 0 ? 0 : 1);
            List<IssueHandlingDetails> issueHandlingDetails = new ArrayList<>(issueHandlingDetailsList);
            for (int i = 0; i < count; i++) {

                int fromIndex = i * manageSize;
                int toIndex = (i + 1) * manageSize;
                List<IssueHandlingDetails> saveData = issueHandlingDetails.subList(fromIndex, Math.min(toIndex, issueHandlingDetails.size()));

                ThreadPoolCenter.commonThreadPoolExecute(() -> storeCancelRedeem(new ArrayList<>(saveData), giftCardEntityMap));
            }
        } else {
            storeCancelRedeem(list, giftCardEntityMap);
        }
    }

    public void storeCancelRedeem(List<IssueHandlingDetails> details, final Map<String, GiftCardEntity> giftCardEntityMap) {
        Date createTime = new Date();
        for (IssueHandlingDetails detail : details) {
            //get balance adjustment record
            GcBalanceAdjustmentEntity adjustmentEntity = new GcBalanceAdjustmentEntity();
            adjustmentEntity.setInvoiceNumber(detail.getInvoiceNo());
            adjustmentEntity.setCardNumber(detail.getVoucherCode());
            adjustmentEntity.setAdjustmentType(IssueHandlingTypeEnum.GC_CANCEL_REDEEM.code());
            adjustmentEntity = gcBalanceAdjustmentMapper.selectOne(adjustmentEntity);
            //add redemption record
            GiftCardEntity giftCard = giftCardEntityMap.get(detail.getVoucherCode());
            GcRedemptionEntity gcRedemptionEntity = new GcRedemptionEntity();
            gcRedemptionEntity.setRedemptionCode(gcCodeHelper.generateRedemptionCode());
            gcRedemptionEntity.setCardNumber(detail.getVoucherCode());
            gcRedemptionEntity.setIssuerCode(giftCard.getIssuerCode());
            if (null != adjustmentEntity) {
                gcRedemptionEntity.setOutletCode(adjustmentEntity.getOutletCode());
                gcRedemptionEntity.setMerchantCode(adjustmentEntity.getMerchantCode());
                gcRedemptionEntity.setBalanceBefore(adjustmentEntity.getBalanceBefore());
                gcRedemptionEntity.setBalanceAfter(adjustmentEntity.getBalanceAfter());
            } else {
                log.info("balance adjustment record doesn't exist, omit saving some redemption fields, invoiceNumber:{}, voucherCode:{}", detail.getInvoiceNo(), detail.getVoucherCode());
            }
            gcRedemptionEntity.setAmount(detail.getAmount());
            gcRedemptionEntity.setCpgCode(giftCard.getCpgCode());
            gcRedemptionEntity.setInvoiceNumber(detail.getInvoiceNo());
            gcRedemptionEntity.setRedemptionTime(createTime);
            //gcRedemptionEntity.setRedeemedBy(detail.getCreateUser());
            gcRedemptionEntity.setStatus(RedemptionStatusEnum.CANCELLED_REDEEMED.getCode());
            gcRedemptionEntity.setDenomination(giftCard.getDenomination());
            gcRedemptionMapper.insertSelective(gcRedemptionEntity);
            WeekendSqls<GcRedemptionEntity> weekendSqls = WeekendSqls.custom();
            weekendSqls.andEqualTo(GcRedemptionEntity::getInvoiceNumber, detail.getInvoiceNo())
                    .andEqualTo(GcRedemptionEntity::getStatus, RedemptionStatusEnum.REDEEMED.getCode())
                    .andEqualTo(GcRedemptionEntity::getCardNumber, detail.getVoucherCode());
            Example example = Example.builder(GcRedemptionEntity.class).where(weekendSqls).build();
            GcRedemptionEntity updEntity = new GcRedemptionEntity();
            updEntity.setRedemptionCanceled(1);
            gcRedemptionMapper.updateByConditionSelective(updEntity, example);
        }
    }
}