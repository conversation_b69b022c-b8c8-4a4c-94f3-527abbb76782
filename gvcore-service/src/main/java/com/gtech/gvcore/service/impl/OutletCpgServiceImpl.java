package com.gtech.gvcore.service.impl;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.gtech.commons.result.PageResult;
import com.gtech.commons.result.Result;
import com.gtech.commons.utils.BeanCopyUtils;
import com.gtech.gvcore.common.constants.GvcoreConstants;
import com.gtech.gvcore.common.enums.ResultErrorCodeEnum;
import com.gtech.gvcore.common.request.outletcpg.*;
import com.gtech.gvcore.common.response.outletcpg.OutletCpgResponse;
import com.gtech.gvcore.dao.mapper.CpgMapper;
import com.gtech.gvcore.dao.mapper.OutletCpgMapper;
import com.gtech.gvcore.dao.model.Cpg;
import com.gtech.gvcore.dao.model.OutletCpg;
import com.gtech.gvcore.giftcard.masterdata.gcpg.dao.GcCpgMapper;
import com.gtech.gvcore.giftcard.masterdata.gcpg.entity.GcCpg;
import com.gtech.gvcore.helper.GvCodeHelper;
import com.gtech.gvcore.service.OutletCpgService;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.dao.DuplicateKeyException;
import org.springframework.stereotype.Service;
import tk.mybatis.mapper.entity.Example;

import java.util.ArrayList;
import java.util.Collections;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @Date 2022/2/21 11:48
 */
@Service
public class OutletCpgServiceImpl implements OutletCpgService {
    @Autowired
    private OutletCpgMapper outletCpgMapper;

    @Autowired
    private GvCodeHelper codeHelper;
    
    @Autowired
    private CpgMapper cpgMapper;
    
    @Autowired
    private GcCpgMapper gcCpgMapper;

    @Override
    public Result<Void> createOutletCpg(CreateOutletCpgRequest param) {
        OutletCpg entity = BeanCopyUtils.jsonCopyBean(param, OutletCpg.class);
        entity.setOutletCpgCode(codeHelper.generateOutletCpgCode());
        entity.setCreateTime(new Date());
        //默认开启
        entity.setStatus(GvcoreConstants.STATUS_ENABLE);


        Map<String, String> typeMap = getCpgTypesMap(Collections.singletonList(entity.getCpgCode()));
        String cpgType = typeMap.getOrDefault(entity.getCpgCode(), "voucher");
        entity.setCpgType(cpgType);

        
        try {
            outletCpgMapper.insertSelective(entity);
        } catch (DuplicateKeyException e) {
            return Result.failed(ResultErrorCodeEnum.DATA_ALREADY_EXISTS.code(),
                    ResultErrorCodeEnum.DATA_ALREADY_EXISTS.desc());
        }
        return Result.ok();
    }
    
    /**
     * 查询CPG的类型
     * 通过查询Cpg表和GcCpg表来确定CPG类型
     * 
     * @param cpgCodeList CPG编码列表
     * @return CPG编码->类型的映射
     */
    private Map<String, String> getCpgTypesMap(List<String> cpgCodeList) {
        Map<String, String> result = new HashMap<>();
        if (CollectionUtils.isEmpty(cpgCodeList)) {
            return result;
        }
        
        // 批量查询voucher类型的CPG
        Example voucherExample = new Example(Cpg.class);
        voucherExample.createCriteria().andIn("cpgCode", cpgCodeList);
        List<Cpg> voucherCpgs = cpgMapper.selectByCondition(voucherExample);
        
        // 将所有voucher类型的CPG加入结果
        if (CollectionUtils.isNotEmpty(voucherCpgs)) {
            for (Cpg cpg : voucherCpgs) {
                result.put(cpg.getCpgCode(), "voucher");
            }
        }
        
        // 查找所有未匹配的CPG编码
        List<String> unmatchedCodes = new ArrayList<>(cpgCodeList);
        unmatchedCodes.removeAll(result.keySet());
        
        // 如果还有未匹配的CPG编码，查询gc类型的CPG
        if (CollectionUtils.isNotEmpty(unmatchedCodes)) {
            Example gcExample = new Example(GcCpg.class);
            gcExample.createCriteria().andIn("cpgCode", unmatchedCodes);
            List<GcCpg> gcCpgs = gcCpgMapper.selectByCondition(gcExample);
            
            // 将所有gc类型的CPG加入结果
            if (CollectionUtils.isNotEmpty(gcCpgs)) {
                for (GcCpg gcCpg : gcCpgs) {
                    result.put(gcCpg.getCpgCode(), "gc");
                }
            }
        }
        
        return result;
    }

    @Override
    public Result<Void> updateOutletCpg(UpdateOutletCpgRequest param) {
        OutletCpg entity = BeanCopyUtils.jsonCopyBean(param, OutletCpg.class);
        entity.setUpdateTime(new Date());

        Example example = new Example(OutletCpg.class);
        example.createCriteria()
                .andEqualTo(OutletCpg.C_OUTLET_CPG_CODE,param.getOutletCpgCode());

        Map<String, String> typeMap = getCpgTypesMap(Collections.singletonList(entity.getCpgCode()));
        String cpgType = typeMap.getOrDefault(entity.getCpgCode(), "voucher");
        entity.setCpgType(cpgType);

        try {
            outletCpgMapper.updateByConditionSelective(entity,example);
        } catch (DuplicateKeyException e) {
            return Result.failed(ResultErrorCodeEnum.DATA_ALREADY_EXISTS.code(),
                    ResultErrorCodeEnum.DATA_ALREADY_EXISTS.desc());
        }
        return Result.ok();
    }

    @Override
    public Result<Void> deleteOutletCpg(DeleteOutletCpgRequest param) {
        Example example = new Example(OutletCpg.class);
        example.createCriteria()
                .andEqualTo(OutletCpg.C_OUTLET_CPG_CODE,param.getOutletCpgCode());
        outletCpgMapper.deleteByCondition(example);

        return Result.ok();
    }

    @Override
    public Result<Void> deleteOutletCpgByOutletCode(DeleteOutletCpgByOutletCodeRequest param) {

        Example example = new Example(OutletCpg.class);
        example.createCriteria()
                .andEqualTo(OutletCpg.C_OUTLET_CODE,param.getOutletCode());
        outletCpgMapper.deleteByCondition(example);
        return Result.ok();
    }

    @Override
    public PageResult<OutletCpgResponse> queryOutletCpgList(QueryOutletCpgRequest param) {

        PageHelper.startPage(param.getPageNum(),param.getPageSize());

        Example example = new Example(OutletCpg.class);
        Example.Criteria criteria = example.createCriteria()
                .andEqualTo(OutletCpg.C_OUTLET_CPG_CODE, param.getOutletCpgCode())
                .andEqualTo(OutletCpg.C_CPG_CODE, param.getCpgCode())
                .andEqualTo(OutletCpg.C_OUTLET_CODE, param.getOutletCode())
                .andEqualTo(OutletCpg.C_STATUS, param.getStatus());
                
        // 添加CPG类型条件
        if (StringUtils.isNotBlank(param.getCpgType())) {
            criteria.andEqualTo(OutletCpg.C_CPG_TYPE, param.getCpgType());
        }
        
        //创建时间倒序
        example.orderBy(OutletCpg.C_CREATE_TIME).desc();

        List<OutletCpg> gvOutletCpgEntities = outletCpgMapper.selectByCondition(example);
        PageInfo<OutletCpg> info = PageInfo.of(gvOutletCpgEntities);

        return new PageResult<>(BeanCopyUtils.jsonCopyList(info.getList(),OutletCpgResponse.class),info.getTotal());
    }

    @Override
    public OutletCpgResponse getOutletCpg(GetOutletCpgRequest param) {
        OutletCpg entity = BeanCopyUtils.jsonCopyBean(param, OutletCpg.class);
        OutletCpg outletCpg = outletCpgMapper.selectOne(entity);

        return BeanCopyUtils.jsonCopyBean(outletCpg,OutletCpgResponse.class);
    }

    @Override
    public List<OutletCpgResponse> queryOutletCpgListByOutlet(String outletCode) {
        return outletCpgMapper.queryOutletCpgListByOutlet(outletCode);
    }

    @Override
    public List<OutletCpgResponse> queryOutletCpgListByOutletList(List<String> outletCodeList) {
        return outletCpgMapper.queryOutletCpgListByOutletList(outletCodeList);
    }
}
