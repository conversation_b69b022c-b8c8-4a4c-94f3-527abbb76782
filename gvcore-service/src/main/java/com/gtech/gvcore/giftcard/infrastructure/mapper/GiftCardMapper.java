package com.gtech.gvcore.giftcard.infrastructure.mapper;

import com.gtech.commons.dao.mapper.GTechBaseMapper;
import com.gtech.gvcore.giftcard.infrastructure.entity.GiftCardEntity;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;
import java.util.Optional;
import java.util.Set;

import org.apache.ibatis.annotations.Update;
import tk.mybatis.mapper.entity.Example;
import tk.mybatis.mapper.weekend.Weekend;

/**
 * 礼品卡数据访问接口
 */
@Mapper
public interface GiftCardMapper extends GTechBaseMapper<GiftCardEntity> {

    /**
     * 根据卡号查询礼品卡
     */
    @Select("SELECT * FROM gc_gift_card WHERE card_number = #{cardNumber}")
    GiftCardEntity selectByCardNumber(@Param("cardNumber") String cardNumber);

    /**
     * 查询所有卡号
     */
    @Select("SELECT card_number FROM gc_gift_card")
    Set<String> selectAllCardNumbers();

    /**
     * 根据前缀查询卡号
     */
    @Select("SELECT card_number FROM gc_gift_card WHERE card_number LIKE CONCAT(#{prefix}, '%')")
    Set<String> selectCardNumbersByPrefix(@Param("prefix") String prefix);

    //todo 等待一恒确认，加入outlet更新sql
    @Update("UPDATE gc_gift_card set balance = balance + #{balance}" +
            " WHERE card_number = #{cardNumber}")
    int updateBalanceByCardNumber(GiftCardEntity entity);

    @Update("" +
            "<script>" +
            "UPDATE gc_gift_card set sales_time = null, sales_outlet = null, management_status = 'DESTROY'" +
            " WHERE card_number in " +
            "<foreach collection=\"cardNumbers\" item=\"cardNumber\" index=\"index\" open=\"(\" close=\")\" separator=\",\">" +
            "       #{cardNumber} " +
            "      </foreach> " +
            " </script>")
    void setSalesNull(@Param("cardNumbers") List<String> cardNumbers);
}