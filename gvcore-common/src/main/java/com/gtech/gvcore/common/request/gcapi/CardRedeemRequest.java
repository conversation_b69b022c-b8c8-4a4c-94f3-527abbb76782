package com.gtech.gvcore.common.request.gcapi;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.gtech.gvcore.common.response.gc.GiftCardInfo;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * Redemption Request
 */
@Data
@ApiModel(value = "CardRedeemRequest", description = "Redemption Request")
public class CardRedeemRequest {


    @ApiModelProperty(value = "Redemption Amount", notes = "Amount to be redeemed", example = "100.00", required = true, position = 2)
    private Double amount;

    @ApiModelProperty(value = "Gift Card List", notes = "List of gift cards to be redeemed", required = true, position = 3)
    private List<GiftCardInfo> giftCards;

    @ApiModelProperty(value = "Transaction Amount", notes = "Total transaction amount", example = "500.00", required = true, position = 4)
    private BigDecimal transactionAmount;

    @ApiModelProperty(value = "Invoice Number", notes = "Invoice number", example = "INV-12345", required = true, position = 3)
    private String invoiceNumber;

    @ApiModelProperty(value = "Transaction ID", notes = "Unique number", example = "12345", required = true, position = 4)
    private Integer transactionId;

    @ApiModelProperty(value = "Client Time", notes = "Timestamp of client application in YYYY-MM-DDTHH:MM:SS format", example = "2023-01-01T12:00:00", required = true, position = 5)
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd'T'HH:mm:ss")
    private Date clientTime;

    @ApiModelProperty(value = "Notes", notes = "Reference text", example = "Customer redemption", position = 6)
    private String notes;


} 