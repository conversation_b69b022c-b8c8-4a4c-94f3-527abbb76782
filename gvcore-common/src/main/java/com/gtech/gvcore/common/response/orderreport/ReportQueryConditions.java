package com.gtech.gvcore.common.response.orderreport;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2022/4/22 15:42
 */

@Getter
@Setter
@Accessors(chain = true)
@ApiModel(value = "QueryConditions")
public class ReportQueryConditions implements Serializable {
    private static final long serialVersionUID = 4765796525060494288L;
    public static final String SEPARATOR = ",";

    @ApiModelProperty(value = "Report type")
    private Integer reportType;

    @ApiModelProperty(value = "Create user")
    private String createUser;

    @ApiModelProperty(value = "Create user email")
    private String createUserEmail;//! 需要从接收邮件里获取


    @ApiModelProperty(value = "Issuer code", example = "IS102203031638001252")
    private String issuerCode;

    @ApiModelProperty(value = "Company code")
    private List<String> companyCodes;

    @ApiModelProperty(value = "Merchant code")
    private List<String> merchantCodes;

    @ApiModelProperty(value = "Outlet code")
    private List<String> outletCodes;

    @ApiModelProperty(value = "outbound Outlet code")
    private List<String> outboundCodeList;

    @ApiModelProperty(value = "inbound Outlet code")
    private List<String> inboundCodeList;


    @ApiModelProperty(value = "Customer type")
    private String customerType;

    @ApiModelProperty(value = "Customer code")
    private List<String> customerCodes;

    @ApiModelProperty(name = "contact_division")
    private String contactDivision;


    @ApiModelProperty(value = "Voucher code")
    private String voucherCode;

    @ApiModelProperty(value = "voucherStatus")
    private List<String> voucherStatus;

    public String getVoucherStatus() {
        return CollectionUtils.isEmpty(voucherStatus) ? StringUtils.EMPTY : String.join(SEPARATOR, voucherStatus);
    }

    @ApiModelProperty(value = "Voucher code num start")
    private String voucherCodeNumStart;

    @ApiModelProperty(value = "Voucher code number end")
    private String voucherCodeNumEnd;

    @ApiModelProperty(value = "Voucher effective Date start")
    private Date voucherEffectiveDateStart;

    @ApiModelProperty(value = "Voucher effective date end")
    private Date voucherEffectiveDateEnd;


    @ApiModelProperty(value = "Booklet status")
    private List<String> bookletStatus;

    public String getBookletStatus() {
        return CollectionUtils.isEmpty(bookletStatus) ? StringUtils.EMPTY : String.join(SEPARATOR, bookletStatus);
    }

    @ApiModelProperty(value = "Booklet start")
    private String bookletStart;

    @ApiModelProperty(value = "Booklet end")
    private String bookletEnd;


    @ApiModelProperty(value = "Transaction date start")
    private Date transactionDateStart;

    @ApiModelProperty(value = "Transaction date end")
    private Date transactionDateEnd;

    @ApiModelProperty(value = "Transaction type")
    private List<String> transactionTypes;

    @ApiModelProperty(value = "Transaction status")
    private String transactionStatus;

    @ApiModelProperty(value = "Transaction Corporate Name.")
    private String transactionCorporateName;


    @ApiModelProperty(value = "Purchase order number")
    private String purchaseOrderNo;

    @ApiModelProperty(value = "Invoice number")
    private String invoiceNo;

    @ApiModelProperty(value = "Voucher program group code")
    private List<String> cpgCodes;

    @ApiModelProperty(value = "Order status")
    private List<String> orderStatuses;


    @ApiModelProperty(value = "操作时间起始")
    private Date operateTimeBegin;

    @ApiModelProperty(value = "操作时间截止")
    private Date operateTimeEnd;

    @ApiModelProperty(value = "操作人编码")
    private String operateUserCode;

    @ApiModelProperty(value = "操作人名称")
    private String operateUserName;

    @ApiModelProperty(value = "sysLoggerId")
    private String sysLoggerId;

    @ApiModelProperty(value = "uploadedFileUrl")
    private String uploadedFileUrl;

    @ApiModelProperty(value = "uploadedFileName")
    private String uploadedFileName;


    @ApiModelProperty(value = "voucherRequestId (卡券移动的请求id)")
    private String voucherRequestId;

    @ApiModelProperty(value = "voucherRequestSourceList")
    private List<String> voucherRequestSourceList;

    @ApiModelProperty(value = "voucherAllocationStatusList")
    private List<String> voucherAllocationStatusList;

    @ApiModelProperty(value = "voucherPrintingStatusList") //Voucher Printing Report -> Status
    private List<String> voucherPrintingStatusList;

    @ApiModelProperty(value = "printingVendorList")// Voucher Printing Report - > Printing Vendor
    private List<String> printingVendorList;

    @ApiModelProperty(value = "voucherReturnTransferStatusList")// Voucher Return & Transfer Report -> Status
    private List<String> voucherReturnTransferStatusList;

    @ApiModelProperty(value = "voucherReturnTransferFromStoreList")// Voucher Return & Transfer Report -> From/To Store Name
    private List<String> voucherReturnTransferFromStoreList;

    @ApiModelProperty(value = "voucherReturnTransferToStoreList")// Voucher Return & Transfer Report -> From/To Store Name
    private List<String> voucherReturnTransferToStoreList;

    @ApiModelProperty(value = "webReturnTransferFromTo(前端为了回显使用的字段后端不做处理)")
    private String webReturnTransferFromTo;

}
