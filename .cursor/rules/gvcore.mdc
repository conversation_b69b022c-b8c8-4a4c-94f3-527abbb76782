---
description: 
globs: 
alwaysApply: true
---

# gvcore rule
- 用中文回答
- 我的技术栈是 springboot + mybatis
- 常用的查询数据库的方式是
    Weekend<Voucher> selectVoucherW = Weekend.of(Voucher.class);
            selectVoucherW.weekendCriteria().andIn(Voucher::getVoucherCode,voucherCodes);
            List<Voucher> vouchers = voucherMapper.selectByCondition(selectVoucherW);
- 除非是特别复杂的 sql 否则不要手写 sql 语句
- sql 查询优先使用 tk.mybatis 的能力

