package com.gtech.gvcore.giftcard.domain.service;

import com.gtech.gvcore.giftcard.application.dto.RedemptionDTO;
import com.gtech.gvcore.giftcard.domain.model.GcRedemptionRecord;
import com.gtech.gvcore.giftcard.domain.repository.GcRedemptionRepository;
import com.gtech.gvcore.helper.GvCodeHelper;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

@Service
public class GcRedemptionDomainService {

    @Autowired
    private GcRedemptionRepository redemptionRepository;

    @Autowired
    private GvCodeHelper gvCodeHelper;


    @Transactional
    public RedemptionDTO recordRedemption(RedemptionDTO redemptionDTO) {


        // 创建使用记录
        GcRedemptionRecord gcRedemptionRecord = new GcRedemptionRecord(
                gvCodeHelper.generateRedemptionCode(),
                redemptionDTO.getCardNumber(),
                redemptionDTO.getCpgCode(),
                redemptionDTO.getAmount(),
                redemptionDTO.getBalanceBefore(),
                redemptionDTO.getOutletCode(),
                redemptionDTO.getIssuerCode(),
                redemptionDTO.getMerchantCode(),
                redemptionDTO.getInvoiceNumber(),
                redemptionDTO.getApprovalCode(),
                redemptionDTO.getNotes(),
                redemptionDTO.getBatchNumber()
        );

        redemptionRepository.save(gcRedemptionRecord);

        // 返回使用结果
        redemptionDTO.setBalanceBefore(gcRedemptionRecord.getBalanceBefore());
        redemptionDTO.setBalanceAfter(gcRedemptionRecord.getBalanceAfter());
        redemptionDTO.setRedemptionTime(gcRedemptionRecord.getRedemptionTime());
        redemptionDTO.setStatus(gcRedemptionRecord.getStatus().name());

        return redemptionDTO;
    }


    @Transactional
    public int recordRedemption(List<RedemptionDTO> redemptionDTOs) {
        if (redemptionDTOs == null || redemptionDTOs.isEmpty()) {
            return 0;
        }
        List<GcRedemptionRecord> recordArrayList = new ArrayList<>();
        for (RedemptionDTO redemptionDTO : redemptionDTOs) {
            // 创建使用记录
            GcRedemptionRecord gcRedemptionRecord = new GcRedemptionRecord(
                    gvCodeHelper.generateRedemptionCode(),
                    redemptionDTO.getCardNumber(),
                    redemptionDTO.getCpgCode(),
                    redemptionDTO.getAmount(),
                    redemptionDTO.getBalanceBefore(),
                    redemptionDTO.getOutletCode(),
                    redemptionDTO.getIssuerCode(),
                    redemptionDTO.getMerchantCode(),
                    redemptionDTO.getInvoiceNumber(),
                    redemptionDTO.getApprovalCode(),
                    redemptionDTO.getNotes(),
                    redemptionDTO.getBatchNumber()
            );
            recordArrayList.add(gcRedemptionRecord);
        }
        return redemptionRepository.insertList(recordArrayList);
    }

    public List<GcRedemptionRecord> getRedemptionsByCardNumber(String cardNumber) {
        if (StringUtils.isBlank(cardNumber)) return Collections.emptyList();
        return redemptionRepository.findByCardNumber(cardNumber);
    }

    public GcRedemptionRecord getRedemption(String cardNumber, String invoiceNumber) {
        if (StringUtils.isBlank(cardNumber) || StringUtils.isBlank(invoiceNumber)) return null;
        return redemptionRepository.getRedemption(cardNumber, invoiceNumber);
    }

    public List<GcRedemptionRecord> getRedemptionsByBatchNumber(String batchNumberInteger) {
        if (batchNumberInteger == null) return Collections.emptyList();
        return redemptionRepository.findByBatchNumber(batchNumberInteger);
    }
}
