package com.gtech.gvcore.service.report.impl.support.gclife.builder;

import com.gtech.commons.utils.DateUtil;
import com.gtech.commons.utils.StringUtil;
import com.gtech.gvcore.common.enums.ReportExportTypeEnum;
import com.gtech.gvcore.giftcard.infrastructure.entity.GiftCardEntity;
import com.gtech.gvcore.giftcard.infrastructure.entity.GcSalesEntity;
import com.gtech.gvcore.giftcard.infrastructure.mapper.GiftCardMapper;
import com.gtech.gvcore.giftcard.infrastructure.mapper.GcSalesMapper;
import com.gtech.gvcore.giftcard.masterdata.gcpg.entity.GcCpg;
import com.gtech.gvcore.service.report.impl.bean.GcLifeCycleBean;
import com.gtech.gvcore.service.report.impl.param.GcLifeCycleQueryData;
import com.gtech.gvcore.service.report.impl.support.gclife.GcLifeCycleAbstract;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.Collections;
import java.util.Comparator;
import java.util.Date;
import java.util.List;
import java.util.Optional;

/**
 * Gift Card Life Cycle Card Builder
 *
 * <p>负责构建礼品卡生命周期报表的主要信息，包括：
 * <ul>
 *   <li>礼品卡基本信息（卡号、程序组、状态等）</li>
 *   <li>时间信息（发行日期、激活日期、过期日期等）</li>
 *   <li>金额信息（面额、余额）</li>
 * </ul>
 *
 * <AUTHOR> based on VoucherLifeCycleVoucherBuilder
 * @version V1.0
 * @date 2025年6月19日
 */
@Slf4j
@Service
public class GcLifeCycleCardBuilder extends GcLifeCycleAbstract<GcLifeCycleBean> {

    private final GiftCardMapper giftCardMapper;
    private final GcSalesMapper gcSalesMapper;


    @Autowired
    public GcLifeCycleCardBuilder(GiftCardMapper giftCardMapper, GcSalesMapper gcSalesMapper) {
        this.giftCardMapper = giftCardMapper;
        this.gcSalesMapper = gcSalesMapper;
    }

    @Override
    public ReportExportTypeEnum exportTypeEnum() {
        return ReportExportTypeEnum.Gc_CARD_LIFE_CYCLE_REPORT;
    }

    @Override
    public Class<?> getExportDataClass() {
        return GcLifeCycleBean.class;
    }

    @Override
    public String getFillKey() {
        return "cycle";
    }

    @Override
    public List<GcLifeCycleBean> builder(GcLifeCycleQueryData queryData) {

        // 1. 查询礼品卡信息
        GiftCardEntity giftCard = giftCardMapper.selectByCardNumber(queryData.getCardNumber());

        if (null == giftCard) return Collections.emptyList();

        // 2. 查询销售交易记录（使用单表查询）
        List<GcSalesEntity> salesList = gcSalesMapper.selectByCardNumber(giftCard.getCardNumber());

        // 获取最后一次销售交易
        Optional<GcSalesEntity> latestSales = getLatestSalesRecord(salesList);

        // 3. 查询相关主数据
        GcCpg gcCpg = super.nonNullGetByCode(giftCard.getCpgCode(), GcCpg.class);


        // 4. 组装数据
        GcLifeCycleBean cycleBean = buildLifeCycleBean(giftCard, gcCpg);

        return Collections.singletonList(cycleBean);
    }

    /**
     * 获取最新的销售记录
     */
    private Optional<GcSalesEntity> getLatestSalesRecord(List<GcSalesEntity> salesList) {
        if (CollectionUtils.isEmpty(salesList)) {
            return Optional.empty();
        }

        return salesList.stream()
                .filter(sales -> sales != null && sales.getCreateTime() != null)
                .max(Comparator.comparing(GcSalesEntity::getCreateTime));
    }

    /**
     * 构建生命周期Bean
     */
    private GcLifeCycleBean buildLifeCycleBean(GiftCardEntity giftCard, GcCpg gcCpg) {
        GcLifeCycleBean cycleBean = new GcLifeCycleBean();

        // 基本信息
        setBasicInfo(cycleBean, giftCard, gcCpg);

        // 时间信息
        setTimeInfo(cycleBean, giftCard, gcCpg);

        // 金额信息
        setAmountInfo(cycleBean, giftCard);

        return cycleBean;
    }

    /**
     * 设置基本信息
     */
    private void setBasicInfo(GcLifeCycleBean cycleBean, GiftCardEntity giftCard, GcCpg gcCpg) {
        cycleBean.setGiftCardNumber(giftCard.getCardNumber());
        cycleBean.setGiftCardProgram(gcCpg.getCpgName());
        cycleBean.setGiftCardStatus(giftCard.getStatus());
    }

    /**
     * 设置时间信息
     */
    private void setTimeInfo(GcLifeCycleBean cycleBean, GiftCardEntity giftCard, GcCpg gcCpg) {
        // 发行日期
        setFormattedDate(cycleBean::setIssuanceDate, giftCard.getCreateTime());

        // 激活日期
        setFormattedDate(cycleBean::setActivationDate, giftCard.getActivationTime());

        // 激活截止日期
        setFormattedDate(cycleBean::setActivationEnded, giftCard.getActivationDeadline());

        // 过期日期
        setFormattedDate(cycleBean::setExpiryDate, giftCard.getExpiryTime());

        //todo 宽限期结束日期
//        setGracePeriodEnded(giftCard, gcCpg, cycleBean);
    }

    /**
     * 设置金额信息
     */
    private void setAmountInfo(GcLifeCycleBean cycleBean, GiftCardEntity giftCard) {
        cycleBean.setDenomination(super.toAmount(giftCard.getDenomination()));
        cycleBean.setRemainingBalance(super.toAmount(giftCard.getBalance()));
    }

    /**
     * 设置格式化日期的通用方法
     */
    private void setFormattedDate(java.util.function.Consumer<String> setter, Date date) {
        if (date != null) {
            setter.accept(DateUtil.format(date, DateUtil.FORMAT_YYYYMMDDHHMISS));
        }
    }


    /**
     * 解析宽限期天数
     */
    private int parseGracePeriodDays(String gracePeriod) {
        // 支持多种格式：纯数字、带单位等
        String cleanPeriod = gracePeriod.trim().replaceAll("[^0-9]", "");
        if (StringUtil.isEmpty(cleanPeriod)) {
            throw new NumberFormatException("No numeric value found in grace period: " + gracePeriod);
        }
        return Integer.parseInt(cleanPeriod);
    }


}
