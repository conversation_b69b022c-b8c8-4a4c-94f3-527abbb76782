package com.gtech.gvcore.giftcard.domain.service;

import com.gtech.gvcore.giftcard.application.dto.BalanceAdjustmentDTO;
import com.gtech.gvcore.giftcard.domain.model.GcBalanceAdjustmentRecord;
import com.gtech.gvcore.giftcard.domain.model.GiftCard;
import com.gtech.gvcore.giftcard.domain.repository.GcBalanceAdjustmentRepository;
import com.gtech.gvcore.giftcard.domain.repository.GiftCardRepository;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;

@Service
public class GcAdjustBalanceDomainService {
    
    @Autowired
    private GiftCardRepository giftCardRepository;
    
    @Autowired
    private GcBalanceAdjustmentRepository adjustmentRepository;


    @Transactional
    public BalanceAdjustmentDTO adjustBalance(BalanceAdjustmentDTO adjustmentDTO) {
        // 查询礼品卡
        GiftCard giftCard = giftCardRepository.findByCardNumber(adjustmentDTO.getCardNumber())
                .orElseThrow(() -> new IllegalArgumentException("礼品卡不存在: " + adjustmentDTO.getCardNumber()));

        // 记录调整前余额用于创建调整记录
        BigDecimal balanceBefore = giftCard.getBalance();
        
        // 调整余额
        if (adjustmentDTO.getAdjustmentAmount().compareTo(BigDecimal.ZERO) > 0) {
            // 增加余额
            giftCard.addBalance(adjustmentDTO.getAdjustmentAmount());
        } else if (adjustmentDTO.getAdjustmentAmount().compareTo(BigDecimal.ZERO) < 0) {
            // 扣减余额
            try {
                giftCard.deduct(adjustmentDTO.getAdjustmentAmount().abs());
            } catch (IllegalArgumentException e) {
                throw new IllegalArgumentException("余额不足，无法扣减: 当前余额=" + balanceBefore + 
                        ", 扣减金额=" + adjustmentDTO.getAdjustmentAmount().abs());
            }
        }
        
        // 保存礼品卡
        giftCardRepository.save(giftCard);

        // 创建余额调整记录
        GcBalanceAdjustmentRecord adjustmentRecord = new GcBalanceAdjustmentRecord(
                adjustmentDTO.getCardNumber(),
                adjustmentDTO.getAdjustmentAmount(),
                balanceBefore,
                adjustmentDTO.getAdjustmentType(),
                adjustmentDTO.getReason(),
                adjustmentDTO.getAdjustedBy()
        );

        adjustmentRepository.save(adjustmentRecord);

        // 返回调整结果
        adjustmentDTO.setId(adjustmentRecord.getId());
        adjustmentDTO.setBalanceBefore(adjustmentRecord.getBalanceBefore());
        adjustmentDTO.setBalanceAfter(adjustmentRecord.getBalanceAfter());
        adjustmentDTO.setAdjustmentTime(adjustmentRecord.getAdjustmentTime());

        return adjustmentDTO;
    }
}
