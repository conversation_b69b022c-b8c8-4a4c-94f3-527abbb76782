package com.gtech.gvcore.giftcard.infrastructure.mapper;

import com.gtech.commons.dao.mapper.GTechBaseMapper;
import com.gtech.gvcore.giftcard.infrastructure.entity.GcRedemptionEntity;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * 礼品卡使用记录数据访问接口
 */
@Mapper
public interface GcRedemptionMapper extends GTechBaseMapper<GcRedemptionEntity> {

    /**
     * 根据卡号查询使用记录
     */
    @Select("SELECT * FROM gc_redemption WHERE card_number = #{cardNumber}")
    List<GcRedemptionEntity> selectByCardNumber(@Param("cardNumber") String cardNumber);

    /**
     * 根据商户ID查询使用记录
     */
    @Select("SELECT * FROM gc_redemption WHERE merchant_id = #{merchantId}")
    List<GcRedemptionEntity> selectByMerchantId(@Param("merchantId") String merchantId);

    /**
     * 根据发票号查询使用记录
     */
    @Select("SELECT * FROM gc_redemption WHERE invoice_number = #{invoiceNumber}")
    List<GcRedemptionEntity> selectByInvoiceNumber(@Param("invoiceNumber") String invoiceNumber);

    /**
     * 根据POS批次号查询使用记录
     */
    @Select("SELECT * FROM gc_redemption WHERE pos_batch_number = #{posBatchNumber}")
    List<GcRedemptionEntity> selectByPosBatchNumber(@Param("posBatchNumber") String posBatchNumber);
} 