-- 创建礼品卡延长激活期限记录表
CREATE TABLE gc_activation_extension (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    card_number VARCHAR(50) NOT NULL,
    extension_code VARCHAR(50) NOT NULL UNIQUE,
    extension_time TIMESTAMP NOT NULL,
    old_activation_deadline TIMESTAMP NOT NULL,
    new_activation_deadline TIMESTAMP NOT NULL,
    issuer_code VA<PERSON><PERSON>R(50),
    outlet_code VA<PERSON><PERSON><PERSON>(50),
    merchant_code VARCHAR(50),
    invoice_number VARCHAR(100),
    approval_code VARCHAR(50),
    notes TEXT,
    batch_number VARCHAR(50),
    source VARCHAR(20) NOT NULL DEFAULT 'API' COMMENT 'Record source: API, MANUAL, SYSTEM, etc.',
    create_time TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT 'create time',
    update_time TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT 'update time',
    CONSTRAINT chk_new_deadline_after_old CHECK (new_activation_deadline > old_activation_deadline)
);

-- 创建索引
CREATE INDEX idx_gc_extension_card_number ON gc_activation_extension(card_number);
CREATE INDEX idx_gc_extension_extension_code ON gc_activation_extension(extension_code);
CREATE INDEX idx_gc_extension_extension_time ON gc_activation_extension(extension_time);
CREATE INDEX idx_gc_extension_outlet_code ON gc_activation_extension(outlet_code);

-- MySQL不需要手动创建更新时间触发器，ON UPDATE CURRENT_TIMESTAMP已经处理

-- 添加注释
COMMENT ON TABLE gc_activation_extension IS '礼品卡延长激活期限记录表';
COMMENT ON COLUMN gc_activation_extension.id IS '主键ID';
COMMENT ON COLUMN gc_activation_extension.card_number IS '礼品卡号';
COMMENT ON COLUMN gc_activation_extension.extension_code IS '延长操作流水号';
COMMENT ON COLUMN gc_activation_extension.extension_time IS '延长操作时间';
COMMENT ON COLUMN gc_activation_extension.old_activation_deadline IS '原激活截止时间';
COMMENT ON COLUMN gc_activation_extension.new_activation_deadline IS '新激活截止时间';
COMMENT ON COLUMN gc_activation_extension.issuer_code IS '发行商代码';
COMMENT ON COLUMN gc_activation_extension.outlet_code IS '网点代码';
COMMENT ON COLUMN gc_activation_extension.merchant_code IS '商户代码';
COMMENT ON COLUMN gc_activation_extension.invoice_number IS '发票号';
COMMENT ON COLUMN gc_activation_extension.approval_code IS '审批代码';
COMMENT ON COLUMN gc_activation_extension.notes IS '备注';
COMMENT ON COLUMN gc_activation_extension.batch_number IS '批次号';
COMMENT ON COLUMN gc_activation_extension.source IS '记录来源：API-接口调用，MANUAL-手动操作，SYSTEM-系统自动等';
COMMENT ON COLUMN gc_activation_extension.create_time IS '创建时间';
COMMENT ON COLUMN gc_activation_extension.update_time IS '更新时间';
