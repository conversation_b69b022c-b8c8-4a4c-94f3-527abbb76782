package com.gtech.gvcore.web.external;

import com.gtech.gvcore.service.impl.SystemLoggerServiceImpl;
import com.gtech.gvcore.service.report.base.ReportTaskRegisterServiceImpl;
import com.gtech.gvcore.service.report.impl.support.sales.CancelSalesDataRefresh;
import io.swagger.annotations.Api;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@Slf4j
@RestController
@RequestMapping(value = "/gv/external")
@Api(value = "Om API.", tags = { "Om API" })
public class OmController {


    @Autowired
    private CancelSalesDataRefresh cancelSalesDataRefresh;

    @Autowired
    private SystemLoggerServiceImpl systemLoggerService;

    @Autowired
    private ReportTaskRegisterServiceImpl reportTaskRegisterService;

    @PostMapping("initCancelSalesData")
    public void initCancelSalesData(){
        cancelSalesDataRefresh.init(null);
    }


    @GetMapping("/truncate")
    public void truncate(){
        systemLoggerService.truncate();
    }

    @GetMapping("/performTask")
    public void performTask(){
        reportTaskRegisterService.performTask();
    }
}
