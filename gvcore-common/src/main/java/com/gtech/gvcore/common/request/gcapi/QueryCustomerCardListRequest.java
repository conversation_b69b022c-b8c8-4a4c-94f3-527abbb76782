package com.gtech.gvcore.common.request.gcapi;

import com.gtech.gvcore.common.request.base.PageBean;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

/**
 * Query Customer Card List Request
 * 查询客户礼品卡列表请求
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode(callSuper = false)
@ApiModel(value = "QueryCustomerCardListRequest", description = "Query Customer Card List Request")
public class QueryCustomerCardListRequest extends PageBean {

    @ApiModelProperty(value = "Customer ID", notes = "Customer unique identifier", required = true, example = "CUST123456", position = 1)
    private String customerId;

    @ApiModelProperty(value = "Invoice Number", notes = "InvoiceNumber", example = "1451220131041014", position = 2)
    private String invoiceNumber;

}