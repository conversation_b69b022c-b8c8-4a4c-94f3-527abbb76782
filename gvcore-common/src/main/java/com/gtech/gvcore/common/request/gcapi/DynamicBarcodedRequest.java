package com.gtech.gvcore.common.request.gcapi;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * Dynamic Barcode Request
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@ApiModel(value = "DynamicBarcodedRequest", description = "Dynamic Barcode Request")
public class DynamicBarcodedRequest {

    @ApiModelProperty(value = "Gift Card Number", notes = "16 digit gift card number", required = true, example = "1234567890123456", position = 1)
    private String giftCardNumber;


}