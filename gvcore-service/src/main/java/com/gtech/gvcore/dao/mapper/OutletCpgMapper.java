package com.gtech.gvcore.dao.mapper;

import com.gtech.commons.dao.mapper.GTechBaseMapper;
import com.gtech.gvcore.common.response.outletcpg.OutletCpgResponse;
import com.gtech.gvcore.dao.model.OutletCpg;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * <AUTHOR>
 * @Date 2022/2/21 11:25
 */
@Mapper
public interface OutletCpgMapper extends GTechBaseMapper<OutletCpg> {


    @Select("SELECT " +
            " oc.id, " +
            " oc.outlet_cpg_code, " +
            " oc.outlet_code, " +
            " oc.outlet_cpg_code, " +
            " CASE " +
            "   WHEN oc.cpg_type = 'voucher' THEN c.cpg_name " +
            "   ELSE g.cpg_name " +
            " END AS cpgName, " +
            " CASE " +
            "   WHEN oc.cpg_type = 'voucher' THEN c.cpg_code " +
            "   ELSE g.cpg_code " +
            " END AS cpgCode, " +


            " CASE " +
            "   WHEN oc.cpg_type = 'voucher' THEN c.denomination " +
            "   ELSE g.denomination " +
            " END AS denomination, " +
            " oc.cpg_type, " +
            " c.cpg_type_code, " +
            " c.disable_generation, " +
            " a.mop_code, " +
            " oc.`status`, " +
            " oc.create_user, " +
            " oc.create_time, " +
            " oc.update_user, " +
            " oc.update_time  " +
            "FROM " +
            " gv_outlet_cpg oc " +
            " LEFT JOIN gv_cpg c ON oc.cpg_code = c.cpg_code" +
            " LEFT JOIN gc_cpg g ON oc.cpg_code = g.cpg_code" +
            " LEFT JOIN gv_article_mop a ON a.article_mop_code = c.article_mop_code" +
            " WHERE oc.outlet_code = #{outletCode} ")
    List<OutletCpgResponse> queryOutletCpgListByOutlet(String outletCode);



    @Select("<script>" +
            "SELECT " +
            " oc.id, " +
            " oc.outlet_cpg_code, " +
            " oc.outlet_code, " +
            " oc.outlet_cpg_code, " +
            " CASE " +
            "   WHEN oc.cpg_type = 'voucher' THEN c.cpg_name " +
            "   ELSE g.cpg_name " +
            " END AS cpgName, " +
            " CASE " +
            "   WHEN oc.cpg_type = 'voucher' THEN c.cpg_code " +
            "   ELSE g.cpg_code " +
            " END AS cpgCode, " +


            " CASE " +
            "   WHEN oc.cpg_type = 'voucher' THEN c.denomination " +
            "   ELSE g.denomination " +
            " END AS denomination, " +

            " c.disable_generation, " +
            " c.cpg_type_code, " +
            " c.denomination, " +
            " a.mop_code, " +
            " oc.`status`, " +
            " oc.cpg_type, " +
            " oc.create_user, " +
            " oc.create_time, " +
            " oc.update_user, " +
            " oc.update_time  " +
            "FROM " +
            " gv_outlet_cpg oc " +
            " LEFT JOIN gv_cpg c ON oc.cpg_code = c.cpg_code" +
            " LEFT JOIN gc_cpg g ON oc.cpg_code = g.cpg_code" +
            " LEFT JOIN gv_article_mop a ON a.article_mop_code = c.article_mop_code" +
            " WHERE oc.outlet_code in " +
            "<foreach collection=\"outletCodeList\" item=\"outlet\" index=\"index\" open=\"(\" close=\")\" separator=\",\">"+
            "       #{outlet} " +
            "      </foreach> " +
            " </script>")
    List<OutletCpgResponse> queryOutletCpgListByOutletList(@Param("outletCodeList") List<String> outletCodeList);
}
