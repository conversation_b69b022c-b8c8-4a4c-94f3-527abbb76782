package com.gtech.gvcore.common.request.gcapi;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

/**
 * New Batch Close Request
 * 新批次关闭请求
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@ApiModel(value = "NewBatchCloseRequest", description = "New Batch Close Request")
public class NewBatchCloseRequest {

    @ApiModelProperty(value = "Batch Number", notes = "Current batch number to close", required = true, example = "BATCH001", position = 2)
    private String batchNumber;

    @ApiModelProperty(value = "Operator ID", notes = "ID of the operator closing the batch", required = true, example = "OP001", position = 4)
    private Date settlementDate;

}