package com.gtech.gvcore.giftcard.masterdata.gcpg.service;

import com.alibaba.excel.util.StringUtils;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.gtech.commons.exception.GTechBaseException;
import com.gtech.commons.page.PageData;
import com.gtech.commons.result.PageResult;
import com.gtech.commons.utils.BeanCopyUtils;
import com.gtech.commons.utils.ConvertUtils;
import com.gtech.gvcore.cache.MasterDataCache;
import com.gtech.gvcore.common.enums.ResultErrorCodeEnum;
import com.gtech.gvcore.dao.model.ProductCategory;
import com.gtech.gvcore.giftcard.masterdata.gcpg.dao.GcCpgMapper;
import com.gtech.gvcore.giftcard.masterdata.gcpg.request.CreateGcCpgRequest;
import com.gtech.gvcore.giftcard.masterdata.gcpg.dto.GcCpgDTO;
import com.gtech.gvcore.giftcard.masterdata.gcpg.entity.GcCpg;
import com.gtech.gvcore.giftcard.masterdata.gcpg.request.QueryCpgListRequest;
import com.gtech.gvcore.giftcard.masterdata.gcpg.request.UpdateGcCpgRequest;
import com.gtech.gvcore.giftcard.util.PeriodCalculator;
import com.gtech.gvcore.helper.GvCodeHelper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.rocketmq.common.filter.impl.Op;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import tk.mybatis.mapper.weekend.Weekend;
import tk.mybatis.mapper.weekend.WeekendCriteria;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

@Slf4j
@Service
@RequiredArgsConstructor
public class GcCpgService {

    private final GcCpgMapper gcCpgMapper;

    private final GvCodeHelper codeHelper;

    private final MasterDataCache masterDataCache;

    @Transactional
    public String createCpg(CreateGcCpgRequest request) {
        Weekend<GcCpg> weekend = Weekend.of(GcCpg.class);
        weekend.weekendCriteria()
                .andEqualTo(GcCpg::getCpgName, request.getCpgName());
        if (gcCpgMapper.selectCountByCondition(weekend) > 0) {
            throw new  GTechBaseException(ResultErrorCodeEnum.DATA_ALREADY_EXISTS.code(),ResultErrorCodeEnum.DATA_ALREADY_EXISTS.desc());
        }

        GcCpg gcCpg = new GcCpg();
        gcCpg.setCpgCode(codeHelper.generateGcCpgCode())
                .setCpgName(request.getCpgName())
                .setIssuerCode(request.getIssuerCode())
            .setAutomaticActivate(request.getAutomaticActivate())
            .setActivationPeriod(PeriodCalculator.formatPeriod(
                request.getActivationPeriodYears(),
                request.getActivationPeriodMonths(),
                request.getActivationPeriodDays(),
                request.getActivationPeriodHours()
            ))
            .setActivationGracePeriod(PeriodCalculator.formatPeriod(
                request.getActivationGracePeriodYears(),
                request.getActivationGracePeriodMonths(),
                request.getActivationGracePeriodDays(),
                request.getActivationGracePeriodHours()
            ))
            .setEffectivePeriod(PeriodCalculator.formatPeriod(
                request.getEffectivePeriodYears(),
                request.getEffectivePeriodMonths(),
                request.getEffectivePeriodDays(),
                request.getEffectivePeriodHours()
            ))
            .setCurrency(request.getCurrency())
            .setDenomination(request.getDenomination())
            .setArticleMopCode(request.getArticleMopCode())
            .setWalletHost(request.getWalletHost())
            .setConceptIdentifier(request.getConceptIdentifier())
            .setProductBrand(request.getProductBrand())
            .setCoverFrontUrl(request.getCoverFrontUrl())
            .setCoverBackUrl(request.getCoverBackUrl())
            .setStatus("1") // 默认启用
            .setCreateUser(request.getCreateUser())
            .setCreateTime(new java.util.Date());

        gcCpgMapper.insertSelective(gcCpg);

        return gcCpg.getCpgCode();
    }

    public GcCpgDTO getCpg(String cpgCode) {
        GcCpg gcCpg = gcCpgMapper.selectOne(new GcCpg().setCpgCode(cpgCode));
        if (gcCpg == null) {
            return null;
        }
        
        GcCpgDTO dto = new GcCpgDTO();
        BeanUtils.copyProperties(gcCpg, dto);

        parsePeriodTime(dto);
        return dto;
    }

    public GcCpgDTO getCpgByName(String cpgName) {
        GcCpg cpg = new GcCpg();
        cpg.setCpgName(cpgName);
        GcCpg gcCpg = gcCpgMapper.selectOne(cpg);
        if (gcCpg == null) {
            return null;
        }

        GcCpgDTO dto = new GcCpgDTO();
        BeanUtils.copyProperties(gcCpg, dto);

        parsePeriodTime(dto);
        return dto;
    }

    private GcCpgDTO parsePeriodTime(GcCpgDTO dto) {
        return dto.setActivationPeriodYears(PeriodCalculator.parsePeriod(dto.getActivationPeriod()).getYears())
                .setActivationPeriodMonths(PeriodCalculator.parsePeriod(dto.getActivationPeriod()).getMonths())
                .setActivationPeriodDays(PeriodCalculator.parsePeriod(dto.getActivationPeriod()).getDays())
                .setActivationPeriodHours(PeriodCalculator.parsePeriod(dto.getActivationPeriod()).getHours())

                .setActivationGracePeriodYears(PeriodCalculator.parsePeriod(dto.getActivationGracePeriod()).getYears())
                .setActivationGracePeriodMonths(PeriodCalculator.parsePeriod(dto.getActivationGracePeriod()).getMonths())
                .setActivationGracePeriodDays(PeriodCalculator.parsePeriod(dto.getActivationGracePeriod()).getDays())
                .setActivationGracePeriodHours(PeriodCalculator.parsePeriod(dto.getActivationGracePeriod()).getHours())

                .setEffectivePeriodYears(PeriodCalculator.parsePeriod(dto.getEffectivePeriod()).getYears())
                .setEffectivePeriodMonths(PeriodCalculator.parsePeriod(dto.getEffectivePeriod()).getMonths())
                .setEffectivePeriodDays(PeriodCalculator.parsePeriod(dto.getEffectivePeriod()).getDays())
                .setEffectivePeriodHours(PeriodCalculator.parsePeriod(dto.getEffectivePeriod()).getHours());
    }

    @Transactional
    public void updateStatus(Long id, String status) {
        GcCpg gcCpg = gcCpgMapper.selectByPrimaryKey(id);
        if (gcCpg == null) {
            throw new RuntimeException("卡项目组不存在");
        }

        gcCpg.setStatus(status)
            .setUpdateTime(new Date());
        gcCpgMapper.updateByPrimaryKeySelective(gcCpg);

        masterDataCache.updateGcCpgCache(gcCpg);

    }

    public PageData<GcCpgDTO> select(QueryCpgListRequest request) {
        PageHelper.startPage(request.getPageNum(), request.getPageSize());
        
        // 构建查询条件
        Weekend<GcCpg> weekend = Weekend.of(GcCpg.class);
        WeekendCriteria<GcCpg, Object> criteria = weekend.weekendCriteria()
                .andEqualTo(GcCpg::getIssuerCode, request.getIssuerCode());

        if (StringUtils.isNotBlank(request.getCpgName())) {
            criteria.andLike(GcCpg::getCpgName, "%" + request.getCpgName() + "%");
        }
        weekend.orderBy("id").desc();

        // 执行查询
        List<GcCpg> gcCpgs = gcCpgMapper.selectByCondition(weekend);
        PageInfo<GcCpg> pageInfo = new PageInfo<>(gcCpgs);
        
        // 转换为DTO
        List<GcCpgDTO> dtos = gcCpgs.stream().map(this::toDto).map(this::parsePeriodTime).collect(Collectors.toList());
        
        // 构建分页数据
        PageData<GcCpgDTO> pageData = new PageData<>();
        pageData.setList(dtos);
        pageData.setTotal(pageInfo.getTotal());
        return pageData;
    }
    
    /**
     * 实体转DTO
     */
    private GcCpgDTO toDto(GcCpg entity) {
        GcCpgDTO dto = new GcCpgDTO();
        BeanUtils.copyProperties(entity, dto);
        return dto;
    }
    
    /**
     * 计算CPG的生效日期
     * 使用PeriodCalculator工具类处理YYMMDDHH格式的时间
     *
     * @param cpgCode CPG编码
     * @return 生效日期，如果CPG不存在或无效则返回null
     */
    public Date calculateEffectiveDate(String cpgCode) {
        GcCpgDTO cpg = getCpg(cpgCode);
        if (cpg == null || cpg.getEffectivePeriod() == null) {
            return null;
        }
        
        return PeriodCalculator.calculateDate(cpg.getEffectivePeriod());
    }
    
    /**
     * 计算CPG的激活截止日期
     * 使用PeriodCalculator工具类处理YYMMDDHH格式的时间
     *
     * @param cpgCode CPG编码
     * @return 激活截止日期，如果CPG不存在或无效则返回null
     */
    public Date calculateActivationDeadline(String cpgCode) {
        GcCpgDTO cpg = getCpg(cpgCode);
        if (cpg == null || cpg.getActivationPeriod() == null) {
            return null;
        }
        
        return PeriodCalculator.calculateDate(cpg.getActivationPeriod());
    }
    
    /**
     * 计算CPG的激活宽限期
     * 使用PeriodCalculator工具类处理YYMMDDHH格式的时间
     *
     * @param cpgCode CPG编码
     * @return 激活宽限期日期，如果CPG不存在或无效则返回null
     */
    public Date calculateActivationGraceDate(String cpgCode) {
        GcCpgDTO cpg = getCpg(cpgCode);
        if (cpg == null || cpg.getActivationGracePeriod() == null) {
            return null;
        }
        
        // 先计算激活截止日期，然后再加上宽限期
        Date activationDeadline = calculateActivationDeadline(cpgCode);
        if (activationDeadline == null) {
            return PeriodCalculator.calculateDate(cpg.getActivationGracePeriod());
        }
        
        return PeriodCalculator.calculateDate(cpg.getActivationGracePeriod(), activationDeadline);
    }

    public Map<String, GcCpg> queryCpgMapByCpgCodeList (List<String> cpgCodeList){

        if (CollectionUtils.isEmpty(cpgCodeList)) {
            return Collections.emptyMap();
        }
        Weekend<GcCpg> gcCpgWeekend = Weekend.of(GcCpg.class);
        gcCpgWeekend.weekendCriteria().andIn(GcCpg::getCpgCode, cpgCodeList);

        List<GcCpg> cpgs = gcCpgMapper.selectByCondition(gcCpgWeekend);
        return cpgs.stream().collect(Collectors.toMap(GcCpg::getCpgCode, Function.identity()));
    }


    public GcCpgDTO updateCpg(UpdateGcCpgRequest request) {
        //判断名称是否重复，如果重复则抛出异常
        GcCpgDTO cpg = getCpg(request.getCpgCode());

        if (StringUtils.isNotBlank(request.getCpgName())){
            Weekend<GcCpg> gcCpgWeekend = Weekend.of(GcCpg.class);
            gcCpgWeekend.weekendCriteria().andEqualTo(GcCpg::getCpgName, request.getCpgName());
            if (gcCpgMapper.selectCountByCondition(gcCpgWeekend) > 0 && !cpg.getCpgName().equals(request.getCpgName())) {
                throw new GTechBaseException(ResultErrorCodeEnum.DATA_ALREADY_EXISTS.code(), ResultErrorCodeEnum.DATA_ALREADY_EXISTS.desc());
            }
        }


        GcCpg gcCpg = BeanCopyUtils.jsonCopyBean(request, GcCpg.class);

        gcCpg.setActivationPeriod(PeriodCalculator.formatPeriod(
                        Optional.ofNullable(request.getActivationPeriodYears()).orElse(cpg.getActivationPeriodYears()),
                        Optional.ofNullable(request.getActivationPeriodMonths()).orElse(cpg.getActivationPeriodMonths()),
                        Optional.ofNullable(request.getActivationPeriodDays()).orElse(cpg.getActivationPeriodDays()),
                        Optional.ofNullable(request.getActivationPeriodHours()).orElse(cpg.getActivationPeriodHours())
                ))
                .setActivationGracePeriod(PeriodCalculator.formatPeriod(
                        Optional.ofNullable(request.getActivationGracePeriodYears()).orElse(cpg.getActivationGracePeriodYears()),
                        Optional.ofNullable(request.getActivationGracePeriodMonths()).orElse(cpg.getActivationGracePeriodMonths()),
                        Optional.ofNullable(request.getActivationGracePeriodDays()).orElse(cpg.getActivationGracePeriodDays()),
                        Optional.ofNullable(request.getActivationGracePeriodHours()).orElse(cpg.getActivationGracePeriodHours())
                ))
                .setEffectivePeriod(PeriodCalculator.formatPeriod(
                        Optional.ofNullable(request.getEffectivePeriodYears()).orElse(cpg.getEffectivePeriodYears()),
                        Optional.ofNullable(request.getEffectivePeriodMonths()).orElse(cpg.getEffectivePeriodMonths()),
                        Optional.ofNullable(request.getEffectivePeriodDays()).orElse(cpg.getEffectivePeriodDays()),
                        Optional.ofNullable(request.getEffectivePeriodHours()).orElse(cpg.getEffectivePeriodHours())
                ));

        Weekend<GcCpg> weekend = Weekend.of(GcCpg.class);
        weekend.weekendCriteria().andEqualTo(GcCpg::getCpgCode, request.getCpgCode());
        int i = gcCpgMapper.updateByConditionSelective(gcCpg, weekend);
        if (i == 0) {
            throw new GTechBaseException(ResultErrorCodeEnum.FAILED_TO_UPDATE_DATA.code(), ResultErrorCodeEnum.FAILED_TO_UPDATE_DATA.desc());
        }
        masterDataCache.updateGcCpgCache(gcCpg);
        return getCpg(request.getCpgCode());
    }
}
