package com.gtech.gvcore.giftcard.domain.model;

import com.gtech.commons.utils.StringUtil;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.Date;
import java.util.UUID;

/**
 * 礼品卡销售记录
 */
@Data
public class GcSalesRecord {

    private final String salesCode;
    private final String cardNumber;
    private final String cpgCode;
    private final String issuerCode;
    private final String merchantCode;
    private final String outletCode;
    private final String customerCode;
    private final String invoiceNumber;
    private final String approvalCode;
    private final String purchaseOrderNumber;
    private final Date salesTime;
    private final BigDecimal denomination;
    private final String notes;
    private final String batchNumber;

    /**
     * 创建销售记录
     */
    public GcSalesRecord(
        String salesCode,
        String cardNumber,
        String cpgCode,
        String issuerCode,
        String merchantCode,
        String merchantOutletCode,
        String customerCode,
        String invoiceNumber,
        String approvalCode,
        String purchaseOrderNumber,
        BigDecimal denomination,
        Date salesTime,
        String notes,
        String batchNumber
    ) {
        this.salesCode = salesCode;
        this.cardNumber = cardNumber;
        this.cpgCode = cpgCode;
        this.issuerCode = issuerCode;
        this.merchantCode = merchantCode;
        this.outletCode = merchantOutletCode;
        this.customerCode = customerCode;
        this.invoiceNumber = invoiceNumber;
        this.approvalCode = approvalCode;
        this.purchaseOrderNumber = purchaseOrderNumber;
        this.salesTime = salesTime;
        this.denomination = denomination;
        this.notes = notes;
        this.batchNumber = batchNumber;
    }

    /**
     * 生成销售编码
     */
    private String generateSalesCode() {
        return "S" + System.currentTimeMillis() + UUID.randomUUID().toString().substring(0, 8).toUpperCase();
    }
} 