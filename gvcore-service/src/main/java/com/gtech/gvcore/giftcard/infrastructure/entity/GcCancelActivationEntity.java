package com.gtech.gvcore.giftcard.infrastructure.entity;

import lombok.Data;

import javax.persistence.*;
import java.util.Date;

/**
 * 礼品卡激活记录实体
 */
@Data
@Entity
@Table(name = "gc_cancel_activation")
public class GcCancelActivationEntity {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @Column(name = "cancel_code", nullable = false)
    private String cancelCode;

    @Column(name = "card_number", nullable = false)
    private String cardNumber;

    @Column(name = "owner_customer", nullable = false, unique = true)
    private String ownerCustomer;

    @Column(name = "issuer_code")
    private String issuer_code;

    @Column(name = "cpg_code")
    private String cpgCode;

    @Column(name = "outlet_code")
    private String outletCode;

    @Column(name = "invoice_number")
    private String invoiceNumber;

    @Column(name = "approval_code")
    private String approvalCode;

    @Column(name = "notes")
    private String notes;

    @Column(name = "cancel_time", nullable = false)
    private Date cancelTime;

    @Column(name = "cancel_reason", nullable = false)
    private Date cancelReason;

    @Column(name = "create_time", nullable = false)
    private Date createTime;

    @Column(name = "update_time", nullable = false)
    private Date updateTime;


} 