package com.gtech.gvcore.service.report.impl.bean;

import com.alibaba.excel.annotation.ExcelProperty;
import com.gtech.gvcore.service.report.export.file.ExportExcelNumberConverter;
import com.gtech.gvcore.service.report.export.snapshoot.annotation.ReportAmountValue;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

/**
 * @Author: gao.yuhua
 * @Date: 2022/4/21 14:12
 * @Description:
 */
@Getter
@Setter
@Accessors(chain = true)
public class GcSalesSummaryBean {

    @ExcelProperty(value = "Transaction Date")
    private String date;

    @ExcelProperty(value = "Merchant Name")
    private String merchantName;
    @ExcelProperty(value = "Outlet Name")
    private String merchantOutletName;
    @ExcelProperty(value = "Invoice Number")
    private String invoiceNumber;
    @ExcelProperty(value = "Gift Card Program Group")
    private String cpgCode;
    @ExcelProperty(value = "Sales Count", converter = ExportExcelNumberConverter.class)
    private String salesCount;
    @ReportAmountValue
    @ExcelProperty(value = "Sales Amount", converter = ExportExcelNumberConverter.class)
    private String salesAmount;

    @ExcelProperty(value = "SBU Company Name")
    private String subCompanyName;

    @ExcelProperty(value = "Notes")
    private String notes;
}
