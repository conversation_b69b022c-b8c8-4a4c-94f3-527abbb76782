package com.gtech.gvcore.giftcard.domain.repository;

import com.gtech.commons.utils.BeanCopyUtils;
import com.gtech.gvcore.giftcard.domain.model.GcHolder;
import com.gtech.gvcore.giftcard.infrastructure.entity.GcHolderEntity;
import com.gtech.gvcore.giftcard.infrastructure.mapper.GcHolderMapper;
import com.gtech.gvcore.giftcard.infrastructure.mapper.GcSalesMapper;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

import java.util.Collections;
import java.util.List;

@Repository
public class GiftCardHolderRepository {

    @Autowired
    private GcHolderMapper gcHolderMapper;


    public GcHolder getByGiftCardNumber(String giftCardNumber) {
        GcHolderEntity gcHolderEntity = new GcHolderEntity();
        gcHolderEntity.setCardNumber(giftCardNumber);
        GcHolderEntity entity = gcHolderMapper.selectOne(gcHolderEntity);

        return entity == null ? null : BeanCopyUtils.jsonCopyBean(entity,GcHolder.class);
    }


    public List<GcHolder> getHolderGiftCardList(String holder) {
        GcHolderEntity gcHolderEntity = new GcHolderEntity();
        gcHolderEntity.setCardNumber(holder);
        List<GcHolderEntity> entity = gcHolderMapper.selectByCondition(gcHolderEntity);

        return CollectionUtils.isEmpty(entity) ? Collections.emptyList() : BeanCopyUtils.jsonCopyList(entity,GcHolder.class);
    }


    public int bindCardholder(GcHolder holder) {
        GcHolderEntity entity = BeanCopyUtils.jsonCopyBean(holder,GcHolderEntity.class);
        return gcHolderMapper.insert(entity);
    }
}
