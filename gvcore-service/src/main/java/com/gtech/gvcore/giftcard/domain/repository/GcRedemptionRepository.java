package com.gtech.gvcore.giftcard.domain.repository;

import cn.jiguang.common.Week;
import com.gtech.commons.utils.BeanCopyUtils;
import com.gtech.gvcore.giftcard.domain.model.GcRedemptionRecord;
import com.gtech.gvcore.giftcard.infrastructure.entity.GcRedemptionEntity;
import com.gtech.gvcore.giftcard.infrastructure.mapper.GcRedemptionMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;
import tk.mybatis.mapper.weekend.Weekend;

import java.util.List;

/**
 * 礼品卡使用记录仓储实现
 */
@Repository
public class GcRedemptionRepository {

    @Autowired
    private GcRedemptionMapper gcRedemptionMapper;


    /**
     * 保存使用记录
     */
    @Transactional
    public GcRedemptionRecord save(GcRedemptionRecord record) {
        GcRedemptionEntity entity = BeanCopyUtils.jsonCopyBean(record, GcRedemptionEntity.class);

        if (entity.getId() == null) {
            gcRedemptionMapper.insert(entity);
        } else {
            gcRedemptionMapper.updateByPrimaryKey(entity);
        }

        return record;
    }


    @Transactional
    public int insertList(List<GcRedemptionRecord> record) {
        List<GcRedemptionEntity> entity = BeanCopyUtils.jsonCopyList(record, GcRedemptionEntity.class);
        return gcRedemptionMapper.insertList(entity);
    }


    /**
     * 根据卡号查询使用记录
     */
    public List<GcRedemptionRecord> findByCardNumber(String cardNumber) {
        List<GcRedemptionEntity> entities = gcRedemptionMapper.selectByCardNumber(cardNumber);
        return BeanCopyUtils.jsonCopyList(entities, GcRedemptionRecord.class);
    }

    /**
     * 根据卡号查询使用记录
     */
    public GcRedemptionRecord getRedemption(String cardNumber, String invoiceNumber) {
        GcRedemptionEntity gcRedemptionEntity = new GcRedemptionEntity();
        gcRedemptionEntity.setInvoiceNumber(invoiceNumber);
        gcRedemptionEntity.setCardNumber(cardNumber);
        GcRedemptionEntity entities = gcRedemptionMapper.selectOne(gcRedemptionEntity);
        return BeanCopyUtils.jsonCopyBean(entities, GcRedemptionRecord.class);
    }


    /**
     * 根据发票号查询使用记录
     */
    public List<GcRedemptionRecord> findByInvoiceNumber(String invoiceNumber) {
        List<GcRedemptionEntity> entities = gcRedemptionMapper.selectByInvoiceNumber(invoiceNumber);
        return BeanCopyUtils.jsonCopyList(entities, GcRedemptionRecord.class);
    }

    /**
     * 根据POS批次号查询使用记录
     */
    public List<GcRedemptionRecord> findByPosBatchNumber(String posBatchNumber) {
        List<GcRedemptionEntity> entities = gcRedemptionMapper.selectByPosBatchNumber(posBatchNumber);
        return BeanCopyUtils.jsonCopyList(entities, GcRedemptionRecord.class);
    }

    public List<GcRedemptionRecord> findByBatchNumber(String batchNumberInteger) {
        Weekend<GcRedemptionEntity> gcRedemptionEntityWeekend = Weekend.of(GcRedemptionEntity.class);
        gcRedemptionEntityWeekend.weekendCriteria()
                .andEqualTo(GcRedemptionEntity::getBatchNumber, batchNumberInteger);
        List<GcRedemptionEntity> entities = gcRedemptionMapper.selectByCondition(gcRedemptionEntityWeekend);
        return BeanCopyUtils.jsonCopyList(entities, GcRedemptionRecord.class);
    }
}